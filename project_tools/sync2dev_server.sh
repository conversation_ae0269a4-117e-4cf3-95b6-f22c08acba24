#!/bin/bash
# author : liuxu
# date   : 2018-09-06

SCRIPT_DIR="$(dirname $(git rev-parse --git-dir))/"
SCRIPT_ENV="$SCRIPT_DIR"/project_tools/script_environment.sh

TARGET_USER=
TARGET_DIR=
TARGET_IP="*************"

if [ "$1" != "" ]; then
    TARGET_IP="$1"
fi

if [ -f "$SCRIPT_ENV" ]; then
    source "$SCRIPT_ENV"
else
    TARGET_USER="$1"
fi

if [ "$TARGET_USER" == "" ]; then
    echo "* need to specify a user"
    exit 1
fi

if [ "$TARGET_DIR" == "" ]; then
    TARGET_DIR=/home/<USER>/kaiba_sc_main
fi

rsync -azP --delete \
    --exclude "target/" \
    --exclude "var/" \
    --exclude ".git/" \
    --exclude ".idea/" \
    "$SCRIPT_DIR" "$TARGET_USER@$TARGET_IP:$TARGET_DIR"

echo "send $SCRIPT_DIR to $TARGET_USER@$TARGET_IP:$TARGET_DIR done. $?"
