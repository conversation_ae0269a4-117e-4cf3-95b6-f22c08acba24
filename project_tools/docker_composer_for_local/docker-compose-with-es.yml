version: '2'

services:

  redis:
    image: redis:4.0-alpine
    container_name: kaiba-redis
    command: redis-server --requirepass Kaiba315Redis
    ports:
      - 6379:6379

  rabbitmq:
    image: rabbitmq:3.7.7-management
    environment:
      - RABBITMQ_DEFAULT_USER=kaiba
      - RABBITMQ_DEFAULT_PASS=kaiba_rabbitmq
    ports:
      - 15672:15672
      - 5672:5672

  nacos:
    image: nacos/nacos-server:latest
    container_name: kaiba-nacos
    environment:
      - PREFER_HOST_MODE=hostname
      - MODE=standalone
    volumes:
      - ./docker_data/nacos:/home/<USER>/data
      - ./nacos_custom.properties:/home/<USER>/init.d/custom.properties
    ports:
      - 8848:8848

  elastic:
    image: registry.cn-hangzhou.aliyuncs.com/kaiba/kaiba_elasticsearch
    container_name: kaiba-elasticsearch
    environment:
      - discovery.type=single-node
      - cluster.name=kaiba-es-local
    ports:
      - 9200:9200
      - 9300:9300
    volumes:
      - ./docker_data/elasticsearch/data:/usr/share/elasticsearch/data
      - ./docker_data/elasticsearch/logs:/usr/share/elasticsearch/logs
