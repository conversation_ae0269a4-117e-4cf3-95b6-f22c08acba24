
kb.circle.main_page.search_hint=??

kb.accounting.poundage.in=0.006
kb.accounting.poundage.out=0
kb.accounting.user.withdraw.limit.message=???5?????
kb.accounting.user.withdraw.limit=10
kb.accounting.issue.service.charge=0

#############################################

kb.mongo.uri=**************************************************************
kb.mongo.datav.uri=**************************************************************

kb.redis.host=127.0.0.1
kb.redis.port=6379
kb.redis.password=Kaiba315Redis

kb.rabbitmq.instance-id=****************
kb.rabbitmq.host=****************.mq-amqp.cn-hangzhou-a.aliyuncs.com
kb.rabbitmq.port=5672
kb.rabbitmq.vhost=kaiba_test
kb.rabbitmq.username=LTAI4G4RXtuthaQWMpAhuhhC
kb.rabbitmq.password=******************************

kb.job.admin.address=http://************:18080/xxl-job-admin
kb.job.executor.port=18081
kb.job.executor.log-path=${kaiba.path}/job-logs/
kb.job.executor.log-retention-days=30

kb.host.inner.phpapi=dev.phpapi.kaiba.in
kb.host.inner.mall=dev.mall.kaiba.in
kb.host.page=dev.page.kaiba315.com.cn
kb.host.hd=dev.hd.kaiba315.com.cn
kb.host.open=dev.open.kaiba315.com.cn
kb.host.cms=dev.cmd.kaiba315.com.cn
kb.host.api=dev.api.kaiba315.com.cn
kb.host.static=static.kaiba315.com.cn

#############################################

spring.redis.host=${kb.redis.host}
spring.redis.port=${kb.redis.port}
spring.redis.password=${kb.redis.password}

spring.rabbitmq.host=${kb.rabbitmq.host}
spring.rabbitmq.port=${kb.rabbitmq.port}
spring.rabbitmq.virtual-host=${kb.rabbitmq.vhost}
spring.rabbitmq.username=${kb.rabbitmq.username}
spring.rabbitmq.password=${kb.rabbitmq.password}
