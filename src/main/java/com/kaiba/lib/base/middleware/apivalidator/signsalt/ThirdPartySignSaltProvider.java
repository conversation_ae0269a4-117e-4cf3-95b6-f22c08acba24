package com.kaiba.lib.base.middleware.apivalidator.signsalt;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.constant.KbHttpHeaders;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IThirdPartyAuthService;
import com.kaiba.lib.base.util.ServletRequestUtils;

import java.util.List;
import javax.servlet.http.HttpServletRequest;

/**
 * author: lyux
 * date: 2022-08-18
 */
public class ThirdPartySignSaltProvider implements IKbSignSaltProvider {

    private final IThirdPartyAuthService thirdPartyAuthService;

    public ThirdPartySignSaltProvider(IThirdPartyAuthService thirdPartyAuthService) {
        this.thirdPartyAuthService = thirdPartyAuthService;
    }

    @Override
    public KbSignType getSignType() {
        return KbSignType.THIRD_PARTY;
    }

    @Override
    public List<String> getSalts() {
        if (thirdPartyAuthService == null) {
            throw new KbException(KbCode.NOT_SUPPORT_YET, "no third party access service specified");
        }
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        if (request == null) {
            throw new KbException(KbCode.AUTH_FAIL_KBSIGN_INVALID, "not in a request context");
        }
        String accessId = KbHttpHeaders.KB_AID.getValidHeaderOrNull(request);
        if (accessId == null) {
            accessId = request.getParameter("accessId");
        }
        if (null == accessId) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID, "check kbSign, empty accessId").li();
        }
        return Lists.newArrayList(thirdPartyAuthService.getAccessSaltById(accessId, true).getData());
    }
}

