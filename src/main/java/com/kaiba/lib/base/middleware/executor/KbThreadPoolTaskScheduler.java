package com.kaiba.lib.base.middleware.executor;

import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.Date;

/**
 * author: lyux
 * date: 2020-09-11
 */
public class KbThreadPoolTaskScheduler implements IKbTaskSchedulerExecutor {

    private final ThreadPoolTaskScheduler threadPoolTaskScheduler;

    public KbThreadPoolTaskScheduler(ThreadPoolTaskScheduler threadPoolTaskScheduler) {
        this.threadPoolTaskScheduler = threadPoolTaskScheduler;
    }

    @Override
    public void execute(Runnable task) {
        threadPoolTaskScheduler.execute(task);
    }

    @Override
    public void schedule(Runnable task, Long runAt) {
        threadPoolTaskScheduler.schedule(task, new Date(runAt));
    }

}
