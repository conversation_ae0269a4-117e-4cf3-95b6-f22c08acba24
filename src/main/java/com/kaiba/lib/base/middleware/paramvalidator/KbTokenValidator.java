package com.kaiba.lib.base.middleware.paramvalidator;

import com.kaiba.lib.base.annotation.apiparam.KbToken;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * author: lyux
 * date: 18-7-25
 */
public class KbTokenValidator implements ConstraintValidator<KbToken, String>, IKbValidator {


    @Override
    public void initialize(KbToken constraintAnnotation) {

    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        return true;
    }
}
