package com.kaiba.lib.base.middleware.jwt;

import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.constant.Restful;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * author: lyux
 * date: 2020-09-02
 *
 * 开吧签名统一采用 ES256 算法, 为对称加密算法. 采用私钥加密, 公钥解密.
 * 公钥可以生成多本, 可发送给第三方做为校验依据.
 *
 * 生成私钥:
 * openssl ecparam -genkey -name prime256v1 -out jwt-private-key.pem
 *
 * 转换私钥:
 * openssl pkcs8 -topk8 -inform PEM -outform DER -in jwt-private-key.pem -nocrypt > jwt_private_pkcs8
 *
 * 生成公钥:
 * openssl ec -in jwt-private-key.pem -pubout -out jwt-public-key.pem
 */
@Slf4j
public class KbJWTHandler {

    private final PrivateKey privateKey;
    private final PublicKey publicKey;
    private final Map<String, KbEndpoint> endpointMap;
    private JwtParser parser;

    /** 验证 jwt 有效性只需要公钥 */
    public static KbJWTHandler createAsVerifier(String resourcePublicKeyPath) {
        try {
            return new KbJWTHandler(null, KbJWTKeyProvider.getEC256PublicKeyByResource(resourcePublicKeyPath));
        } catch (Exception e) {
            throw new KbException(KbCode.ILLEGAL_STATE, "create key fail", e);
        }
    }

    /** 签发 jwt 需要私钥 */
    public static KbJWTHandler createAsHandler(String resourcePrivatePkcsPath, String resourcePublicKeyPath) {
        try {
            return new KbJWTHandler(
                    KbJWTKeyProvider.getEC256PrivateKeyByResource(resourcePrivatePkcsPath),
                    KbJWTKeyProvider.getEC256PublicKeyByResource(resourcePublicKeyPath));
        } catch (Exception e) {
            throw new KbException(KbCode.ILLEGAL_STATE, "create key fail", e);
        }
    }

    private KbJWTHandler(PrivateKey privateKey, PublicKey publicKey) {
        this.privateKey = privateKey;
        this.publicKey = publicKey;
        if (publicKey != null) {
            this.parser = Jwts.parserBuilder()
                    .setAllowedClockSkewSeconds(10)
                    .setSigningKey(publicKey)
                    .build();
        }
        KbEndpoint[] endpoints = KbEndpoint.values();
        this.endpointMap = new HashMap<>(endpoints.length);
        for (KbEndpoint endpoint : endpoints) {
            endpointMap.put(Integer.toString(endpoint.getValue()), endpoint);
        }
    }

    public String createJWT(String accessId, KbEndpoint endpoint, Duration expire) {
        if (privateKey == null) {
            throw new KbException(KbCode.ILLEGAL_STATE, "private key is required to create jwt");
        }
        JwtBuilder builder = Jwts.builder()
                .setId(accessId)
                .setSubject(Integer.toString(endpoint.getValue()))
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expire.toMillis()));
        return builder.signWith(privateKey, SignatureAlgorithm.ES256).compact();
    }

    public Claims parseJWT(String jwt) {
        Jws<Claims> jws = parser.parseClaimsJws(jwt);
        return jws.getBody();
    }

    public KbJWTPayload parseJWTOrThrow(String jwt) {
        if (publicKey == null) {
            throw new KbException(KbCode.ILLEGAL_STATE, "jwt parser needs public key");
        }
        Jws<Claims> jws;
        try {
            jws = parser.parseClaimsJws(jwt);
        } catch (ExpiredJwtException expireE) {
            log.debug("token expired. " + expireE.getMessage());
            throw new KbException(KbCode.AUTH_FAIL_TOKEN_EXPIRED).setResponseCode(Restful.RSP_LOGIN_EXPIRE).li();
        } catch (MalformedJwtException malformedE) {
            log.info("token invalid. " + malformedE.getMessage() + ": " + jwt);
            throw new KbException(KbCode.AUTH_FAIL_TOKEN_INVALID).li();
        } catch (InvalidClaimException claimE) {
            log.info("token invalid. " + claimE.getMessage() + ": " + claimE.getClaimName() + " -> " + claimE.getClaimValue());
            throw new KbException(KbCode.AUTH_FAIL_TOKEN_INVALID).li();
        } catch (Exception e) {
            log.info("token invalid. " + e.getMessage() + ": " + jwt);
            throw new KbException(KbCode.AUTH_FAIL_TOKEN_INVALID, "jwt validation fails - " + e.getClass().getSimpleName()).li();
        }
        Claims claims = jws.getBody();
        String id = claims.getId();
        if (id == null) {
            throw new KbException(KbCode.AUTH_FAIL_TOKEN_INVALID, "no id in token").li();
        }
        String subject = claims.getSubject();
        if (subject == null) {
            throw new KbException(KbCode.AUTH_FAIL_TOKEN_INVALID, "no subject in token").li();
        }
        KbEndpoint endpoint = endpointMap.get(subject);
        if (endpoint == null) {
            throw new KbException(KbCode.AUTH_FAIL_TOKEN_INVALID, "unknown endpoint: " + subject).li();
        }
        Date expireAt = claims.getExpiration();
        if (expireAt == null) {
            throw new KbException(KbCode.AUTH_FAIL_TOKEN_INVALID, "no expire in token").li();
        }
        Date issuedAt = claims.getIssuedAt();
        return new KbJWTPayload(id, endpoint, expireAt.getTime(), issuedAt == null ? 0 : issuedAt.getTime());
    }

}
