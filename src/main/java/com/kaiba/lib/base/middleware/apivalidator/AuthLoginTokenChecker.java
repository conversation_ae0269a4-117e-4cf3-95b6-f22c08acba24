package com.kaiba.lib.base.middleware.apivalidator;

import com.kaiba.lib.base.constant.Restful;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.ServletRequestUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;

/**
 * author: lyux
 * date: 18-7-25
 */
@Slf4j
public class AuthLoginTokenChecker {

    private static final String USER_TOKEN_PREFIX = "kb_user_token_";

    private final ITokenCacheProvider tokenCacheProvider;
    private boolean isSkipCheck = false;

    public AuthLoginTokenChecker(ITokenCacheProvider tokenCacheProvider) {
        this.tokenCacheProvider = tokenCacheProvider;
    }

    public void setIsSkipCheck(boolean isSkipCheck) {
        this.isSkipCheck = isSkipCheck;
    }

    public void checkLogin() {
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        checkLogin(request);
    }

    public void checkLogin(HttpServletRequest request) {
        if (null == request) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "no request available. this method should only be called under http request context")
                    .setLevel(KbException.LEVEL_INFO);
        }
        String userIdStr = request.getParameter("userId");
        if (null == userIdStr) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID,
                    "check login without userId").setLevel(KbException.LEVEL_INFO);
        }
        int userId;
        try {
            userId = Integer.parseInt(userIdStr);
        } catch (NumberFormatException e) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID,
                    "check login with a malformed userId: " + userIdStr).setLevel(KbException.LEVEL_INFO);
        }
        if (userId < 0) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID,
                    "check login without a valid userId: " + userId).setLevel(KbException.LEVEL_INFO);
        }
        String token = request.getParameter("token");
        if (null == token) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID,
                    "check login without token")
                    .setLevel(KbException.LEVEL_INFO);
        } else if (token.length() != 32) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID,
                    "check login with a malformed token: " + userId + " -> " + token)
                    .setLevel(KbException.LEVEL_INFO);
        }
        if (isSkipCheck) {
            log.warn("check login skipped for userId: " + userId + " -> " + token);
            return;
        }

        checkLogin(userId, token);
    }

    public void checkLogin(Integer userId, String token) {
        String cacheKey = USER_TOKEN_PREFIX + userId;
        String cacheToken;
        try {
            cacheToken = tokenCacheProvider.getTokenByKey(cacheKey);
        } catch (Exception e) {
            throw new KbException(KbCode.AUTH_FAIL_TOKEN_CHECK_FAIL,
                    "check login fail: " + userId + " -> " + token, e)
                    .setLevel(KbException.LEVEL_ERROR);
        }

        if (null == cacheToken) {
            throw new KbException(KbCode.AUTH_FAIL_TOKEN_EXPIRED,
                    "check login, token expired: " + userId + " -> " + token)
                    .setResponseCode(Restful.RSP_LOGIN_EXPIRE)
                    .setLevel(KbException.LEVEL_DEBUG);
        }
        if (!cacheToken.equals(token)) {
            throw new KbException(KbCode.AUTH_FAIL_TOKEN_INVALID,
                    "check login, token invalid: " + userId + " -> " + token)
                    .setResponseCode(Restful.RSP_LOGIN_EXPIRE)
                    .setLevel(KbException.LEVEL_INFO);
        }
    }


    public interface ITokenCacheProvider {
        String getTokenByKey(String key);
    }

}
