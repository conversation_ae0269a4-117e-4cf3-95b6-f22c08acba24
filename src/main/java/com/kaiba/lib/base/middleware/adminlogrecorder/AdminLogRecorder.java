package com.kaiba.lib.base.middleware.adminlogrecorder;

import com.kaiba.lib.base.constant.KbHttpHeaders;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.domain.IAttrGetterSetter;
import com.kaiba.lib.base.domain.adminlog.AdminLogCreateModel;
import com.kaiba.lib.base.domain.adminlog.AdminLogModel;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IAdminLogService;
import com.kaiba.lib.base.util.ServletRequestUtils;
import com.kaiba.lib.base.util.StringUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * author: lyux
 * date: 2023-07-25
 */
@Slf4j
public class AdminLogRecorder {

    private final KbModule module;
    private final Map<String, String> units;
    private final String determinedUnit;
    private final IAdminLogService adminLogService;

    public AdminLogRecorder(KbModule module, Map<String, String> units, String determinedUnit, IAdminLogService adminLogService) {
        this.determinedUnit = determinedUnit;
        if (module == null) {
            throw new IllegalArgumentException("module null");
        }
        if (adminLogService == null) {
            throw new IllegalArgumentException("adminLogService null");
        }
        if (units == null || units.size() == 0) {
            this.units = Collections.emptyMap();
        } else {
            this.units = Collections.unmodifiableMap(units);
        }
        this.module = module;
        this.adminLogService = adminLogService;
    }

    public static AdminLogRecorderBuilder builder(IAdminLogService adminLogService) {
        return new AdminLogRecorderBuilder(adminLogService);
    }

    /**
     * 开始添加日志
     */
    public AdminLogBuilder on() {
        return on(determinedUnit);
    }

    /**
     * 为特定功能单元添加一条日志.
     * @param unit 功能单元标识. 应在模块内唯一, 且应在设计模块时就规划好有哪些功能单元标识. 一旦确定就尽量不要再行更改.
     */
    public AdminLogBuilder on(String unit) {
        if (!isValidUnit(unit)) {
            throw new IllegalArgumentException("unit and registered not match");
        }
        AdminLogBuilder builder = new AdminLogBuilder(this);
        builder.model.setModule(module.name());
        builder.model.setUnit(unit);
        return builder;
    }

    public boolean isValidUnit(String unit) {
        if (units.isEmpty() && unit == null) {
            return true;
        } else if (units.containsKey(unit)) {
            return true;
        } else {
            return false;
        }
    }

    public String getUnitDesc(String unit) {
        if (unit == null) {
            return null;
        } else {
            return units.get(unit);
        }
    }

    // ------------------------------------------------------------

    public static class AdminLogRecorderBuilder {

        private KbModule module;
        private Map<String, String> units;
        private String determinedUnit;
        private final IAdminLogService adminLogService;

        public AdminLogRecorderBuilder(IAdminLogService adminLogService) {
            this.adminLogService = adminLogService;
        }

        /**
         * 指定模块.
         */
        public AdminLogRecorderBuilder module(KbModule module) {
            this.module = module;
            return this;
        }

        /**
         * 注册模块内的功能单元.
         * 一旦添加, 则只能向已注册的功能单元中添加日志.
         * @param unit 功能单元标识, 一旦采用建议不要再做修改.
         * @param desc 功能单元简称, 可以随时更改
         */
        public AdminLogRecorderBuilder registerUnit(String unit, String desc) {
            if (units == null) {
                units = new HashMap<>();
            }
            units.put(unit, desc);
            return this;
        }

        /**
         * 指定模块内的功能单元, 作为缺省功能单元标识. 添加日志时将不再接受指定为其他功能单元.
         * @param unit 功能单元标识.
         */
        public AdminLogRecorderBuilder unit(String unit, String desc) {
            this.determinedUnit = unit;
            return registerUnit(unit, desc);
        }

        public AdminLogRecorder create() {
            return new AdminLogRecorder(module, units, determinedUnit, adminLogService);
        }

    }

    // ------------------------------------------------------------

    public static class AdminLogBuilder implements IAttrGetterSetter {

        private final AdminLogRecorder recorder;
        private final AdminLogCreateModel model;

        @Getter
        @Setter
        private Map<String, String> attr;

        public AdminLogBuilder(AdminLogRecorder recorder) {
            this.recorder = recorder;
            this.model = new AdminLogCreateModel();
            this.model.setModule(recorder.module.name());
        }

        /** 指定操作者. 若不指定, 日志存储模块会尝试从当前请求上下文的 header 中获取. */
        public AdminLogBuilder by(Integer userId) {
            model.setUserId(userId);
            return this;
        }

        /** 指定系统作为操作者. 适用于自动调度任务等场景. */
        public AdminLogBuilder bySystem() {
            model.setUserId(KbProperties.ADMIN_USER_ID);
            return this;
        }

        /**
         * 指定操作信息
         * @param act 操作简介, 应尽量使用运营可读懂的中文描述
         */
        public AdminLogBuilder act(String act) {
            model.setAct(act);
            return this;
        }

        /**
         * 指定操作信息
         * @param action 操作标识, 应尽量使用英文及数字的编码
         * @param desc 操作简介, 应尽量使用运营可读懂的中文描述
         */
        public AdminLogBuilder act(String action, String desc) {
            model.setAct(action);
            model.setDesc(desc);
            return this;
        }

        /**
         * 使用预设的操作类型指定操作信息
         */
        public AdminLogBuilder act(AdminLogAction action) {
            return act(action, null);
        }

        /**
         * 使用预设的操作类型指定操作信息
         */
        public AdminLogBuilder act(AdminLogAction action, String desc) {
            model.setAct(action.name());
            if (desc == null) {
                model.setDesc(action.getDescription());
            } else {
                model.setDesc(desc);
            }
            return this;
        }

        /**
         * 指定操作信息
         * @param desc 操作简介, 应尽量使用运营可读懂的中文描述
         */
        public AdminLogBuilder desc(String desc) {
            model.setDesc(desc);
            return this;
        }

        /** 指定关联 ID */
        public AdminLogBuilder ref1(Object refId) {
            model.setRef1(refId.toString());
            return this;
        }

        /** 指定关联 ID */
        public AdminLogBuilder ref2(Object refId) {
            model.setRef2(refId.toString());
            return this;
        }

        /** 指定关联 ID */
        public AdminLogBuilder ref3(Object refId) {
            model.setRef3(refId.toString());
            return this;
        }

        /** 指定操作前的数据快照 */
        public AdminLogBuilder snapshot(Object snapshot) {
            model.setSnapshot(snapshot);
            return this;
        }

        /** 指定操作前的数据快照, 并指明该信息为敏感信息. */
        public AdminLogBuilder sensitiveSnapshot(Object snapshot) {
            model.setSnapshot(snapshot);
            model.setSensitive(true);
            return this;
        }

        /** 指定其他记录数据 */
        public AdminLogBuilder info(Object info) {
            model.setInfo(info);
            return this;
        }

        /** 提交日志 */
        public void add() {
            if (model.getModule() == null) {
                log.error("add admin log fail, module missing: " + model);
                return;
            }
            if (model.getAct() == null && model.getDesc() == null) {
                log.error("add admin log fail, act and desc missing: " + model);
                return;
            }
            if (model.getUserId() == null) {
                HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
                if (request == null) {
                    // 没有 HTTP 上下文, 标记为系统内部调用
                    model.setUserId(KbProperties.SYSTEM_USER_ID);
                } else {
                    model.setUserId(KbHttpHeaders.KB_USER_ID.getValidIntegerHeaderOrNull(request));
                    if (model.getUserId() == null) {
                        // 若由系统触发操作, 应该显式调用 bySystem()
                        log.warn("add admin log, userId missing: " + model);
                    }
                }
            }
            model.setAttr(attr);
            recorder.adminLogService.addLog(model);
        }

        /** 提交日志 */
        public KbEntity<AdminLogModel> addOrThrow() {
            if (model.getModule() == null) {
                throw new IllegalArgumentException("module is required");
            }
            if (model.getAct() == null && model.getDesc() == null) {
                throw new IllegalArgumentException("action or desc is required");
            }
            HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
            if (request != null) {
                if (model.getUserId() == null) {
                    model.setUserId(KbHttpHeaders.KB_USER_ID.getValidIntegerHeaderOrNull(request));
                }
                model.setSiteId(KbHttpHeaders.KB_SITE_ID.getValidIntegerHeaderOrNull(request));
                model.setEp(KbHttpHeaders.KB_EP.getValidIntegerHeaderOrNull(request));
                model.setCid(KbHttpHeaders.KB_CID.getValidHeaderOrNull(request));
                model.setAid(KbHttpHeaders.KB_AID.getValidHeaderOrNull(request));
                model.setIp(KbHttpHeaders.KB_IP.getValidHeaderOrNull(request));
                if(StringUtils.isEmpty(model.getIp())) {
                    model.setIp(request.getHeader("remoteip"));
                }
            }
            if (model.getUserId() == null) {
                throw new IllegalArgumentException(
                        "userId is required. if act by system, should call bySystem() explicitly.");
            }
            model.setAttr(attr);
            return recorder.adminLogService.addLog(model).check();
        }

    }


}
