package com.kaiba.lib.base.middleware.paramvalidator;

import com.kaiba.lib.base.annotation.apiparam.KbUserId;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * author: lyux
 * date: 18-7-25
 */
public class KbUserIdValidator implements ConstraintValidator<KbUserId, Integer>, IKbValidator {

    @Override
    public void initialize(KbUserId constraintAnnotation) {

    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        return value == null || value >= 0;
    }

}
