package com.kaiba.lib.base.middleware.paramvalidator;

import com.kaiba.lib.base.annotation.apiparam.KbSource;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * author: lyux
 * date: 18-7-25
 */
public class KbSourceValidator implements ConstraintValidator<KbSource, Integer>, IKbValidator {

    @Override
    public void initialize(KbSource constraintAnnotation) {

    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if (null == value) {
            return true;
        } else {
            int n = value / 100000;
            return n > 0 && n < 10;
        }
    }

}
