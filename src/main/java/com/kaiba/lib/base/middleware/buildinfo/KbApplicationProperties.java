package com.kaiba.lib.base.middleware.buildinfo;

import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.Main;
import com.kaiba.lib.base.util.GsonUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * author: lyux
 * date: 2020-05-21
 */
@Slf4j
@Getter
public class KbApplicationProperties {

    private final String service;
    private final String host;
    private final String port;
    private final String profile;
    private final String libVersion;
    private final long createTime;
    private final boolean debug;

    private final GitPropertiesProvider gitPropertiesProvider;
    private final Map<String, String> properties;

    public KbApplicationProperties(Environment environment) {
        this.service = environment.getProperty("spring.application.name");
        this.host = environment.getProperty("spring.cloud.client.hostname");
        this.port = environment.getProperty("server.port");
        this.profile = Arrays.stream(environment.getActiveProfiles())
                .reduce((a, b) -> a + "," + b).orElse("none");
        this.libVersion = Main.class.getPackage().getImplementationVersion();
        this.debug = "true".equals(environment.getProperty("debug", "false"));
        this.createTime = System.currentTimeMillis();
        this.gitPropertiesProvider = new GitPropertiesProvider();
        this.properties = properties2map();
    }

    public GitProperties getGitProperties() {
        return gitPropertiesProvider.get();
    }

    public String getProperty(String name) {
        return properties.get(name);
    }

    public String getProperty(String name, String defaultValue) {
        String property = properties.get(name);
        if (property == null) {
            return defaultValue;
        } else {
            return property;
        }
    }

    public void properties2log() {
        log.info("------------------------------------------------");
        properties.forEach((k, v) -> log.info(k + ": " + v));
        log.info("------------------------------------------------");
    }

    private Map<String, String> properties2map() {
        Map<String, String> properties = new LinkedHashMap<>();
        properties.put("service", service);
        properties.put("host", host);
        properties.put("port", port);
        properties.put("profile", profile);
        properties.put("lib", "kaiba-lib-base-" + libVersion);
        properties.put("debug", Boolean.toString(debug));
        properties.put("create_time", DateTimeFormatter
                .ofPattern("yyyy-MM-dd_HH:mm:ss")
                .withZone(ZoneId.systemDefault()).format(Instant.ofEpochMilli(createTime)));
        try {
            Map<String, String> gitProperties = GsonUtils.getGson().fromJson(
                    gitPropertiesProvider.getData(), new TypeToken<Map<String, String>>() {}.getType());
            if (gitProperties != null) {
                properties.putAll(gitProperties);
            }
        } catch (JsonSyntaxException e) {
            log.warn("parse git properties fail: " + gitPropertiesProvider.getData(), e);
        }
        return properties;
    }

}
