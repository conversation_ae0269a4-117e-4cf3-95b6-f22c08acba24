package com.kaiba.lib.base.middleware.apivalidator;

import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.annotation.api.KbHasPermission;
import com.kaiba.lib.base.constant.auth.AuthPermissionLogic;
import com.kaiba.lib.base.domain.auth.AuthResult;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.ServletRequestUtils;
import com.kaiba.lib.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 19-8-20
 */
@Slf4j
public class AuthPermissionChecker {

    private String urlHasPermission = "http://kaiba-m-core/auth/hasPermission";
    private String urlHasPermissionAnd = "http://kaiba-m-core/auth/hasPermissionAnd";
    private String urlHasPermissionOr = "http://kaiba-m-core/auth/hasPermissionOr";

    private final RestTemplate restTemplate;

    public AuthPermissionChecker(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public AuthPermissionChecker setUrlHasPermission(String urlHasPermission) {
        this.urlHasPermission = urlHasPermission;
        return this;
    }

    public AuthPermissionChecker setUrlHasPermissionAnd(String urlHasPermissionAnd) {
        this.urlHasPermissionAnd = urlHasPermissionAnd;
        return this;
    }

    public AuthPermissionChecker setUrlHasPermissionOr(String urlHasPermissionOr) {
        this.urlHasPermissionOr = urlHasPermissionOr;
        return this;
    }

    public void checkPermission(KbHasPermission permissionAnnotation) {
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        if (null == request) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "no request available. this method should only be called under http request context")
                    .setLevel(KbException.LEVEL_INFO);
        }
        String[] permission = permissionAnnotation.value();
        if (permission.length == 0) {
            throw new KbException(KbCode.ILLEGAL_STATE, "permission empty");
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        Map<String, String[]> originRequestMap = request.getParameterMap();
        Map<String, List<String>> requestParamMap = new LinkedMultiValueMap<>(originRequestMap.size());
        originRequestMap.forEach((k, v) -> requestParamMap.put(k, Arrays.asList(v)));
        requestParamMap.put("permission", Arrays.asList(permission));
        requestParamMap.put("checkAuth", Collections.singletonList(permissionAnnotation.checkAuth() ? "1" : "0"));
        HttpEntity<Map<String, List<String>>> requestEntity = new HttpEntity<>(requestParamMap, headers);
        KbEntity<List<AuthResult>> result;
        ResponseEntity<String> response = null;
        String url;
        if (permission.length == 1) {
            url = urlHasPermission;
        } else if (permissionAnnotation.logic() == AuthPermissionLogic.AND) {
            url = urlHasPermissionAnd;
        } else {
            url = urlHasPermissionOr;
        }
        try {
            response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            result = GsonUtils.getGson().fromJson(response.getBody(),
                    new TypeToken<KbEntity<List<AuthResult>>>() {}.getType());
        } catch (JsonSyntaxException e) {
            throw new KbException(KbCode.AUTH_PERMISSION_DENIED,
                    "check permission fail, parse response fail: " + response, e)
                    .setReadableMessage("数据格式错误")
                    .setLevel(KbException.LEVEL_ERROR);
        } catch (Exception e) {
            throw new KbException(KbCode.AUTH_PERMISSION_DENIED,
                    "check permission fail", e)
                    .setReadableMessage("请求失败")
                    .setLevel(KbException.LEVEL_ERROR);
        }
        if (null == result) {
            throw new KbException(KbCode.AUTH_PERMISSION_DENIED,
                    "check permission fail, no result")
                    .setReadableMessage("没有请求到数据...")
                    .setLevel(KbException.LEVEL_ERROR);
        }
        if (!result.isOk()) {
            String message = StringUtils.isEmpty(permissionAnnotation.message()) ?
                    result.getMsg() : permissionAnnotation.message();
            throw new KbException(result.getKbCode(), AuthResult.list2string(result.getData()))
                    .setReadableMessage(message)
                    .setLevel(KbException.LEVEL_INFO);
        }
    }

}
