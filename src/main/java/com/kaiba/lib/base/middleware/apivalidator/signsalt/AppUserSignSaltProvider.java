package com.kaiba.lib.base.middleware.apivalidator.signsalt;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.kaiba.lib.base.constant.KbHttpHeaders;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.domain.appversion.AppVersionModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAppVersionService;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.lib.base.util.ServletRequestUtils;
import com.kaiba.lib.base.util.StringUtils;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;

/**
 * Description: 客户端产生盐值的校验器，需要登录
 * Author: ZM227
 * Date: 2024/12/13 12:03
 */
public class AppUserSignSaltProvider implements IKbSignSaltProvider {

    private final IUserService userService;

    private final IAppVersionService appVersionService;

    private final Map<String, String> packageIdMap = ImmutableMap.of(
        "Android", "com.hz.czfw.app",
        "iOS", "cn.czfw.HangZhou",
        "ohos", "com.kaiba.app"
    );

    private final LoadingCache<String, List<String>> saltListCache = Caffeine.newBuilder()
        .expireAfterAccess(Duration.ofMinutes(30))
        .build(this::getSaltList);

    private final LoadingCache<Integer, String> userPasswordCache = Caffeine.newBuilder()
        .expireAfterAccess(Duration.ofSeconds(5))
        .build(this::requestUserPassword);

    public AppUserSignSaltProvider(IUserService userService, IAppVersionService appVersionService) {
        this.userService = userService;
        this.appVersionService = appVersionService;
    }

    @Override
    public KbSignType getSignType() {
        return KbSignType.APP_USER_ENFORCE;
    }

    @Override
    public List<String> getSalts() {
        if (userService == null) {
            throw new KbException(KbCode.NOT_SUPPORT_YET, "no user service specified").li();
        }
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        if (request == null) {
            throw new KbException(KbCode.AUTH_FAIL_KBSIGN_INVALID, "not in a request context");
        }
        String kbCid = KbHttpHeaders.KB_CID.getValidHeaderOrNull(request);
        String kbVc = KbHttpHeaders.KB_VC.getValidHeaderOrNull(request);
        String kbDo = KbHttpHeaders.KB_DO.getValidHeaderOrNull(request);
        if (StringUtils.isEmpty(kbCid)) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID,
                "check kbSign, cid empty: " + kbCid).li();
        }
        if (StringUtils.isEmpty(kbVc)) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID,
                "check kbSign, vc empty: " + kbVc).li();
        }
        if (StringUtils.isEmpty(kbDo)) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID,
                "check kbSign, do empty: " + kbDo).li();
        }
        String userIdStr = request.getParameter("userId");
        if (null == userIdStr) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID,
                "check kbSign, invalid userId: " + userIdStr).li();
        }
        String password = userPasswordCache.get(Integer.valueOf(userIdStr));
        String finalKbCid = kbCid.length() < 8 ? kbCid : kbCid.substring(0, 8);
        return Objects.requireNonNull(saltListCache.get(kbDo + "," + kbVc)).stream()
            .map(a -> a + finalKbCid + password).collect(Collectors.toList());
    }

    private List<String> getSaltList(String cacheKey) {
        String[] keys = cacheKey.split(",");
        KbEntity<AppVersionModel> appVersionMode = appVersionService.getVersionByPackageIdAndCode(
            packageIdMap.get(keys[0]), Integer.valueOf(keys[1]));
        if (Objects.isNull(appVersionMode) || !appVersionMode.isOk() || Objects.isNull(
            appVersionMode.getData())) {
            return Lists.newArrayList();
        }
        return appVersionMode.getData().getSalt();
    }

    private String requestUserPassword(Integer userId) {
        return userService.getUserPasswordById(userId).data().orElse("");
    }
}
