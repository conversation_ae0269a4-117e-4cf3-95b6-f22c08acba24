package com.kaiba.lib.base.middleware.jwt;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

import static sun.nio.ch.IOStatus.EOF;

/**
 * author: lyux
 * date: 2020-09-02
 */
public final class KbJWTKeyProvider {

    public static PrivateKey getEC256PrivateKeyByResource(String privatePkcsPath)
            throws InvalidKeySpecException, NoSuchProviderException, NoSuchAlgorithmException, IOException {
        Security.addProvider(new BouncyCastleProvider());
        KeyFactory keyFactory = KeyFactory.getInstance("ECDH", "BC");
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(privatePkcsPath);
        if (inputStream == null) {
            throw new IllegalStateException("open cert file fail for jwt provider");
        }
        byte[] devicePriKeyBytes = streamToByteArray(inputStream);
        PKCS8EncodedKeySpec devicePriKeySpec = new PKCS8EncodedKeySpec(devicePriKeyBytes);
        return keyFactory.generatePrivate(devicePriKeySpec);
    }

    public static PublicKey getEC256PublicKeyByResource(String publicKeyPath) throws
            InvalidKeySpecException, NoSuchProviderException, NoSuchAlgorithmException, IOException {
        Security.addProvider(new BouncyCastleProvider());
        KeyFactory keyFactory = KeyFactory.getInstance("ECDH", "BC");
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(publicKeyPath);
        if (inputStream == null) {
            throw new IllegalStateException("open cert file fail for jwt provider");
        }
        String publicKeyBase64 = streamToString(inputStream);
        publicKeyBase64 = publicKeyBase64
                .replaceAll("-*BEGIN.*KEY-*", "")
                .replaceAll("-*END.*KEY-*", "")
                .replaceAll("\r", "")
                .replaceAll("\n", "");
        byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyBase64);
        X509EncodedKeySpec pubX509 = new X509EncodedKeySpec(publicKeyBytes);
        return keyFactory.generatePublic(pubX509);
    }

    private static byte[] streamToByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024 * 4];
        int n;
        while (EOF != (n = input.read(buffer))) {
            output.write(buffer, 0, n);
        }
        return output.toByteArray();
    }

    private static String streamToString(InputStream input) throws IOException {
        StringBuilder sb = new StringBuilder();
        InputStreamReader in = new InputStreamReader(input, StandardCharsets.UTF_8);
        char[] buffer = new char[1024 * 4];
        int n;
        while (EOF != (n = in.read(buffer))) {
            sb.append(buffer, 0, n);
        }
        return sb.toString();
    }

}
