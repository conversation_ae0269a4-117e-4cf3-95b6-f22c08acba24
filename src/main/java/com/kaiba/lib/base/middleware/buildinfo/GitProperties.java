package com.kaiba.lib.base.middleware.buildinfo;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * author: lyux
 * date: 2020-05-21
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class GitProperties {

    @SerializedName("git.branch")
    private String branch;

    @SerializedName("git.tags")
    private String tags;

    @SerializedName("git.dirty")
    private String dirty;

    @SerializedName("git.build.host")
    private String buildHost;

    @SerializedName("git.build.time")
    private String buildTime;

    @SerializedName("git.build.version")
    private String buildVersion;

    @SerializedName("git.build.user.email")
    private String buildUserEmail;

    @SerializedName("git.build.user.name")
    private String buildUserName;

    @SerializedName("git.commit.id")
    private String commitId;

    @SerializedName("git.commit.id.abbrev")
    private String commitIdAbbrev;

    @SerializedName("git.commit.id.describe")
    private String commitIdDescribe;

    @SerializedName("git.commit.message.full")
    private String commitMessageFull;

    @SerializedName("git.commit.message.short")
    private String commitMessageShort;

    @SerializedName("git.commit.time")
    private String commitTime;

    @SerializedName("git.commit.user.email")
    private String commitUserEmail;

    @SerializedName("git.commit.user.name")
    private String commitUserName;

    @SerializedName("git.total.commit.count")
    private String commitTotalCommitCount;

    @SerializedName("git.remote.origin.url")
    private String commitRemoteOriginUrl;

}
