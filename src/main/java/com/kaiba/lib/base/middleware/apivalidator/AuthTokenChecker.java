package com.kaiba.lib.base.middleware.apivalidator;

import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.constant.KbHttpHeaders;
import com.kaiba.lib.base.middleware.jwt.KbJWTHandler;
import com.kaiba.lib.base.middleware.jwt.KbJWTPayload;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.ServletRequestUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;

/**
 * author: lyux
 * date: 18-7-25
 *
 * TODO: 按一定条件, 将 jwt 存入 redis 缓存, 以实现 登录互斥 和 session 管理
 */
@Slf4j
public class AuthTokenChecker {

    private static final String JWT_TOKEN_PREFIX = "kb_jwt_token_";

    private final KbJWTHandler jwtVerifier;
    private final AuthLoginTokenChecker legacyLoginTokenChecker;
    private boolean isSkipCheck = false;

    public AuthTokenChecker(
            KbJWTHandler jwtVerifier,
            AuthLoginTokenChecker authLoginTokenChecker
    ) {
        this.jwtVerifier = jwtVerifier;
        this.legacyLoginTokenChecker = authLoginTokenChecker;
    }

    public void setIsSkipCheck(boolean isSkipCheck) {
        this.isSkipCheck = isSkipCheck;
    }

    public void checkToken() {
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        if (null == request) {
            throw new KbException(KbCode.ILLEGAL_STATE, "not under request context").li();
        }

        if (isSkipCheck) {
            log.warn("check login skipped for " + ServletRequestUtils.getRequestInfo(request));
            return;
        }

        String token = request.getHeader(KbHttpHeaders.KB_TOKEN.getHeaderName());
        if (null == token || token.length() <= 32) {
            // header 中不存在 token 或小于等于 32 位则使用老的 token 逻辑验证
            legacyLoginTokenChecker.checkLogin(request);
        } else {
            // 大于 32 位 token 视为 jwt
            checkJWTToken(token, request);
        }
    }

    public void checkJWTToken(String jwt, HttpServletRequest request) {
        KbJWTPayload payload = jwtVerifier.parseJWTOrThrow(jwt);
        if (payload.getEndpoint() == KbEndpoint.THIRD_PARTY) {
            String accessId = request.getHeader(KbHttpHeaders.KB_AID.getHeaderName());
            if (!payload.getId().equals(accessId)) {
                throw new KbException(KbCode.AUTH_FAIL_TOKEN_INVALID,
                        "token accessId mismatch: " + payload.getId() + " -> " + accessId).li();
            }
        } else {
            String userIdStr = request.getHeader(KbHttpHeaders.KB_USER_ID.getHeaderName());
            if (!payload.getId().equals(userIdStr)) {
                throw new KbException(KbCode.AUTH_FAIL_TOKEN_INVALID,
                        "token userId mismatch: " + payload.getId() + " -> " + userIdStr).li();
            }
        }
    }

}
