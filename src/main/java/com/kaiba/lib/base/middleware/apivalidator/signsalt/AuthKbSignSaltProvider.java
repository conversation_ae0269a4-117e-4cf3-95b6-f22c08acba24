package com.kaiba.lib.base.middleware.apivalidator.signsalt;

import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.util.CollectionUtils;

/**
 * author: lyux
 * date: 2022-08-18
 */
@Slf4j
public class AuthKbSignSaltProvider {

    private final ConcurrentHashMap<KbSignType, IKbSignSaltProvider> providers;

    public AuthKbSignSaltProvider() {
        providers = new ConcurrentHashMap<>(KbSignType.values().length);
        for (KbSignType kbSignType : KbSignType.values()) {
            if (kbSignType.getSalt() != null) {
                providers.put(kbSignType, new FixedSignSaltProvider(kbSignType));
            }
        }
        addSignSaltProvider(new CIDSignSaltProvider());
    }

    public AuthKbSignSaltProvider addSignSaltProvider(IKbSignSaltProvider saltProvider) {
        providers.put(saltProvider.getSignType(), saltProvider);
        return this;
    }

    public String getSaltOrThrow(KbSignType signType) {
        return getSaltOrThrow(signType, null);
    }

    public String getSaltOrThrow(KbSignType signType, HttpServletRequest request) {
        IKbSignSaltProvider saltProvider = providers.get(signType);
        if (saltProvider == null) {
            throw new KbException(KbCode.AUTH_FAIL_KBSIGN_INVALID,
                    "check kbSign, kbSignType not supported: " + signType).li();
        }

        List<String> salts = saltProvider.getSalts();
        if (CollectionUtils.isEmpty(salts)) {
            throw new KbException(KbCode.AUTH_FAIL_KBSIGN_INVALID,
                    "check kbSign, no salt yielded: " + signType).ld();
        }

        return salts.get(0);
    }

}
