package com.kaiba.lib.base.middleware.jwt;

import com.kaiba.lib.base.constant.KbEndpoint;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2020-09-02
 */
@Data
@ToString
@NoArgsConstructor
public class KbJWTPayload {

    /** token 拥有者 id. 可能是 userId, 也可能是 accessId. 需要根据 {@link #endpoint} 判断. */
    private String id;

    /** 由哪一个端发起的访问. */
    private KbEndpoint endpoint;

    /** token 过期时间点. 单位毫秒. */
    private long expireAt;

    /** token 签发时间点. 单位毫秒. */
    private long issuedAt;

    public KbJWTPayload(String id, KbEndpoint endpoint, long expireAt, long issuedAt) {
        this.id = id;
        this.endpoint = endpoint;
        this.expireAt = expireAt;
        this.issuedAt = issuedAt;
    }

}
