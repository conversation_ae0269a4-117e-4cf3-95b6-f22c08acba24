package com.kaiba.lib.base.middleware.paramvalidator;

import com.kaiba.lib.base.annotation.apiparam.KbPage;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * author: lyux
 * date: 18-7-25
 */
public class KbPageValidator implements ConstraintValidator<KbPage, Integer>, IKbValidator {

    @Override
    public void initialize(KbPage constraintAnnotation) {

    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        return value == null || value > 0;
    }

}
