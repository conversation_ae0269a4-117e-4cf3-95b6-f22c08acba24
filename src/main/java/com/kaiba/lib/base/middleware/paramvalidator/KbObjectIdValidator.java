package com.kaiba.lib.base.middleware.paramvalidator;

import com.kaiba.lib.base.annotation.apiparam.KbObjectId;
import com.kaiba.lib.base.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * author: lyux
 * date: 18-7-25
 */
public class KbObjectIdValidator implements ConstraintValidator<KbObjectId, String>, IKbValidator {

    @Override
    public void initialize(KbObjectId constraintAnnotation) {

    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        return value == null || StringUtils.isValidObjectId(value);
    }

}
