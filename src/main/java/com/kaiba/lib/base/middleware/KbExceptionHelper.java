package com.kaiba.lib.base.middleware;

import com.kaiba.lib.base.constant.KbHttpHeaders;
import com.kaiba.lib.base.constant.Restful;
import com.kaiba.lib.base.middleware.paramvalidator.IKbValidator;
import com.kaiba.lib.base.response.*;
import com.kaiba.lib.base.util.ServletRequestUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 19-11-15
 */
@Slf4j
public class KbExceptionHelper {

    private final KbRes kbRes;
    private boolean isResponseDebug;

    public KbExceptionHelper(KbRes kbRes, boolean isResponseDebug) {
        this.kbRes = kbRes;
        this.isResponseDebug = isResponseDebug;
    }

    public KbExceptionHelper setResponseDebug(boolean responseDebug) {
        isResponseDebug = responseDebug;
        return this;
    }

    public KbResponse<Void> exception2response(HttpServletRequest req, Exception e, boolean printException) {
        KbResponse<Void> response = new KbResponse<>();
        if (e instanceof KbException) {
            KbException kbe = (KbException) e;
            return exception2response(req, kbe);
        }
        boolean responseDebug = isResponseDebug || KbHttpHeaders.KB_DEBUG.hasHeader(req);
        response.setState(Restful.RSP_FAIL);
        response.setMsg(Restful.REQ_FAIL);
        List<KbDebug> debugs = kbRes.on(e).setRequest(req).createKbDebugList();
        if (responseDebug) {
            response.setDebugs(debugs);
        }
        if (printException) {
            log.error(dump(response, debugs), e);
        } else {
            log.error(dump(response, debugs));
        }
        return response;
    }

    public KbResponse<Void> exception2response(HttpServletRequest req, KbException kbe) {
        boolean responseDebug = isResponseDebug || KbHttpHeaders.KB_DEBUG.hasHeader(req);
        KbResponse<Void> response = new KbResponse<>();
        response.setKbCode(kbe.getCode().getCode());
        if (kbe.getReadableMessage() == null) {
            response.setMsg(kbe.getCode().getMessage());
        } else {
            response.setMsg(kbe.getReadableMessage());
        }
        response.setState(kbe.getResponseCode());
        List<KbDebug> debugs = kbRes.on(kbe).setRequest(req).createKbDebugList();
        if (responseDebug) {
            response.setDebugs(debugs);
        }
        switch (kbe.getLevel()) {
            case KbException.LEVEL_DEBUG:
                log.debug(dump(response, debugs));
                break;
            case KbException.LEVEL_INFO:
                log.info(dump(response, debugs));
                break;
            case KbException.LEVEL_WARN:
                log.warn(dump(response, debugs));
                break;
            case KbException.LEVEL_ERROR:
            default:
                log.error(dump(response, debugs), kbe);
                break;
        }
        return response;
    }

    public KbResponse<Void> exception2response(HttpServletRequest req, ConstraintViolationException e) {
        String msg = e.getConstraintViolations().stream()
                .findFirst()
                .flatMap(cv -> {
                    Class<? extends ConstraintValidator<?, ?>> clazz = cv
                            .getConstraintDescriptor()
                            .getConstraintValidatorClasses()
                            .get(0);
                    boolean isKbValidator = false;
                    for (Class<?> clz : clazz.getInterfaces()) {
                        if (clz.equals(IKbValidator.class)) {
                            isKbValidator = true;
                            break;
                        }
                    }
                    return Optional.of(isKbValidator ? cv.getMessage() : Restful.REQ_PARAM_INVALID);
                })
                .orElse(Restful.REQ_PARAM_INVALID);
        log.info(ServletRequestUtils.getRequestInfo(req) + ". msg=" + msg);
        KbResponse<Void> kbResponse = new KbResponse<>();
        kbResponse.setKbCode(KbCode.REQUEST_PARAM_INVALID.getCode());
        kbResponse.setMsg(msg);
        return kbResponse;
    }

    public KbEntity<Void> exception2entity(HttpServletRequest req, KbException e) {
        return kbRes.on(e)
                .setRequest(req)
                .logExceptionTrace(e.getLevel() >= KbException.LEVEL_ERROR)
                .create();
    }

    public static String dump(KbResponse<?> response, List<KbDebug> debugs) {
        StringBuilder sb = new StringBuilder();
        sb.append(response.getState()).append("-")
                .append(response.getKbCode()).append("-")
                .append(response.getMsg());
        if (null != debugs && debugs.size() != 0) {
            sb.append("\n").append(KbDebug.dumpList(debugs, true));
        }
        return sb.toString();
    }

}
