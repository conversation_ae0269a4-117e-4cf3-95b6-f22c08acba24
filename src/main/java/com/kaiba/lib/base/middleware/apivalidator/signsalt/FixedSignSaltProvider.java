package com.kaiba.lib.base.middleware.apivalidator.signsalt;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.constant.KbSignType;
import java.util.List;

/**
 * author: lyux
 * date: 2022-08-18
 */
public class FixedSignSaltProvider implements IKbSignSaltProvider {

    private final KbSignType kbSignType;

    public FixedSignSaltProvider(KbSignType kbSignType) {
        this.kbSignType = kbSignType;
    }

    @Override
    public KbSignType getSignType() {
        return kbSignType;
    }

    @Override
    public List<String> getSalts() {
        return Lists.newArrayList(getSignType().getSalt());
    }

}
