package com.kaiba.lib.base.middleware.paramvalidator;

import com.kaiba.lib.base.annotation.apiparam.KbSiteId;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * author: lyux
 * date: 18-7-25
 */
public class KbSiteIdValidator implements ConstraintValidator<KbSiteId, Integer>, IKbValidator {

    @Override
    public void initialize(KbSiteId constraintAnnotation) {

    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        return value == null || value >= 0;
    }

}
