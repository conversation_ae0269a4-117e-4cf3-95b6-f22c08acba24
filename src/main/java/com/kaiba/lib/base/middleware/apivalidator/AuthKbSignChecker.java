package com.kaiba.lib.base.middleware.apivalidator;

import com.kaiba.lib.base.constant.KbHttpParams;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.middleware.apivalidator.signsalt.AuthKbSignSaltProvider;
import com.kaiba.lib.base.middleware.apivalidator.signsalt.IKbSignSaltProvider;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.ServletRequestUtils;
import com.kaiba.lib.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;

/**
 * author: yanghy
 * date: 19-05-28
 */
@Slf4j
public class AuthKbSignChecker {

    private static final String KB_KEY = "kbKey";

    private final AuthKbSignSaltProvider providers;

    public AuthKbSignChecker() {
        this.providers = new AuthKbSignSaltProvider();
    }

    public AuthKbSignChecker addSignSaltProvider(IKbSignSaltProvider saltProvider) {
        providers.addSignSaltProvider(saltProvider);
        return this;
    }

    /**
     * sign type 不再由调用方指定, 而应当标记在需要验证签名的接口上.
     * 建议使用 {@link #checkSign(KbSignType)}
     */
    @Deprecated
    public void checkSign() {
        checkSign(null);
    }

    public void checkSign(KbSignType signType) {
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        if (null == request) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "no request available. this method should only be called under http request context").li();
        }

        if (signType == null) {
            String signTypeStr = request.getParameter(KbHttpParams.KB_SIGN_TYPE.getName());
            signType = KbSignType.valueOf(Integer.valueOf(signTypeStr))
                    .orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT, "cannot determine sign type").ld());
        }

        String signTimeStr = request.getParameter(KbHttpParams.KB_SIGN_TIME.getName());
        if (!StringUtils.isEmpty(signTimeStr)) {
            long signTime = StringUtils.toLong(signTimeStr, 0L);
            if (System.currentTimeMillis() - signTime > signType.getTimeLimitMillis()) {
                throw new KbException(KbCode.AUTH_FAIL_KBSIGN_EXPIRED).ld();
            }
        }

        String kbSign = request.getParameter(KbHttpParams.KB_SIGN.getName());
        if (kbSign == null) {
            throw new KbException(KbCode.AUTH_FAIL_KBSIGN_INVALID, "kbSign null").ld();
        }

        String salt = providers.getSaltOrThrow(signType, request);
        String signature = generateSignature(request, salt);
        if (!kbSign.equals(signature)) {
            throw new KbException(KbCode.AUTH_FAIL_KBSIGN_INVALID,
                    "check kbSign, kbSign invalid: " + kbSign + "; generateSignature:" + signature).ld();
        }
    }

    /***
     * @param salt 指定盐值
     * @param expireInSecond 指定过期时间, 单位秒
     */
    public void checkSign(String salt, long expireInSecond) {
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        if (null == request) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "no request available. this method should only be called under http request context").li();
        }

        String signTypeStr = request.getParameter(KbHttpParams.KB_SIGN_TYPE.getName());
        if (!Integer.toString(KbSignType.API.getValue()).equals(signTypeStr)) {
            throw new KbException(KbCode.AUTH_FAIL_KBSIGN_INVALID, "sign type should be S***").li();
        }

        String signTimeStr = request.getParameter(KbHttpParams.KB_SIGN_TIME.getName());
        if (!StringUtils.isEmpty(signTimeStr)) {
            long signTime = StringUtils.toLong(signTimeStr, 0L);
            if (System.currentTimeMillis() - signTime > expireInSecond * 1000) {
                throw new KbException(KbCode.AUTH_FAIL_KBSIGN_EXPIRED).ld();
            }
        }

        String kbSign = request.getParameter(KbHttpParams.KB_SIGN.getName());
        if (kbSign == null) {
            throw new KbException(KbCode.AUTH_FAIL_KBSIGN_INVALID, "kbSign null").ld();
        }

        String signature = generateSignature(request, salt);
        if (!kbSign.equals(signature)) {
            throw new KbException(KbCode.AUTH_FAIL_KBSIGN_INVALID,
                    "check kbSign, kbSign invalid: " + kbSign + "; generateSignature:" + signature).ld();
        }
    }

    public static String generateSignature(HttpServletRequest kbRequest, String salt) {
        return generateSignature(kbRequest.getParameterMap(), salt);
    }

    public static String generateSignature(Map<String, String[]> params, String salt) {
        Set<String> keySet = params.keySet();
        String[] keys = keySet.toArray(new String[0]);
        Arrays.sort(keys);
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            if (key.equals(KbHttpParams.KB_SIGN.getName())) {
                continue;
            }
            String[] vals = params.get(key);
            Arrays.sort(vals);
            sb.append(key).append("=").append(array2String(vals)).append("&");
        }
        String signatureRaw = sb.append(KB_KEY).append("=").append(salt).toString().toLowerCase();
        //log.info(signatureRaw);
        String signatureMD5 = StringUtils.toMd5(signatureRaw);
        String lower8 = signatureMD5.substring(0, 8);
        String upper8 = signatureMD5.substring(24, 32);
        return lower8 + upper8;
    }

    private static String array2String(String[] strArr) {
        if (null == strArr || strArr.length == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (String key : strArr) {
            sb.append(key).append("&");
        }
        String strLowerCase = sb.toString().toLowerCase();
        return strLowerCase.substring(0, strLowerCase.length() - 1);
    }

}
