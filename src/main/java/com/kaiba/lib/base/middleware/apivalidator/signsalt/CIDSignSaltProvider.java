package com.kaiba.lib.base.middleware.apivalidator.signsalt;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.constant.KbHttpHeaders;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.ServletRequestUtils;
import com.kaiba.lib.base.util.StringUtils;

import java.util.List;
import javax.servlet.http.HttpServletRequest;

/**
 * author: lyux
 * date: 2022-08-18
 */
public class CIDSignSaltProvider implements IKbSignSaltProvider {

    @Override
    public KbSignType getSignType() {
        return KbSignType.CID;
    }

    @Override
    public List<String> getSalts() {
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        if (request == null) {
            throw new KbException(KbCode.AUTH_FAIL_KBSIGN_INVALID, "not in a request context");
        }
        String cid = KbHttpHeaders.KB_CID.getValidHeaderOrNull(request);
        if (StringUtils.isEmpty(cid)) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID, "check kbSign, cid empty: " + cid).li();
        }
        return Lists.newArrayList(cid.length() < 8 ? cid : cid.substring(0, 8));
    }
}
