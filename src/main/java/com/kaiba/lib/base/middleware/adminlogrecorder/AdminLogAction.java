package com.kaiba.lib.base.middleware.adminlogrecorder;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-07-26
 *
 * 预设的一些常见操作类型, 供业务选用.
 */
@Getter
public enum AdminLogAction {

    CREATE("创建"),
    UPDATE("更新"),
    UPDATE_DATA("更新数据"),
    UPDATE_STATE("更新状态"),
    UPDATE_AS_SIGNED("更新状态为已签发"),
    UPDATE_AS_ONLINE("更新状态为已上线"),
    UPDATE_AS_OFFLINE("更新状态为已下线"),
    UPDATE_AS_PAUSED("更新状态为已暂停"),
    UPDATE_AS_ARCHIVED("更新状态为已归档"),
    UPDATE_SCHEDULE_TIME("更新预约起止时间"),
    REFRESH("刷新"),
    REFRESH_TIME("刷新时间"),
    DELETE("删除"),
    PUSH("推送")
    ;

    private final String description;

    AdminLogAction(String description) {
        this.description = description;
    }

    public static Optional<AdminLogAction> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (AdminLogAction v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<AdminLogAction> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (AdminLogAction v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
