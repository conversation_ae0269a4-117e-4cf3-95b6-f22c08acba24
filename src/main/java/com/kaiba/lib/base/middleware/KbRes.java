package com.kaiba.lib.base.middleware;

import com.kaiba.lib.base.middleware.buildinfo.KbApplicationProperties;
import com.kaiba.lib.base.response.*;
import com.kaiba.lib.base.util.ServletRequestUtils;
import com.kaiba.lib.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * author: lyux
 * date: 18-12-14
 *
 * 用以生成 {@link KbEntity}, 微服务间接口调用的统一返回格式. 具有基本的链路追踪能力.
 */
@Slf4j
public class KbRes {

    private transient static final int TRACE_MAX_LENGTH = 10;
    private transient static final int TRACE_MAX_LEVEL = 3;
    private transient static final int ENABLE_TRACE_THRESHOLD = KbException.LEVEL_WARN;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter
            .ofPattern("yyyy-MM-dd HH:mm:ss")
            .withZone(ZoneId.systemDefault());

    private final KbApplicationProperties applicationProperties;
    private final KbDebugPrototypeProvider kbDebugPrototypeProvider;

    /** 场景: 面向客户端的返回值在生产环境中始终应该隐藏调试信息 */
    private final boolean alwaysTrimDebug;

    public KbRes(Environment environment) {
        this(environment, false);
    }

    public KbRes(Environment environment, boolean alwaysTrimDebug) {
        this.kbDebugPrototypeProvider = KbRes.this::createKbDebug;
        this.applicationProperties = new KbApplicationProperties(environment);
        this.applicationProperties.properties2log();
        this.alwaysTrimDebug = alwaysTrimDebug;
    }

    public KbApplicationProperties getApplicationProperties() {
        return this.applicationProperties;
    }

    public <T> KbEntity<T> ok(T data) {
        KbEntity<T> entity = new KbEntity<>(data);
        if (alwaysTrimDebug) {
            entity.setKbDebugs(null);
        }
        return entity;
    }

    public <T> KbEntity<List<T>> ok(Page<T> data) {
        KbEntity<List<T>> entity = new KbEntity<>(data.getContent());
        entity.setTotalPage((long) data.getTotalPages());
        entity.setTotalCount(data.getTotalElements());
        if (alwaysTrimDebug) {
            entity.setKbDebugs(null);
        }
        return entity;
    }

    public <T> KbEntity<T> ok() {
        KbEntity<T> entity = new KbEntity<>();
        if (alwaysTrimDebug) {
            entity.setKbDebugs(null);
        }
        return entity;
    }

    public <T> KbEntity<T> asIs(KbEntity<T> entity) {
        if (alwaysTrimDebug) {
            entity.setKbDebugs(null);
        }
        return entity;
    }


    public <T> KbEntity<T> wrap(KbEntity<T> entity) {
        KbEntity<T> ret = new KbEntity<>(entity.getKbCode(), entity.getMsg(), entity.getData());
        ret.setTotalPage(entity.getTotalPage());
        ret.setTotalCount(entity.getTotalCount());
        if (alwaysTrimDebug) {
            entity.setKbDebugs(null);
        } else if (!entity.isOk()) {
            ret.addKbDebug(entity.getKbDebugs());
            ret.addKbDebug(createKbDebug(entity.getKbCode(), null, null));
        }
        return ret;
    }

    public <T> KbEntity<T> wrap(KbException exception) {
        KbCode kbCode = exception.getCode();
        KbEntity<T> entity = new KbEntity<>(kbCode, exception.getReadableMessage(), null);
        if (exception instanceof KbWrapException) {
            KbWrapException wrapException = (KbWrapException) exception;
            if (wrapException.getKbDebugs() != null && wrapException.getKbDebugs().size() != 0) {
                entity.addKbDebug(wrapException.getKbDebugs());
            }
        }
        entity.addKbDebug(createKbDebug(kbCode, null, null));
        if (alwaysTrimDebug) {
            entity.setKbDebugs(null);
        }
        return entity;
    }

    public <T> KbEntity<T> err(KbCode kbCode, String debugMessage, String readableMessage) {
        KbEntity<T> entity = new KbEntity<>(kbCode, readableMessage, null);
        if (debugMessage != null) {
            entity.addKbDebug(createKbDebug(kbCode, debugMessage, null));
        }
        if (alwaysTrimDebug) {
            entity.setKbDebugs(null);
        }
        return entity;
    }

    public <T> KbEntity<T> err(KbCode kbCode, String debugMessage) {
        return err(kbCode, debugMessage, null);
    }

    public <T> KbEntity<T> err(KbCode kbCode) {
        return err(kbCode, null);
    }

    public <T> KbEntity<T> msg(KbCode kbCode, String readableMessage) {
        return err(kbCode, null, readableMessage);
    }

    public <T> KbEntity<T> msg(String readableMessage) {
        return err(KbCode.REQUEST_FAIL, null, readableMessage);
    }

    public KbEntityBuilder on() {
        return new KbEntityBuilder(kbDebugPrototypeProvider)
                .setAlwaysTrimDebug(alwaysTrimDebug);
    }

    public KbEntityBuilder on(KbCode kbCode) {
        return new KbEntityBuilder(kbDebugPrototypeProvider)
                .setKbCode(kbCode)
                .setAlwaysTrimDebug(alwaysTrimDebug);
    }

    public KbEntityBuilder on(KbEntity<?> entity) {
        return new KbEntityBuilder(kbDebugPrototypeProvider)
                .fromKbEntity(entity)
                .setAlwaysTrimDebug(alwaysTrimDebug);
    }

    public KbEntityBuilder on(Exception exception) {
        if (exception instanceof KbException) {
            KbException kbException = (KbException) exception;
            return new KbEntityBuilder(kbDebugPrototypeProvider)
                    .fromKbException(kbException)
                    .setAlwaysTrimDebug(alwaysTrimDebug);
        } else {
            return new KbEntityBuilder(kbDebugPrototypeProvider)
                    .addKbDebugWithTrace(KbCode.EXCEPTION, exception)
                    .setAlwaysTrimDebug(alwaysTrimDebug);
        }
    }

    public KbDebug createKbDebug(KbCode kbCode, String debugMessage, HttpServletRequest request) {
        kbCode = null == kbCode ? KbCode.ERROR : kbCode;
        KbDebug kbDebug = new KbDebug();
        kbDebug.setKbCode(kbCode.getCode());
        kbDebug.setMessage(debugMessage == null ? kbCode.getMsg() : debugMessage);
        kbDebug.setService(applicationProperties.getService());
        kbDebug.setHost(applicationProperties.getHost());
        kbDebug.setPort(applicationProperties.getPort());
        kbDebug.setConfProfile(applicationProperties.getProfile());
        kbDebug.setConfDebug(applicationProperties.isDebug());
        kbDebug.setLibVersion(applicationProperties.getLibVersion());
        kbDebug.setBuildTime(applicationProperties.getGitProperties().getBuildTime());
        kbDebug.setBuildCommitId(applicationProperties.getGitProperties().getCommitId());
        kbDebug.setBuildBranch(applicationProperties.getGitProperties().getBranch());
        kbDebug.setBuildDirty(applicationProperties.getGitProperties().getDirty());
        kbDebug.setTime(DATE_TIME_FORMATTER.format(Instant.now()));

        if (null == request) {
            request = ServletRequestUtils.getCurrentRequest();
        }
        if (null != request) {
            kbDebug.setRequestUri(request.getRequestURI());
            kbDebug.setRequestMethod(request.getMethod());
            int paramSize = request.getParameterMap().size();
            if (paramSize != 0) {
                StringBuilder sb = new StringBuilder();
                request.getParameterMap().forEach((k, v) ->
                        sb.append(k).append("=").append(Arrays.toString(v)).append(","));
                sb.deleteCharAt(sb.length() - 1);
                kbDebug.setRequestParams(sb.toString());
            }
        }
        return kbDebug;
    }

    protected boolean isAlwaysTrimDebug() {
        return alwaysTrimDebug;
    }

    public static class KbEntityBuilder {

        private Object data;
        private KbCode kbCode;
        private String readableMessage;
        private Long totalPage;
        private Long totalCount;
        private List<KbDebug> kbDebugs;
        private HttpServletRequest request;
        private Throwable exception;
        private boolean alwaysTrimDebug;

        private final KbDebugPrototypeProvider kbDebugPrototypeProvider;

        private boolean logWhenError = true;
        private boolean logWhenOk = false;
        private boolean logExceptionTrace = true;
        private boolean logData = false;
        private int logLevel = KbException.LEVEL_INFO;

        public KbEntityBuilder(KbDebugPrototypeProvider kbDebugPrototypeProvider) {
            this.kbDebugPrototypeProvider = kbDebugPrototypeProvider;
        }

        public KbEntityBuilder setData(Object data) {
            this.data = data;
            return this;
        }

        public KbEntityBuilder setPageable(Page<?> data) {
            this.data = data.getContent();
            this.totalPage = (long) data.getTotalPages();
            this.totalCount = data.getTotalElements();
            return this;
        }

        public KbEntityBuilder setKbCode(KbCode kbCode) {
            this.kbCode = kbCode;
            if (readableMessage == null) {
                readableMessage = kbCode.getMessage();
            }
            return this;
        }

        public KbEntityBuilder setTotalPage(Long totalPage) {
            this.totalPage = totalPage;
            return this;
        }

        public KbEntityBuilder setTotalPage(Integer totalPage) {
            return setTotalPage(null == totalPage ? null : (long) totalPage);
        }

        public KbEntityBuilder setTotalCount(Long totalCount) {
            this.totalCount = totalCount;
            return this;
        }

        public KbEntityBuilder setTotalCount(Integer totalCount) {
            return setTotalCount(null == totalCount ? null : (long) totalCount);
        }

        public KbEntityBuilder setReadableMessage(String readableMessage) {
            this.readableMessage = readableMessage;
            return this;
        }

        public KbEntityBuilder setRequest(HttpServletRequest request) {
            this.request = request;
            return this;
        }

        public KbEntityBuilder setAlwaysTrimDebug(boolean alwaysTrimDebug) {
            this.alwaysTrimDebug = alwaysTrimDebug;
            return this;
        }

        public KbEntityBuilder fromKbEntity(KbEntity<?> entity) {
            this.data = entity.getData();
            this.totalPage = entity.getTotalPage();
            this.readableMessage = entity.getMsg();
            this.kbCode = entity.getKbCode();
            if (!entity.isOk()) {
                if (kbDebugs == null) {
                    kbDebugs = new LinkedList<>();
                }
                if (entity.getKbDebugs() != null && entity.getKbDebugs().size() != 0) {
                    kbDebugs.addAll(0, entity.getKbDebugs());
                }
                kbDebugs.add(0, kbDebugPrototypeProvider.createKbDebug(entity.getKbCode(), null, request));
            }
            return this;
        }

        public KbEntityBuilder fromKbException(KbException exception) {
            this.kbCode = exception.getCode();
            this.readableMessage = exception.getReadableMessage();
            this.exception = exception;
            this.logLevel = exception.getLevel();
            this.logExceptionTrace = exception.getLevel() >= ENABLE_TRACE_THRESHOLD;
            if (exception instanceof KbWrapException) {
                KbWrapException wrapException = (KbWrapException) exception;
                if (wrapException.getKbDebugs() != null && wrapException.getKbDebugs().size() != 0) {
                    for (int i = wrapException.getKbDebugs().size() - 1; i >= 0; i --) {
                        addKbDebug(wrapException.getKbDebugs().get(i));
                    }
                }
            }
            addKbDebug(this.kbCode, exception, null, this.logExceptionTrace);
            return this;
        }

        public KbEntityBuilder addKbDebug(KbDebug kbDebug) {
            if (kbDebug != null) {
                if (this.kbDebugs == null) {
                    this.kbDebugs = new LinkedList<>();
                }
                this.kbDebugs.add(0, kbDebug);
            }
            return this;
        }

        public KbEntityBuilder addKbDebug(KbCode kbCode, Throwable e, String debugMessage, boolean withTrace) {
            this.exception = e;
            if (e instanceof KbException) {
                this.logLevel = ((KbException) e).getLevel();
            }
            if (StringUtils.isEmpty(debugMessage)) {
                String exceptionMessage = getDebugMessageFromThrowable(e);
                debugMessage = (e == null || StringUtils.isEmpty(exceptionMessage)) ?
                        kbCode.getMsg() : exceptionMessage;
            }
            KbDebug debug = kbDebugPrototypeProvider.createKbDebug(kbCode, debugMessage, request);
            if (withTrace && e != null) {
                debug.setTrace(generateTrace(e));
            }
            return addKbDebug(debug);
        }

        public KbEntityBuilder addKbDebugWithTrace(KbCode kbCode, Throwable e, String debugMessage) {
            return addKbDebug(kbCode, e, debugMessage, true);
        }

        public KbEntityBuilder addKbDebugWithTrace(KbCode kbCode, Throwable e) {
            return addKbDebugWithTrace(kbCode, e, null);
        }

        public <T> KbEntity<T> create() {
            KbEntity<T> entity;
            if (kbCode == KbCode.OK) {
                entity = new KbEntity<>(KbCode.OK, readableMessage, (T) data);
                entity.setTotalPage(totalPage);
            } else {
                if (null == kbCode) {
                    kbCode = KbCode.ERROR;
                }
                if (StringUtils.isEmpty(readableMessage)) {
                    readableMessage = kbCode.getMessage();
                }
                entity = new KbEntity<>(kbCode, readableMessage, null);
                if (kbDebugs == null) {
                    kbDebugs = new LinkedList<>();
                    kbDebugs.add(kbDebugPrototypeProvider.createKbDebug(kbCode, kbCode.getMsg(), request));
                }
                entity.setKbDebugs(kbDebugs);
            }
            log(entity);
            if (alwaysTrimDebug) {
                entity.setKbDebugs(null);
            }
            return entity;
        }

        public List<KbDebug> createKbDebugList() {
            if (kbDebugs == null) {
                kbDebugs = new LinkedList<>();
                kbDebugs.add(kbDebugPrototypeProvider.createKbDebug(kbCode, kbCode.getMsg(), request));
            }
            return kbDebugs;
        }

        public KbEntityBuilder logWhenError(boolean logWhenError) {
            this.logWhenError = logWhenError;
            return this;
        }

        public KbEntityBuilder logWhenOk(boolean logWhenOk) {
            this.logWhenOk = logWhenOk;
            return this;
        }

        public KbEntityBuilder logExceptionTrace(boolean logExceptionTrace) {
            if (logExceptionTrace) {
                logWhenError = true;
            }
            this.logExceptionTrace = logExceptionTrace;
            return this;
        }

        public KbEntityBuilder logData(boolean logData) {
            if (logData) {
                logWhenOk = true;
            }
            this.logData = logData;
            return this;
        }

        public KbEntityBuilder logLevel(int logLevel) {
            this.logLevel = logLevel;
            return this;
        }

        private void log(KbEntity<?> entity) {
            if (logWhenOk && entity.isOk()) {
                StringBuilder sb = new StringBuilder();
                if (null != request) {
                    // 打印当前请求信息
                    sb.append(request.getMethod()).append(" ").append(request.getRequestURI()).append(" ");
                    int paramSize = request.getParameterMap().size();
                    if (paramSize != 0) {
                        request.getParameterMap().forEach((k, v) ->
                                sb.append(k).append("=").append(Arrays.toString(v)).append(","));
                        sb.deleteCharAt(sb.length() - 1);
                    }
                }
                if (logData) {
                    // 打印要返回的数据内容
                    if (sb.length() != 0) {
                        sb.append("\n");
                    }
                    sb.append(entity.getData().toString());
                }
                if (sb.length() != 0) {
                    l(sb.toString(), null);
                }
            }
            if (logWhenError && !entity.isOk()) {
                StringBuilder sb = new StringBuilder();
                sb.append(entity.getKbCode()).append("-")
                        .append(entity.getCode()).append("-")
                        .append(entity.getMsg());
                if (exception != null) {
                    sb.append(", exception: ").append(exception.getMessage());
                }

                List<KbDebug> kbDebugList = entity.getKbDebugs();
                if (kbDebugList != null && kbDebugList.size() != 0) {
                    sb.append("\n");
                    if (entity.getKbDebugs() != null && entity.getKbDebugs().size() != 0) {
                        for (int i = 0; i < kbDebugList.size(); i ++) {
                            // 不打印本层 debug 的 trace, 下面会单独打印
                            sb.append(kbDebugList.get(i).dump(i != 0));
                            if (i < kbDebugList.size() - 1) {
                                sb.append("\n");
                            }
                        }
                    }
                }

                if (exception instanceof KbException) {
                    int exceptionLogLevel = ((KbException) exception).getLevel();
                    if (exceptionLogLevel > logLevel) {
                        logLevel = exceptionLogLevel;
                    }
                }
                l(sb.toString(), logExceptionTrace ? exception : null);
            }
        }

        private void l(String msg, Throwable e) {
            switch (logLevel) {
                case KbException.LEVEL_DEBUG:
                    if (null == e) {
                        log.debug(msg);
                    } else {
                        log.debug(msg, e);
                    }
                    break;
                case KbException.LEVEL_INFO:
                    if (null == e) {
                        log.info(msg);
                    } else {
                        log.info(msg, e);
                    }
                    break;
                case KbException.LEVEL_WARN:
                    if (null == e) {
                        log.warn(msg);
                    } else {
                        log.warn(msg, e);
                    }
                    break;
                case KbException.LEVEL_ERROR:
                default:
                    if (null == e) {
                        log.error(msg);
                    } else {
                        log.error(msg, e);
                    }
                    break;
            }
        }

    }

    public interface KbDebugPrototypeProvider {
        KbDebug createKbDebug(KbCode kbCode, String debugMessage, HttpServletRequest request);
    }

    public static String getDebugMessageFromThrowable(Throwable e) {
        if (e instanceof MethodArgumentTypeMismatchException) {
            MethodArgumentTypeMismatchException me = (MethodArgumentTypeMismatchException) e;
            return e.getMessage() + " - " + me.getName();
        } else if (e instanceof MissingServletRequestParameterException) {
            MissingServletRequestParameterException me = (MissingServletRequestParameterException) e;
            return e.getMessage() + " - " + me.getParameterName();
        } else {
            return e.getMessage();
        }
    }

    private static List<String> generateTrace(Throwable e) {
        return generateTrace(e, null, 0);
    }

    private static List<String> generateTrace(Throwable e, List<String> trace, int traceLevel) {
        if (null == trace) {
            trace = new LinkedList<>();
        }
        trace.add("caused by: " + e.toString());
        StackTraceElement[] stackTraceElements = e.getStackTrace();
        for (int i = 0; i < e.getStackTrace().length; i ++) {
            if (i >= TRACE_MAX_LENGTH) {
                trace.add("... (" + (stackTraceElements.length - TRACE_MAX_LENGTH) + " more)");
                break;
            }
            trace.add(stackTraceElements[i].toString());
        }
        traceLevel ++;
        if (e.getCause() != null && traceLevel < TRACE_MAX_LEVEL) {
            return generateTrace(e.getCause(), trace, traceLevel);
        } else {
            return trace;
        }
    }

}
