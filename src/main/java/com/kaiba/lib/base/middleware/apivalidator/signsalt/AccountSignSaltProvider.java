package com.kaiba.lib.base.middleware.apivalidator.signsalt;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.lib.base.util.ServletRequestUtils;
import com.kaiba.lib.base.util.StringUtils;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import java.time.Duration;

/**
 * author: lyux
 * date: 2022-08-18
 */
public class AccountSignSaltProvider implements IKbSignSaltProvider {

    private final IUserService userService;

    // 签名算法的一个用途就是防止模拟访问.
    // 此处设置一个只要读取就延续 n 秒的缓存, 以防止大量模拟访问冲击数据库的情况.
    private final LoadingCache<Integer, String> userPasswordCache = Caffeine.newBuilder()
            .expireAfterAccess(Duration.ofSeconds(5))
            .build(this::requestUserPassword);

    public AccountSignSaltProvider(IUserService userService) {
        this.userService = userService;
    }

    @Override
    public KbSignType getSignType() {
        return KbSignType.ACCOUNT;
    }

    @Override
    public List<String> getSalts() {
        if (userService == null) {
            throw new KbException(KbCode.NOT_SUPPORT_YET, "no user service specified").li();
        }
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        if (request == null) {
            throw new KbException(KbCode.AUTH_FAIL_KBSIGN_INVALID, "not in a request context");
        }
        String userIdStr = request.getParameter("userId");
        if (null == userIdStr) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID, "check kbSign, invalid userId: " + userIdStr).li();
        }
        Integer userId = Integer.valueOf(userIdStr);
        String password = userPasswordCache.get(userId);
        return StringUtils.isEmpty(password) ? null : Lists.newArrayList(password);
    }

    private String requestUserPassword(Integer userId) {
        return userService.getUserPasswordById(userId).data().orElse("");
    }
}

