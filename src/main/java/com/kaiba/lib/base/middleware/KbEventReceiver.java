package com.kaiba.lib.base.middleware;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonSyntaxException;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import lombok.extern.slf4j.Slf4j;

/**
 * author: lyux
 * date: 18-9-17
 */
@Slf4j
public abstract class KbEventReceiver<T> {

    public static final String RECEIVER_METHOD_NAME = "onReceiveMessage";

    public void onReceiveMessage(String message) {
        log.debug("onReceiveMessage: "+message);
        onReceiveEvent(toModel(message, getEventType()));
    }

    public abstract void onReceiveEvent(T data);

    public abstract Class<T> getEventType();

    // -----------------------------------------------------------

    private static final Gson gson = new GsonBuilder().create();

    private <T> T toModel(String json, Class<T> clazz) {
        try {
            return getGson().fromJson(json, clazz);
        } catch (JsonSyntaxException e) {
            throw new KbException(KbCode.JSON_PARSE_FAIL, e);
        }
    }

    protected Gson getGson() {
        return gson;
    }

}
