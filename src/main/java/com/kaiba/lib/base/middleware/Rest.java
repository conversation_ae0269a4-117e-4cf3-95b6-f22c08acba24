package com.kaiba.lib.base.middleware;

import com.kaiba.lib.base.constant.Restful;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbDebug;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;

import java.util.Collections;
import java.util.List;

/**
 * author: lyux
 * date: 18-7-20
 *
 * 工具类, 用以生成给客户端的数据基本结构.
 *
 * deprecated: 应使用 {@link KbRes} 代替.
 */
@Deprecated
@Slf4j
public class Rest {

    private final KbRes kbRes;
    private boolean isResponseDebug;

    public Rest(KbRes kbRes, boolean debug) {
        this.kbRes = kbRes;
        this.isResponseDebug = debug;
    }

    public void setIsDebug(boolean debug) {
        this.isResponseDebug = debug;
    }

    public <T> KbResponse<T> res(int code, String msg, T data) {
        return new KbResponse<>(code, msg, data);
    }

    public <T> KbResponse<T> ok(T data) {
        return res(Restful.RSP_OK, "ok", data);
    }

    public <T> KbResponse<T> ok() {
        return ok(null);
    }

    public <T> KbResponse<List<T>> okPage(Page<T> data) {
        KbResponse<List<T>> entity = new KbResponse<>(Restful.RSP_OK, "ok", data.getContent());
        entity.setTotalPage((long) data.getTotalPages());
        entity.setTotalCount(data.getTotalElements());
        return entity;
    }

    public <T> KbResponse<T> fromEntity(KbEntity<T> entity) {
        KbResponse<T> response = new KbResponse<>(entity);
        if (!entity.isOk()) {
            List<KbDebug> debugList = kbRes.on(entity).createKbDebugList();
            if (isResponseDebug) {
                response.setDebugs(debugList);
            }
            log.error(KbExceptionHelper.dump(response, debugList));
        }
        response.setTotalPage(entity.getTotalPage());
        response.setTotalCount(entity.getTotalCount());
        return response;
    }

    public <T> KbResponse<T> err(int code, String msg) {
        KbResponse<T> response = res(code, msg, null);
        if (isResponseDebug) {
            response.setDebugs(Collections.singletonList(
                    kbRes.createKbDebug(KbCode.REQUEST_FAIL, null, null)));
        }
        return response;
    }

    public <T> KbResponse<T> err(String msg) {
        return err(Restful.RSP_FAIL, msg);
    }

    public <T> KbResponse<T> err() {
        return err(Restful.RSP_FAIL, Restful.REQ_FAIL);
    }

    public <T> KbResponse<T> kbCode(KbCode kbCode) {
        return new KbResponse<>(kbCode);
    }

    public <T> KbResponseBuilder<T> on() {
        return new KbResponseBuilder<>();
    }

    // -----------------------------------------------

    public static class KbResponseBuilder<T> {

        private KbResponse<T> response;

        public KbResponseBuilder() {
            this.response = new KbResponse<>();
        }

        public KbResponseBuilder<T> state(int state) {
            response.setState(state);
            return this;
        }

        public KbResponseBuilder<T> kbCode(Integer kbCode) {
            response.setKbCode(kbCode);
            return this;
        }

        public KbResponseBuilder<T> readableMessage(String msg) {
            response.setMsg(msg);
            return this;
        }

        public KbResponseBuilder<T> data(T data) {
            response.setData(data);
            return this;
        }

        public KbResponseBuilder<T> debugs(List<KbDebug> debugs) {
            response.setDebugs(debugs);
            return this;
        }

        public KbResponseBuilder<T> totalPage(Long totalPage) {
            response.totalPage(totalPage);
            return this;
        }

        public KbResponseBuilder<T> totalCount(Long totalCount) {
            response.totalCount(totalCount);
            return this;
        }

        public KbResponse<T> create() {
            return response;
        }
    }

}
