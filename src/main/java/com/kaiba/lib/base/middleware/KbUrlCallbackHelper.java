package com.kaiba.lib.base.middleware;

import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.constant.KbHttpHeaders;
import com.kaiba.lib.base.constant.KbHttpParams;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.domain.common.KbCallback;
import com.kaiba.lib.base.middleware.apivalidator.AuthKbSignChecker;
import com.kaiba.lib.base.middleware.executor.IKbTaskSchedulerExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * author: lyux
 * date: 2020-09-09
 *
 * TODO: 服务重启后回调重试将失效
 */
@Slf4j
public class KbUrlCallbackHelper {

    private static final long RETRY_SKIP_INTERVAL = TimeUnit.MINUTES.toMillis(3);

    private final IKbParamSignSaltProvider kbSignSaltProvider;
    private final IKbTaskSchedulerExecutor kbTaskScheduler;
    private final RestTemplate restTemplate;

    public KbUrlCallbackHelper(
            IKbParamSignSaltProvider kbSignSaltProvider
    ) {
        this(new RestTemplate(), kbSignSaltProvider, null);
    }

    public KbUrlCallbackHelper(
            IKbParamSignSaltProvider kbSignSaltProvider,
            IKbTaskSchedulerExecutor kbTaskScheduler
    ) {
        this(new RestTemplate(), kbSignSaltProvider, kbTaskScheduler);
    }

    public KbUrlCallbackHelper(
            RestTemplate restTemplate,
            IKbParamSignSaltProvider kbSignSaltProvider,
            IKbTaskSchedulerExecutor kbTaskScheduler
    ) {
        this.kbSignSaltProvider = kbSignSaltProvider;
        this.kbTaskScheduler = kbTaskScheduler;
        this.restTemplate = restTemplate;
    }

    public void callback(List<KbCallback> callbacks, Map<String, String> params) {
        if (callbacks == null || callbacks.size() == 0) {
            return;
        }
        for (KbCallback callback : callbacks) {
            Optional<KbSignType> opSignType = KbSignType.valueOf(callback.getSignType());
            if (!opSignType.isPresent()) {
                log.error("unknown sign type: " + callback);
                continue;
            }
            KbSignType signType = opSignType.get();
            if (kbTaskScheduler == null) {
                String salt = kbSignSaltProvider.getSalt(signType, params);
                callbackRequest(callback, params, salt, 0);
            } else {
                kbTaskScheduler.execute(() -> {
                    String salt = kbSignSaltProvider.getSalt(signType, params);
                    callbackRequest(callback, params, salt, 0);
                });
            }
        }
    }

    public void callbackRequest(
            KbCallback callback, Map<String, String> params, String salt, int retryCount) {

        long current = System.currentTimeMillis();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set(KbHttpHeaders.KB_EP.getHeaderName(), Integer.toString(KbEndpoint.BACKEND.getValue()));
        MultiValueMap<String, String> paramMap = new LinkedMultiValueMap<>();
        paramMap.setAll(params);
        paramMap.set(KbHttpParams.KB_SIGN_TIME.getName(), Long.toString(current));
        Map<String, String[]> signParams = new HashMap<>();
        paramMap.forEach((k, v) -> signParams.put(k, v.toArray(new String[0])));
        String sign = AuthKbSignChecker.generateSignature(signParams, salt);
        paramMap.set(KbHttpParams.KB_SIGN.getName(), sign);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(paramMap, headers);

        boolean success = false;
        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    callback.getUrl(), HttpMethod.POST, requestEntity, String.class);
            int status = response.getStatusCodeValue();
            log.info("callback send request to " + callback.getUrl() + ", status: " + status);
            if (callback.getSuccessStatus() == null || callback.getSuccessStatus().size() == 0) {
                success = (status == HttpStatus.OK.value());
            } else {
                success = callback.getSuccessStatus().contains(status);
            }
        } catch (RestClientException e) {
            log.warn("callback send request to " + callback.getUrl() + " fail", e);
        }

        if (!success && callback.getRetryMax() != null && callback.getRetryMax() > 0) {
            int nextRetryCount = retryCount + 1;
            long nextRetryTime = current + nextRetryCount * RETRY_SKIP_INTERVAL;
            if (kbTaskScheduler == null) {
                log.info("callback retry send request to " + callback.getUrl() + ", no scheduler specified, will not retry");
            } else if (callback.getRetryExpireAt() != null && callback.getRetryExpireAt() < nextRetryTime) {
                log.info("callback retry send request to " + callback.getUrl() +
                        ", retry exceed time limit: " + nextRetryTime + " -> " + callback.getRetryExpireAt());
            } else if (nextRetryCount > callback.getRetryMax()) {
                log.info("callback retry send request to " + callback.getUrl() +
                        ", retry exceed max: " + callback.getRetryMax());
            } else {
                log.info("callback retry send request to " + callback.getUrl() + ", at " + nextRetryTime);
                kbTaskScheduler.schedule(
                        () -> callbackRequest(callback, params, salt, nextRetryCount), nextRetryTime);
            }
        }
    }

    public interface IKbParamSignSaltProvider {
        /**
         * 根据签名类型获取盐值
         */
        String getSalt(KbSignType signType, Map<String, String> params);
    }

}
