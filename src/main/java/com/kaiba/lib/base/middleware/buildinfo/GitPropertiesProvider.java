package com.kaiba.lib.base.middleware.buildinfo;

import com.google.gson.JsonSyntaxException;
import com.kaiba.lib.base.util.GsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/**
 * author: lyux
 * date: 2020-05-21
 */
@Slf4j
public final class GitPropertiesProvider {

    private String data;
    private GitProperties gitProperties;

    public GitPropertiesProvider(String gitPropertiesFile) {
        readGitProperties(gitPropertiesFile);
    }

    public GitPropertiesProvider() {
        this("git.properties");
    }

    public GitProperties get() {
        return gitProperties;
    }

    public String getData() {
        return data;
    }

    private void readGitProperties(String gitPropertiesFile) {
        ClassLoader classLoader = getClass().getClassLoader();
        InputStream inputStream = classLoader.getResourceAsStream(gitPropertiesFile);
        if (inputStream == null) {
            log.warn("git properties could not find properties file: " + gitPropertiesFile);
            gitProperties = new GitProperties();
        } else {
            try {
                data = readFromInputStream(inputStream);
                gitProperties = GsonUtils.getGson().fromJson(data, GitProperties.class);
            } catch (JsonSyntaxException e) {
                log.warn("git properties could not be retrieved, parse fail: " + data, e);
                gitProperties = new GitProperties();
            } catch (IOException e) {
                log.warn("git properties could not be retrieved", e);
                gitProperties = new GitProperties();
            }
        }
    }

    private static String readFromInputStream(InputStream inputStream) throws IOException {
        StringBuilder resultStringBuilder = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            while ((line = br.readLine()) != null) {
                resultStringBuilder.append(line).append("\n");
            }
        }
        return resultStringBuilder.toString();
    }

}
