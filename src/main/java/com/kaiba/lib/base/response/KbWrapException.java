package com.kaiba.lib.base.response;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * author: lyux
 * date: 18-10-19
 */
@Slf4j
public class KbWrapException extends KbException {

    private List<KbDebug> kbDebugs;

    public KbWrapException(KbCode kbcode, List<KbDebug> kbDebugs) {
        super(kbcode);
        setLevel(KbException.LEVEL_INFO);
        this.kbDebugs = kbDebugs;
    }

    public List<KbDebug> getKbDebugs() {
        return kbDebugs;
    }

}
