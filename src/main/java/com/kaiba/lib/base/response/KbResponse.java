package com.kaiba.lib.base.response;


import com.kaiba.lib.base.constant.Restful;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * author: lyux
 * date: 18-7-20
 *
 * 返回给客户端的数据基本结构. 若要在微服务间传递数据, 请使用 {@link KbEntity}.
 */
@Data
@NoArgsConstructor
public class KbResponse<T> {

    private Long totalPage;

    private Long totalCount;

    private int state;

    private Integer kbCode;

    private String msg;

    private T data;

    private List<KbDebug> debugs;

    public KbResponse(int state, String msg, T data) {
        this.state = state;
        this.msg = msg;
        this.data = data;
    }

    public KbResponse(KbCode kbCode) {
        if (kbCode == KbCode.OK) {
            this.state = Restful.RSP_OK;
        } else {
            this.state = Restful.RSP_FAIL;
            this.kbCode = kbCode.getCode();
            this.msg = kbCode.getMessage();
        }
    }

    public KbResponse(KbEntity<T> entity) {
        if (entity.isOk()) {
            this.state = Restful.RSP_OK;
            this.data = entity.getData();
            this.totalPage = entity.getTotalPage();
            this.msg = entity.getMsg();
        } else {
            KbCode kbCode = entity.getKbCode();
            this.state = Restful.RSP_FAIL;
            this.kbCode = entity.getCode();
            this.msg = entity.getMsg() == null ? kbCode.getMessage() : entity.getMsg();
            this.data = entity.getData();
        }
    }

    public KbResponse<T> totalPage(Long totalPage) {
        this.totalPage = totalPage;
        return this;
    }

    public KbResponse<T> totalCount(Long totalCount) {
        this.totalCount = totalCount;
        return this;
    }

    public static <T> KbResponse<T> withData(T data) {
        return new KbResponse<>(Restful.RSP_OK, "ok", data);
    }

    public static KbResponse<?> withErrorMessage(String message) {
        return new KbResponse<>(Restful.RSP_FAIL, message, null);
    }

}
