package com.kaiba.lib.base.response;

import lombok.Data;
import lombok.ToString;
import org.springframework.data.domain.Page;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * author: lyux
 * date: 18-8-27
 *
 * 微服务节点间传递数据的基本结构.
 */
@Data
@ToString
public class KbEntity<T> {

    /** {@link #state} 字段值: 请求成功 */
    public static final int RSP_OK = 200;
    /** {@link #state} 字段值: 请求失败 */
    public static final int RSP_FAIL = 300;
    /** {@link #state} 字段值: 令牌过期 */
    public static final int RSP_LOGIN_EXPIRE = 310;

    // -----------------------------------------------------

    /** 对应 {@link KbCode} */
    private int code;

    /** 为了兼容老版接口而保留的字段. 使用方应尽量使用 {@link #code} 判断状态. */
    private int state;

    /** 用户可读的消息提示 */
    private String msg;

    /** 如果请求需要分页, 此为总页数. 如果不是分页请求, 则值为 null */
    private Long totalPage;

    /** 如果请求需要分页, 此为总个数. 如果不是分页请求, 则值为 null */
    private Long totalCount;

    /** 数据 */
    private T data;

    /** 如果接口有错误, 此为 debug 信息. 正式环境不会出现此字段 */
    private List<KbDebug> kbDebugs;

    /** 对应 kbCode. 此字段不参与序列化  */
    private transient KbCode kbCode;

    public KbEntity(KbCode kbCode, String message, T data) {
        this.code = kbCode.getCode();
        this.kbCode = kbCode;
        this.state = getStateByKbCode(kbCode);
        this.msg = null == message ? kbCode.getMessage() : message;
        this.data = data;
    }

    public KbEntity(KbCode kbCode) {
        this(kbCode, null, null);
    }

    public KbEntity(T data) {
        this(KbCode.OK, null, data);
    }

    public KbEntity() {
        this(KbCode.OK);
    }

    public void setKbCode(KbCode kbCode) {
        this.kbCode = kbCode;
        this.code = kbCode.getCode();
        this.state = getStateByKbCode(kbCode);
    }

    public void setCode(int code) {
        this.code = code;
        this.kbCode = KbCode.valueOf(code).orElse(KbCode.ERROR);
        this.state = getStateByKbCode(kbCode);
    }

    public KbCode getKbCode() {
        if (kbCode == null || kbCode.getCode() != code) {
            kbCode = KbCode.valueOf(code).orElse(KbCode.ERROR);
        }
        return kbCode;
    }

    public List<KbDebug> getKbDebugs() {
        return kbDebugs;
    }

    /** 添加用户可读的消息提示 */
    public KbEntity<T> r(String msg) {
        this.msg = msg;
        return this;
    }

    public KbEntity<T> addKbDebug(KbDebug debug) {
        if (debug == null) {
            return this;
        }
        if (kbDebugs == null) {
            kbDebugs = new LinkedList<>();
        }
        kbDebugs.add(0, debug);
        return this;
    }

    public KbEntity<T> addKbDebug(List<KbDebug> debugs) {
        if (debugs == null || debugs.size() == 0) {
            return this;
        }
        if (kbDebugs == null) {
            kbDebugs = new LinkedList<>();
        }
        kbDebugs.addAll(0, debugs);
        return this;
    }

    /**
     * 设置分页信息
     * @param totalPage 相关数据列表的总页数
     * @param totalCount 相关数据列表的总条目数
     */
    public KbEntity<T> setPageInfo(long totalPage, long totalCount) {
        this.totalPage = totalPage;
        this.totalCount = totalCount;
        return this;
    }

    /**
     * 设置分页信息
     * @param dataPage 分页数据
     */
    public KbEntity<T> setPageInfo(Page<?> dataPage) {
        this.totalPage = (long) dataPage.getTotalPages();
        this.totalCount = dataPage.getTotalElements();
        return this;
    }

    /**
     * 检查 code 是否为 {@link KbCode#OK}. 若不是则抛出异常
     */
    public KbEntity<T> check() {
        if (KbCode.OK.getCode() != code) {
            throw new KbWrapException(getKbCode(), kbDebugs);
        }
        return this;
    }

    /**
     * 检查 code 是否为 {@link KbCode#OK} 或数据未找到类错误. 若不是则抛出异常
     */
    public KbEntity<T> checkOkOrNotFound() {
        if (!isOkOrNotFound()) {
            throw new KbWrapException(getKbCode(), kbDebugs);
        }
        return this;
    }

    /**
     * 在请求成功时, 根据 data 执行一个动作
     */
    public KbEntity<T> doOnOk(Consumer<? super T> action) {
        if (KbCode.OK.getCode() == code) {
            action.accept(data);
        }
        return this;
    }

    /**
     * 在请求失败时, 根据 data 执行一个动作
     */
    public KbEntity<T> doOnFail(Consumer<? super T> action) {
        if (KbCode.OK.getCode() != code) {
            action.accept(data);
        }
        return this;
    }

    /**
     * 在请求返回特定 code 时, 根据 data 执行一个动作
     */
    public KbEntity<T> doOnCode(KbCode kbCode, Consumer<? super T> action) {
        if (kbCode.getCode() == code) {
            action.accept(data);
        }
        return this;
    }

    /**
     * 在 data 非空时, 根据 data 执行一个动作
     */
    public KbEntity<T> peek(Consumer<? super T> action) {
        if (data != null) {
            action.accept(data);
        }
        return this;
    }

    /**
     * 对 entity 本身执行一个动作
     */
    public KbEntity<T> peekEntity(Consumer<KbEntity<T>> action) {
        action.accept(this);
        return this;
    }

    /**
     * 在 data 为空时, 生成一个新数据代替之
     */
    public KbEntity<T> dataOrElse(Supplier<? extends T> action) {
        if (data == null) {
            data = action.get();
        }
        return this;
    }

    /**
     * 将 data 转化为另一个结构
     */
    public <U> KbEntity<U> map(Function<? super T, ? extends U> mapper) {
        U newData = data == null ? null : mapper.apply(data);
        KbEntity<U> newEntity = new KbEntity<>(this.getKbCode(), this.getMsg(), newData);
        newEntity.setTotalCount(this.getTotalCount());
        newEntity.setTotalPage(this.getTotalPage());
        newEntity.setKbDebugs(this.getKbDebugs());
        return newEntity;
    }

    /**
     * 转化为另一个 KbEntity
     */
    public<U> KbEntity<U> flatMap(Function<? super T, KbEntity<U>> mapper) {
        Objects.requireNonNull(mapper);
        if (data == null) {
            return map(data -> null);
        } else {
            return mapper.apply(data);
        }
    }

    /**
     * 根据自身数据返回一个 KbCode, 如果不是 {@link KbCode#OK},
     * 则将数据相关字段置为空, 并返回 KbCode 代表的错误.
     */
    public KbEntity<T> filterDropData(Function<KbEntity<T>, KbCode> mapper) {
        KbCode filteredKbCode = mapper.apply(this);
        if (filteredKbCode != null) {
            setKbCode(filteredKbCode);
            if (filteredKbCode != KbCode.OK) {
                setData(null);
                setTotalPage(null);
                setTotalCount(null);
            }
        }
        return this;
    }

    /**
     * 根据自身数据返回一个 KbCode, 如果不是 {@link KbCode#OK},
     * 则返回 KbCode 代表的错误, data 保持不变.
     */
    public KbEntity<T> filterKeepData(Function<KbEntity<T>, KbCode> mapper) {
        KbCode filteredKbCode = mapper.apply(this);
        if (filteredKbCode != null) {
            setKbCode(filteredKbCode);
        }
        return this;
    }

    /**
     * 检查 KbCode 是否为 {@link KbCode#OK},
     */
    public boolean isOk() {
        return KbCode.OK.getCode() == code;
    }

    /**
     * 检查 KbCode 是否为 {@link KbCode#OK} 或 {@link KbCode#RESOURCE_NOT_FOUND}. 同时会检查 parentCode.
     */
    public boolean isOkOrNotFound() {
        if (KbCode.OK.getCode() == code || KbCode.RESOURCE_NOT_FOUND.getCode() == code) {
            return true;
        } else {
            KbCode parentCode = getKbCode().getParent();
            return parentCode == KbCode.RESOURCE_NOT_FOUND;
        }
    }

    /**
     * 首先检查 code 是否为 {@link KbCode#OK} 或 {@link KbCode#RESOURCE_NOT_FOUND}.
     * 若不是则抛出异常.
     * 若是则返回 Optional<T>.
     */
    public Optional<T> data() {
        if (KbCode.OK.getCode() == code) {
            return Optional.ofNullable(getData());
        } else if (KbCode.RESOURCE_NOT_FOUND.getCode() == code) {
            return Optional.empty();
        } else {
            KbCode parentCode = getKbCode().getParent();
            if (parentCode == KbCode.RESOURCE_NOT_FOUND) {
                return Optional.empty();
            }
            return Optional.of(dataOrThrow());
        }
    }

    /**
     * 忽略错误, 返回 Optional<T>.
     */
    public Optional<T> dataIgnoreError() {
        return Optional.ofNullable(data);
    }

    /**
     * 首先检查 code 是否为 {@link KbCode#OK}, 若不是则抛出异常.
     * 然后检查 data 是否为空, 若是则抛出异常, 异常 code 为 {@link KbCode#RESOURCE_NOT_FOUND}.
     * 若检查通过, 则返回 data.
     */
    public T dataOrThrow() {
        if (null == data) {
            if (KbCode.OK.getCode() != code) {
                throw new KbWrapException(getKbCode(), kbDebugs).setReadableMessage(msg);
            } else {
                throw new KbException(KbCode.RESOURCE_NOT_FOUND).setReadableMessage(msg);
            }
        }
        return data;
    }

    // -----------------------------------------------------

    public static int getStateByKbCode(KbCode kbCode) {
        if (kbCode == KbCode.OK) {
            return RSP_OK;
        } else if (kbCode == KbCode.AUTH_FAIL_TOKEN_EXPIRED || kbCode == KbCode.AUTH_FAIL_TOKEN_INVALID) {
            return RSP_LOGIN_EXPIRE;
        } else {
            return RSP_FAIL;
        }
    }

}
