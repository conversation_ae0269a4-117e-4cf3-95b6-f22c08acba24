package com.kaiba.lib.base.response;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import com.kaiba.lib.base.constant.KbModule;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 18-8-30
 */
public enum KbCode implements IKbEnumValueGetter {

    OK(0, "ok", "请求成功"),
    ERROR(100, "error", "请求失败"),
    EXCEPTION(101, "exception", "请求错误"),
    ILLEGAL_ARGUMENT(102, "argument invalid", "参数错误"),
    ILLEGAL_STATE(103, "illegal state", "状态错误"),
    JSON_PARSE_FAIL(112, "json parse fail", "数据解析错误"),
    RESOURCE_NOT_FOUND(120, "resource not found", "资源不存在"),
    RESOURCE_IS_REFERENCED(121, "resource is referenced", "资源被引用"),
    RESOURCE_ALREADY_EXIST(122, "resource already exist", "资源已存在"),
    NOT_SUPPORT_YET(140, "feature not supported yet, still working on it", "功能正在开发, 敬请期待"),
    NOT_SUPPORT_ANY_MORE(141, "feature not supported any more", "功能已不再支持或需要升级, 请尽快升级新版本"),
    REQUIRE_UPDATE(142, "feature require client update", "您需要升级新版本才可以使用该功能"),
    TEMPORARILY_SHUT_DOWN(143, "feature temporarily shut down", "功能维护中, 敬请期待"),

    DATA_EXCEED_LIMIT(181, "data exceed limit", "超出限制"),
    DATA_MALFORMED(182, "data malformed", "数据格式错误"),

    NET_ERROR(151, "network error", "网络错误"),
    NET_TIMEOUT(152, "network timeout", "网络超时"),
    NET_NOT_AVAIL(153, "network not available", "网络不可用"),

    REQUEST_NO_HANDLER(404, "request no handler", "请求路径错误"),
    REQUEST_FAIL(500, "request fail", "请求失败"),
    REQUEST_UP_STREAM_FAIL(503, "request up stream fail", "服务出了点问题..."),
    REQUEST_PARAM_INVALID(511, "request param invalid", "请求参数错误"),
    REQUEST_PARAM_MISSING(512, "request param missing", "缺少请求参数"),
    REQUEST_TOO_SOON(513, "request too soon", "请勿频繁请求"),
    REQUEST_IGNORED(514, "request ignored", "非必要的请求"),
    REQUEST_FALLBACK(521, "hystrix fallback", "服务器出了点问题..."),
    REQUEST_RATE_LIMIT(522, "request rate limit", "服务繁忙(1),请稍后再试"),
    REQUEST_RETRY_LATER(523, "retry later", "服务繁忙(2),请稍后再试"),

    APP_MODULE_ILLEGAL_FORMAT(KbModule.APP_MODULE, 1, "illegal app module data format", "界面数据格式错误"),
    APP_MODULE_PAGE_NOT_FOUND(KbModule.APP_MODULE, 2, "page data not found", "界面数据不存在", RESOURCE_NOT_FOUND),
    APP_MODULE_VERSION(KbModule.APP_MODULE, 3, "pageVersion check fail", "版本格式非法"),

    ENROLL_NOT_EXIST(KbModule.ENROLL, 1, "enroll not exists", "报名活动不存在", RESOURCE_NOT_FOUND),
    ENROLL_MODULE_NOT_EXIST(KbModule.ENROLL, 2, "enroll module not exists", "报名活动组件不存在", RESOURCE_NOT_FOUND),
    ENROLL_MODULE_DATA_NOT_EXIST(KbModule.ENROLL, 3, "enroll module data not exists", "报名活动组件用户数据不存在", RESOURCE_NOT_FOUND),

    FLOAT_ICON_NOT_EXISTS(KbModule.FLOAT_VIEW, 101, "float icon not exists", "浮标不存在", RESOURCE_NOT_FOUND),
    FLOAT_ICON_SIGN_TIME_CONFLICT(KbModule.FLOAT_VIEW, 121, "float icon sign time conflict", "签发失败, 起止时间和其他浮标有冲突"),
    FLOAT_WINDOW_NOT_EXISTS(KbModule.FLOAT_VIEW, 201, "float window not exists", "浮窗不存在", RESOURCE_NOT_FOUND),
    FLOAT_WINDOW_SIGN_TIME_CONFLICT(KbModule.FLOAT_VIEW, 221, "float icon sign time conflict", "签发失败, 起止时间和其他浮窗有冲突"),

    AUTH_PERMISSION_DENIED(KbModule.AUTH, 1, "permission denied", "权限不足"),
    AUTH_NOT_OWNER(KbModule.AUTH, 2, "only resource owner could do this operation", "所有者才可以处置该资源"),
    AUTH_FORBIDDEN_USER(KbModule.AUTH, 11, "forbidden user", "用户已被禁言"),
    AUTH_FORBIDDEN_USER_LOGIN(KbModule.AUTH, 12, "forbidden user", "禁止登录"),
    AUTH_FORBIDDEN_DEVICE(KbModule.AUTH, 13, "forbidden user", "设备已被封禁"),
    AUTH_ACS_NOT_PRESENT(KbModule.AUTH, 21, "acs not present", "缺少设备参数"), // 防水墙, 未提供参数
    AUTH_ACS_SUSPECT(KbModule.AUTH, 22, "acs suspect", "设备存在风险, 请进行验证"), // 防水墙, 疑似
    AUTH_ACS_BLOCKED(KbModule.AUTH, 23, "acs blocked", "检测到当前设备环境存在风险, 暂时无法操作"), // 防水墙, 拒绝
    AUTH_ROLE_COLLIDE(KbModule.AUTH, 51, "role collide with each other", "角色存在冲突"),

    AUTH_NO_PERMISSION(KbModule.AUTH, 100, "", "没有权限"),
    AUTH_PERMISSION_NOT_EXISTS(KbModule.AUTH, 101, "permission not exists", "权限不存在", RESOURCE_NOT_FOUND),
    AUTH_ROLE_NOT_EXISTS(KbModule.AUTH, 102, "role not exists", "角色不存在", RESOURCE_NOT_FOUND),
    AUTH_REQUIRE_FURTHER_AUTH(KbModule.AUTH, 200, "require further auth", "尚未授权"),
    AUTH_REQUIRE_REFRESH_LOGIN(KbModule.AUTH, 201, "require refresh login state", "请重新登录"),
    AUTH_FAIL(KbModule.AUTH, 300, "auth fail", "鉴权失败"),
    AUTH_FAIL_PARAM_INVALID(KbModule.AUTH, 301, "auth required param not fit", "鉴权参数错误"),
    AUTH_FAIL_TOKEN_CHECK_FAIL(KbModule.AUTH, 311, "login token check fail", "缺少令牌参数"),
    AUTH_FAIL_TOKEN_EXPIRED(KbModule.AUTH, 312, "login token expired", "令牌已过期"),
    AUTH_FAIL_TOKEN_INVALID(KbModule.AUTH, 313, "login token invalid", "无效的令牌"),
    AUTH_FAIL_VCODE_WRONG(KbModule.AUTH, 351, "vcode check fail", "验证码不正确"),
    AUTH_FAIL_VCODE_MOBILE_INVALID(KbModule.AUTH, 352, "vcode mobile invalid", "验证码发送失败,手机号不可用"),
    AUTH_FAIL_KBSIGN_INVALID(KbModule.AUTH, 361, "kbSign check fail", "签名错误"),
    AUTH_FAIL_KBSIGN_EXPIRED(KbModule.AUTH, 362, "kbSign expired", "签名已过期"),

    USER_NOT_EXISTS(KbModule.USER, 1, "user not exists", "用户不存在", RESOURCE_NOT_FOUND),
    USER_MOBILE_ALREADY_EXISTS(KbModule.USER, 2, "user mobile already exists", "该手机号已注册, 请用此号登录或找回密码"),
    USER_NAME_ALREADY_EXISTS(KbModule.USER, 3, "user name already exists", "用户名已存在"),
    USER_NOT_BIND_MOBILE(KbModule.USER, 4, "user not bind mobile", "根据相关部门规定, 请绑定手机号后再进行互动"),
    USER_LOGIN_FAIL(KbModule.USER, 11, "login fail", "登录失败"),
    USER_LOGIN_WITH_PASSWORD_FAIL(KbModule.USER, 12, "account or password is wrong", "账号或密码错误"),
    USER_WRONG_VCODE(KbModule.USER, 13, "wrong vcode", "验证码错误"),
    USER_PSD_SETTED(KbModule.USER, 14, "password bas been set", "已经设置过密码了"),
    USER_WRONG_WX_CREDENCE (KbModule.USER, 15, "wrong wx credence", "微信凭证错误"),
    USER_QQ_ALREADY_EXISTS(KbModule.USER, 16, "qq already exists", "该QQ号已经被绑定"),
    USER_WX_ALREADY_EXISTS(KbModule.USER, 17, "wx already exists", "该微信号已经被绑定"),
    USER_WRONG_QQ_CREDENCE (KbModule.USER, 18, "wrong qq credence", "QQ凭证错误"),
    USER_UNREGISTER_CREDENCE (KbModule.USER, 19, "credence unregister", "账号未注册"),
    USER_LOGIN_CITY_CHANGE(KbModule.USER, 20, "user login city changed", "登录城市发生变更"),
    USER_LOGIN_UNQUALIFIED_PASSWORD(KbModule.USER, 21, "unqualified password", "密码不符合要求"),
    USER_WX_SNAPSHOT(KbModule.USER, 22, "wx snapshot user 1", "微信虚拟用户"),
    USER_LOGOFF_INFO(KbModule.USER, 51, "account was applied logoff", "你的账号申请了永久注销，是否取消？"),

    CAR_NOT_EXISTS(KbModule.CAR, 1, "car not exists", "车型不存在", RESOURCE_NOT_FOUND),
    CAR_BRAND_NOT_EXISTS(KbModule.CAR, 2, "car brand not exists", "车系不存在", RESOURCE_NOT_FOUND),

    CITY_NOT_EXISTS(KbModule.SITE, 1, "city not exists", "城市不存在", RESOURCE_NOT_FOUND),
    SITE_NOT_EXISTS(KbModule.SITE, 2, "site not exists", "电台不存在", RESOURCE_NOT_FOUND),
    SITE_LIST_EMPTY(KbModule.SITE, 3, "empty site list", "电台列表为空", RESOURCE_NOT_FOUND),
    SITE_CHINA_NOT_EXISTS(KbModule.SITE, 4, "site china not exists", "开吧电台不存在", RESOURCE_NOT_FOUND),

    NOTE_ERROR(KbModule.NOTE, 1, "note error", "帖子模块错误"),
    NOTE_AUTH_PERMISSION_DENIED(KbModule.NOTE, 2, "note action not permitted", "不允许该操作"),
    NOTE_NOT_EXISTS(KbModule.NOTE, 11, "note not exists", "帖子不存在或已被删除", RESOURCE_NOT_FOUND),
    NOTE_THREAD_EXCEED(KbModule.NOTE, 12, "note related thread count exceed", "帖子关联的板块已达上限"),
    NOTE_RULE_NOT_MET(KbModule.NOTE, 13, "note content not met thread rule", "帖子内容错误"),
    NOTE_COMMENT_NOT_EXISTS(KbModule.NOTE, 31, "note comment not exists", "帖子回复不存在或已被删除", RESOURCE_NOT_FOUND),
    NOTE_THREAD_NOT_EXISTS(KbModule.NOTE, 51, "thread not exists", "帖子板块不存在", RESOURCE_NOT_FOUND),
    NOTE_THREAD_HOT_MAX(KbModule.NOTE, 53, "thread hot exceed max count", "板块热议已达上限"),
    NOTE_THREAD_TOP_MAX(KbModule.NOTE, 54, "thread top exceed max count", "板块置顶已达上限"),
    NOTE_THREAD_CONDITION_READONLY(KbModule.NOTE, 81, "thread readonly", "本板块当前不允许发帖"),
    NOTE_THREAD_CONDITION_CLOSED(KbModule.NOTE, 82, "thread closed", "本板块暂时关闭"),

    EMCEE_NOT_EXISTS(KbModule.EMCEE, 1, "emcee not exists", "该主持人不存在",RESOURCE_NOT_FOUND),

    CIRCLE_THREAD_NOT_EXISTS(KbModule.CIRCLE, 11, "circle thread not exists", "板块不存在", RESOURCE_NOT_FOUND),

    VIDEO_LIVE_NOT_EXISTS(KbModule.VIDEO_LIVE, 1, "videolive not exists", "直播间不存在", RESOURCE_NOT_FOUND),

    ISSUE_NOT_EXISTS(KbModule.ISSUE, 1, "issue not exists", "问答不存在", RESOURCE_NOT_FOUND),
    ISSUE_EXPERT_NOT_EXISTS(KbModule.ISSUE, 2, "expert not exists", "专家不存在", RESOURCE_NOT_FOUND),
    ISSUE_ALREADY_DISPATCHED(KbModule.ISSUE, 3, "issue already dispatched", "问答已指派给其他专家作答"),
    ISSUE_REFUND_FAIL(KbModule.ISSUE, 4, "issue refund fail", "问答退款失败"),
    ISSUE_LAWYER_NOT_EXISTS(KbModule.ISSUE, 11, "lawyer not exists", "律师不存在", RESOURCE_NOT_FOUND),
    ISSUE_TASK_WRONG_OWNER(KbModule.ISSUE, 21, "expert is not owner of the task", "问答指派错误"),
    ISSUE_TASK_INVALID_STATE(KbModule.ISSUE, 22, "issue task state invalid", "问答状态错误", ILLEGAL_STATE),
    ISSUE_TASK_NOT_EXISTS(KbModule.ISSUE, 23, "issue task not exists", "问答派单不存在"),

    ACCOUNTING_ERROR(KbModule.ACCOUNTING, 1, "accounting system error", "系统错误"),
    ACCOUNTING_AUDIT(KbModule.ACCOUNTING, 2, "account system is being audited", "资金系统正在审计中，稍后再试"),
    ACCOUNTING_ACCOUNT_NOT_EXIST(KbModule.ACCOUNTING, 11, "account doesn't exist", "账户不存在", RESOURCE_NOT_FOUND),
    ACCOUNTING_ACCOUNT_BALANCE_INSUFFICIENT(KbModule.ACCOUNTING, 12, "account balance insufficient", "余额不足"),
    ACCOUNTING_TRANSACTION_ERROR(KbModule.ACCOUNTING, 51, "transaction fault", "交易失败"),
    ACCOUNTING_TRANSACTION_NOT_EXIST(KbModule.ACCOUNTING, 52, "transaction doesn't exist", "交易不存在", RESOURCE_NOT_FOUND),
    ACCOUNTING_TRANSACTION_BUSINESS_ID_EXISTS(KbModule.ACCOUNTING, 53, "business id already exists", "重复的业务id"),
    ACCOUNTING_AMOUNT_UNEQUAL(KbModule.ACCOUNTING, 54, "payment add discount is not equal amount", "账户收支不平衡"),
    ACCOUNTING_WITHDRAW_REACH_LIMIT(KbModule.ACCOUNTING, 55, "reach withdraw limit", "提现已达上限"),
    ACCOUNTING_TRANSACTION_PATH_NOT_MATCH(KbModule.ACCOUNTING, 56, "transaction path not match", "业务路径错误"),
    ACCOUNTING_JOURNAL_NOT_EXIST(KbModule.ACCOUNTING, 71, "journal not exist", "交易归类不存在", RESOURCE_NOT_FOUND),
    ACCOUNTING_NOT_REFUNDABLE(KbModule.ACCOUNTING, 72, "not refundable", "交易不支持退款"),
    ACCOUNTING_ALREADY_REVERSED(KbModule.ACCOUNTING, 73, "already reversed", "只能回退一次"),
    ACCOUNTING_ALREADY_REFUNDED(KbModule.ACCOUNTING, 74, "already refunded", "只能退款一次"),
    ACCOUNTING_THIRD_PARTY_AMOUNT_UNMATCHED(KbModule.ACCOUNTING, 101, "amount is unmatched", "支付金额不匹配，请重新确认"),
    ACCOUNTING_ALIPAY_ERROR(KbModule.ACCOUNTING, 111, "alipay transaction error", "支付宝支付错误"),
    ACCOUNTING_ALIPAY_FAILED(KbModule.ACCOUNTING, 112, "alipay transaction failed", "支付宝支付失败"),
    ACCOUNTING_WX_ERROR(KbModule.ACCOUNTING, 121, "wxpay transaction error", "微信支付错误"),

    @Deprecated
    PRIZE_STOCK_ENTRY_NOT_EXIST(KbModule.PRIZE, 1, "prize stock entry not exist", "中奖纪录不存在", RESOURCE_NOT_FOUND),
    @Deprecated
    PRIZE_STOCK_NOT_EXIST(KbModule.PRIZE, 2, "prize stock not exist", "奖品库存不存在", RESOURCE_NOT_FOUND),
    @Deprecated
    PRIZE_DISTRIBUTE_CODE_WRONG(KbModule.PRIZE, 3, "prize distribute code wrong", "奖品派发码错误"),
    @Deprecated
    PRIZE_DISTRIBUTE_STATE_WRONG(KbModule.PRIZE, 4, "prize distribute state wrong", "奖品派发状态错误"),

    PRIZE_NOT_EXISTS(KbModule.PRIZE, 101, "prize not exist", "奖品不存在", RESOURCE_NOT_FOUND),
    PRIZE_DISPATCH_METHOD_NOT_EXISTS(KbModule.PRIZE, 102, "dispatch method not exist", "派奖信息不存在", RESOURCE_NOT_FOUND),
    PRIZE_EXPIRED(KbModule.PRIZE, 103, "prize expired", "奖品已过期"),
    PRIZE_GIVE_UP(KbModule.PRIZE, 104, "prize give up", "奖品已弃领"),
    PRIZE_WRITE_OFF_WRONG_CODE(KbModule.PRIZE, 201, "dispatch write code mismatch", "核销码错误"),
    PRIZE_CONFIRM_FAIL(KbModule.PRIZE, 301, "prize confirm fail", "奖品确认失败"),
    PRIZE_CONFIRM_NEED_MOBILE(KbModule.PRIZE, 302, "prize confirm need mobile", "需要提供手机号"),
    PRIZE_CONFIRM_NEED_ADDRESS(KbModule.PRIZE, 303, "prize confirm need address", "需要提供地址"),

    VOTE_NOT_EXISTS(KbModule.VOTE, 1, "vote not exist", "投票不存在"),
    VOTE_NOT_START(KbModule.VOTE, 2, "vote hasn't started", "投票尚未开始"),
    VOTE_IS_CLOSED(KbModule.VOTE, 3, "vote is closed", "投票已经结束"),
    VOTE_ALREADY_VOTED(KbModule.VOTE, 4, "vote is closed", "请勿重复投票"),
    VOTE_OPTION_NOT_EXIST(KbModule.VOTE, 5, "option doesn't exist", "无效的投票选项"),

    THIRD_PARTY_REQUEST_FAIL(KbModule.THIRD_PARTY, 1, "third party request fail", "三方请求失败"),
    THIRD_PARTY_REQUEST_AUTH_FAIL(KbModule.THIRD_PARTY, 2, "third party request auth fail", "三方请求权限错误"),
    THIRD_PARTY_ACCESS_NOT_EXISTS(KbModule.THIRD_PARTY, 11, "third party access not exists", "标识不存在", RESOURCE_NOT_FOUND),
    THIRD_PARTY_CODEC_FAIL(KbModule.THIRD_PARTY, 12, "third party codec fail", "加解密错误"),
    THIRD_PARTY_TRANSACTION_NOT_EXISTS(KbModule.THIRD_PARTY, 22, "third party transaction not exists", "支付订单不存在", RESOURCE_NOT_FOUND),
    THIRD_PARTY_TRANSACTION_CANCELED(KbModule.THIRD_PARTY, 23, "third party transaction canceled", "支付订单已取消"),
    THIRD_PARTY_TRANSACTION_EXPIRED(KbModule.THIRD_PARTY, 24, "third party transaction canceled", "支付订单已超时"),
    THIRD_PARTY_TRANSACTION_PAYED(KbModule.THIRD_PARTY, 25, "third party transaction payed", "支付订单已支付"),

    THIRD_PARTY_RONG_CLOUD_ERROR(KbModule.THIRD_PARTY_RONG, 101, "rong cloud error", "请求失败, 即时通讯错误"),
    THIRD_PARTY_RONG_CLOUD_WRONG_RESPONSE(KbModule.THIRD_PARTY_RONG, 102, "rong cloud response error", "请求失败, 即时通讯错误"),
    THIRD_PARTY_RONG_CLOUD_WRONG_CHAT_ROOM(KbModule.THIRD_PARTY_RONG, 103, "rong cloud create chatroom error", "请求失败, 融云创建聊天室失败"),
    THIRD_PARTY_RONG_CLOUD_WRONG_CHAT_ROOM_BANNED(KbModule.THIRD_PARTY_RONG, 104, "rong cloud chatroom banned user error", "请求失败, 融云聊天室禁言失败"),
    THIRD_PARTY_RONG_LIAN_WRONG_RESPONSE(KbModule.THIRD_PARTY_RONG_LIAN, 101, "rong lian cloud response error", "请求失败, 容联云错误"),

    THIRD_PARTY_V_CLOUD_CREATE_CHANNEL_WRONG(KbModule.THIRD_PARTY_YUNXIN, 1, "yunxin create channel fail", "网易云信 创建频道失败"),

    SAFEGUARD_NOT_EXISTS(KbModule.SAFEGUARD, 1, "safeguard not exist", "该维权不存在", RESOURCE_NOT_FOUND),

    PLAYBACK_ALBUM_NOT_EXIST(KbModule.PLAYBACK, 1, "album not exists", "专辑不存在", RESOURCE_NOT_FOUND),
    PLAYBACK_EMCEE_NOT_EXIST(KbModule.PLAYBACK, 2, "emcee not exists", "主持人信息不存在", RESOURCE_NOT_FOUND),
    PLAYBACK_NOT_EXIST(KbModule.PLAYBACK, 3, "playback not exists", "回播音频不存在", RESOURCE_NOT_FOUND),
    PLAYBACK_NEXT_NOT_EXIST(KbModule.PLAYBACK, 4, "playback next not exists", "已经是最后一条了", RESOURCE_NOT_FOUND),
    PLAYBACK_PREVIOUS_NOT_EXIST(KbModule.PLAYBACK, 5, "playback previous not exists", "已经是第一条了", RESOURCE_NOT_FOUND),

    RUSH_NOT_EXISTS(KbModule.RUSH, 101, "rush not exist", "抽奖活动不存在", RESOURCE_NOT_FOUND),
    RUSH_STOCK_NOT_EXISTS(KbModule.RUSH, 201, "rush stock not exist", "抽奖库存不存在", RESOURCE_NOT_FOUND),
    RUSH_STOCK_PUBLISH_FAIL(KbModule.RUSH, 211, "stock publish fail", "发布奖品库失败"),
    RUSH_STOCK_PUBLISH_SITE_ACCOUNT_INSUFFICIENT(KbModule.RUSH, 212, "stock publish site account insufficient", "发布奖品库失败, 电台账户余额不足抵扣"),
    RUSH_BACKUP_PRIZE_NOT_EXISTS(KbModule.RUSH, 301, "rush backup prize not exist", "奖品存档不存在", RESOURCE_NOT_FOUND),
    RUSH_PAY_PRIZE_EXPIRED(KbModule.RUSH, 401, "rush pay for prize expired", "待付款已过时效"),
    RUSH_PAY_PRIZE_FAIL(KbModule.RUSH, 402, "rush pay for prize fail", "付款"),
    RUSH_SEAL_WITH_UNRESOLVED_PRIZE(KbModule.RUSH, 901, "unresolved prize when seal", "尚有未处理的异常中奖"),

    NEWS_TOP_FULL(KbModule.NEWS, 1, "top news count limited", "置顶帖子过多"),
    NEWS_PUSH_FAIL(KbModule.NEWS, 2, "news isn't sign", "资讯未签发"),

    SCHEDULE_OVERLAPPED(KbModule.PROGRAM, 1, "schedule overlapped with others", "排班时间段重叠"),
    SCHEDULE_READ_ONLY(KbModule.PROGRAM, 2, "schedule is read only", "历史数据不允许变动"),
    ;

    private final int code; // unique
    private final int module;
    private final String msg; // debug message
    private final String message; // user readable message
    private KbCode parent; // parent KbCode, if any

    KbCode(KbModule module, int offset, String msg, String message, KbCode parent) {
        this.module = module.getValue();
        this.code = module.getValue() * 1000 + offset;
        this.msg = msg;
        this.message = message;
        this.parent = parent;
    }

    KbCode(KbModule module, int offset, String msg, String message) {
        this(module, offset, msg, message, null);
    }

    KbCode(int code, String msg, String message) {
        this.code = code;
        this.msg = msg;
        this.message = message;
        this.module = KbModule.SYSTEM.getValue();
    }

    public int getCode() {
        return code;
    }

    public int getModule() {
        return module;
    }

    // debug message
    public String getMsg() {
        return msg;
    }

    // user readable message
    public String getMessage() {
        return message;
    }

    public KbCode getParent() {
        return parent;
    }

    public static Optional<KbCode> valueOf(int code) {
        return Optional.ofNullable(codes.get(code));
    }

    private static final Map<Integer, KbCode> codes =
            Arrays.stream(values()).collect(Collectors.toMap(KbCode::getCode, c -> c));

    @Override
    public int getValue() {
        return code;
    }

    @Override
    public String getDescription() {
        return message;
    }
}
