package com.kaiba.lib.base.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 18-7-20
 *
 * 封装了一次请求产生的错误信息. 该信息可用于链路追踪, 会逐层向上传递.
 */
@Data
@ToString
@NoArgsConstructor
public class KbDebug {

    private transient static final int TRACE_MAX_LENGTH = 6;
    private transient static final int TRACE_MAX_LEVEL = 3;

    private String service;
    private String host;
    private String port;
    private String buildTime;
    private String buildBranch;
    private String buildCommitId;
    private String buildDirty;
    private String libVersion;
    private String confProfile;
    private Boolean confDebug;

    private String time;
    private String id;

    private Integer kbCode = KbCode.ERROR.getCode();
    private String message = KbCode.ERROR.getMsg();
    private List<String> trace;

    private String requestMethod;
    private String requestUri;
    private String requestParams;

    public String dump(boolean withTrace) {
        StringBuilder sb = new StringBuilder();
        sb.append("[").append(time).append("]");
        sb.append("[").append(service).append("]");
        sb.append("[").append(confProfile).append("|").append(confDebug).append("]");
        if (null != libVersion) {
            sb.append("[").append(libVersion).append("]");
        }
        if (null != buildBranch && null != buildCommitId) {
            sb.append("[").append(buildBranch).append("-").append(buildCommitId);
            if ("true".equals(buildDirty)) {
                sb.append("-dirty");
            }
            if (null != buildTime) {
                sb.append("-").append(buildTime);
            }
            sb.append("]");
        }
        sb.append("[").append(kbCode).append(" ").append(message).append("]");
        if (null != requestUri) {
            sb.append(" ").append(requestMethod).append(" ").append(requestUri).append(" ").append(requestParams);
        }
        if (withTrace && trace != null && trace.size() != 0) {
            sb.append("\n");
            for (int i = 0; i < trace.size(); i ++) {
                sb.append("\t").append(trace.get(i));
                if (i < trace.size() - 1) {
                    sb.append("\n");
                }
            }
        }
        return sb.toString();
    }

    public String dump() {
        return dump(false);
    }

    public static String dumpList(List<KbDebug> debugs, boolean withTrace) {
        StringBuilder sb = new StringBuilder();
        if (debugs != null && debugs.size() != 0) {
            for (int i = 0; i < debugs.size(); i ++) {
                sb.append(debugs.get(i).dump(withTrace));
                if (i < debugs.size() - 1) {
                    sb.append("\n");
                }
            }
        }
        return sb.toString();
    }

}
