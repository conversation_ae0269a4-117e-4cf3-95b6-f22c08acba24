package com.kaiba.lib.base.response;


import com.kaiba.lib.base.constant.Restful;
import com.kaiba.lib.base.util.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 18-7-20
 */
public class KbException extends RuntimeException {

    /** 严重程度: 等级 DEBUG 仅会打印一条 debug 信息. */
    public static final int LEVEL_DEBUG = 0;

    /** 严重程度: 等级 INFO 会打印一条 info 信息. */
    public static final int LEVEL_INFO = 1;

    /** 严重程度: 等级 WARN 会打印一条 warn 信息. */
    public static final int LEVEL_WARN = 2;

    /** 严重程度: 等级 ERROR 会打印一条 error 信息, 并打印异常栈信息. */
    public static final int LEVEL_ERROR = 3;

    private static final Map<KbCode, Supplier<KbException>> suppliers = Arrays
            .stream(KbCode.values())
            .collect(Collectors.toMap(c -> c, c -> () -> new KbException(c)));

    private final KbCode code;
    private String readableMessage;
    private int responseCode = Restful.RSP_FAIL;
    private int level = LEVEL_ERROR;

    public KbException(KbCode code) {
        super(generateMessage(code, null));
        this.code = code;
        init();
    }

    public KbException(KbCode code, Throwable cause) {
        super(generateMessage(code, null), cause);
        this.code = code;
        init();
    }

    public KbException(KbCode code, String message) {
        super(generateMessage(code, message));
        this.code = code;
        init();
    }

    public KbException(KbCode code, String message, Throwable cause) {
        super(generateMessage(code, message), cause);
        this.code = code;
        init();
    }

    protected void init() {
        // for sub class to override
    }

    public KbException setLevel(int level) {
        this.level = level;
        return this;
    }

    public KbException li() {
        this.level = LEVEL_INFO;
        return this;
    }

    public KbException ld() {
        this.level = LEVEL_DEBUG;
        return this;
    }

    public KbException lw() {
        this.level = LEVEL_WARN;
        return this;
    }

    public KbException r(String readableMessage) {
        return setReadableMessage(readableMessage);
    }

    public KbException setReadableMessage(String readableMessage) {
        this.readableMessage = readableMessage;
        return this;
    }

    public KbException setResponseCode(int responseCode) {
        this.responseCode = responseCode;
        return this;
    }

    public int getLevel() {
        return level;
    }

    public String getReadableMessage() {
        return readableMessage;
    }

    public KbCode getCode() {
        return code;
    }

    public int getResponseCode() {
        return responseCode;
    }

    @Deprecated
    public static Supplier<KbException> supplier(KbCode kbCode) {
        return supplier(kbCode, null, null);
    }

    @Deprecated
    public static Supplier<KbException> supplier(KbCode kbCode, String debugMessage) {
        return supplier(kbCode, debugMessage, null);
    }

    @Deprecated
    public static Supplier<KbException> supplier(KbCode kbCode, String debugMessage, String readableMessage) {
        if (null == debugMessage && null == readableMessage) {
            return suppliers.get(kbCode);
        } else {
            return () -> {
                KbException e = new KbException(kbCode, debugMessage);
                e.setReadableMessage(readableMessage);
                return e;
            };
        }
    }

    private static String generateMessage(KbCode kbCode, String message) {
        if (StringUtils.isEmpty(message)) {
            message = kbCode.getCode() + "-" + kbCode + " " + kbCode.getMsg();
        }
        return message;
    }

}
