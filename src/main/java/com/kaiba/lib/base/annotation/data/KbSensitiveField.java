package com.kaiba.lib.base.annotation.data;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * author: lyux
 * date: 2020-07-23
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.FIELD, ElementType.ANNOTATION_TYPE })
public @interface KbSensitiveField {

    @AliasFor("attribute")
    int value() default 1;

    @AliasFor("value")
    int attribute() default 1;

}
