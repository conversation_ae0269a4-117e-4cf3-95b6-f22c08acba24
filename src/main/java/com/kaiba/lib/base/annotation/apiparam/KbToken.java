package com.kaiba.lib.base.annotation.apiparam;

import com.kaiba.lib.base.middleware.paramvalidator.KbTokenValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * author: lyux
 * date: 18-7-25
 */
@Target({ METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE })
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = { KbTokenValidator.class })
public @interface KbToken {

    String message() default "令牌格式错误";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };

}
