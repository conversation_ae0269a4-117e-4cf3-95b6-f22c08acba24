package com.kaiba.lib.base.annotation.api;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * author: lyux
 * date: 18-7-25
 *
 * deprecate: token 验证相关逻辑已全部移至 api 网关.
 */
@Deprecated
@Target({ METHOD, ANNOTATION_TYPE })
@Retention(RUNTIME)
@Documented
public @interface KbCheckLogin {

}
