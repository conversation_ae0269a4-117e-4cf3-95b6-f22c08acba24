package com.kaiba.lib.base.annotation.api;

import com.kaiba.lib.base.constant.KbSignType;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 * @date 2019/5/28
 */
@Target({ METHOD, ANNOTATION_TYPE })
@Retention(RUNTIME)
@Documented
public @interface KbCheckSignature {

    /** 签名类型, 由此决定如何获取盐值. */
    KbSignType type();

    /** 盐值. 仅当类型为 {@link KbSignType#SPECIFY} 时有效 */
    String salt() default "";

    /** 过期时间, 单位秒. 仅当类型为 {@link KbSignType#SPECIFY} 时有效 */
    long expire() default 120;

}
