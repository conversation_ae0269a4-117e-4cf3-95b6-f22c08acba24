package com.kaiba.lib.base.annotation.data;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * author: lyux
 * date: 2025-01-08
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface KbRefreshMongoIndex {

    String value() default DEFAULT_UNTIL;

    String DEFAULT_UNTIL = "2025-01-01";

}
