package com.kaiba.lib.base.annotation.api;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * author: lyux
 * date: 18-11-1
 *
 * 注释添加在 controller 接口上, 用以限制满足某条件的 api 请求的频次.
 */
@Target({ METHOD, ANNOTATION_TYPE })
@Retention(RUNTIME)
@Documented
public @interface KbCheckFrequency {

    /**
     * request 频次限制. 单位秒.
     * 具体逻辑见 {@link #key()} 的注释.
     */
    int value() default 3;

    /**
     * 以 request 里的哪个字段为频次限制依据.
     * 默认为 userId, 表示 n 秒内对所注解的接口发起的多个请求,
     * 如果参数中有相同的 userId 值, 则只有第一个请求可以成功, 后续请求都会失败.
     */
    String key() default "userId";

    /**
     * 从 http 的哪个位置取参数. 默认为 param. 可选 param / header .
     */
    KbCheckFrequencyReqPosition position() default KbCheckFrequencyReqPosition.PARAM;

    /**
     * 频次超过限制时给予用户的提示语.
     */
    String message() default "点的太快啦! 请稍后再试.";

}
