package com.kaiba.lib.base.annotation.api;

import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * author: lyux
 * date: 20-3-18
 */
@Target({ METHOD })
@Retention(RUNTIME)
@Inherited
public @interface KbInheritParamApi {
}
