package com.kaiba.lib.base.annotation.api;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * deprecate: 手机号绑定验证相关逻辑已全部移至 api 网关.
 */
@Deprecated
@Target({ METHOD })
@Retention(RUNTIME)
@Documented
public @interface KbCheckMobileBind {
}
