package com.kaiba.lib.base.annotation.api;

import com.kaiba.lib.base.constant.auth.AuthPermissionLogic;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * author: lyux
 * date: 19-8-15
 *
 * deprecate: permission 验证相关逻辑已全部移至 api 网关.
 */
@Deprecated
@Target({ METHOD })
@Retention(RUNTIME)
public @interface KbHasPermission {

    /**
     * 权限字符串
     */
    String[] value();

    /**
     * 是否需要检查授权状态.
     * 若设置为 false, 则只会检查是否具有权限;
     * 若设置为 ture, 则同时会检查用户是否有合适的授权, 比如是否有正确的验证码.
     */
    boolean checkAuth() default true;

    /**
     * 当需要检查多个权限时, 以何种逻辑检查.
     */
    int logic() default AuthPermissionLogic.OR;

    /**
     * 没有权限时的提示语. 默认为各权限的专用提示语.
     */
    String message() default "";

}
