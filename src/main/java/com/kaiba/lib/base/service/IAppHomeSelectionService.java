package com.kaiba.lib.base.service;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.appselection.*;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * author: lyux
 * date: 2023-08-09
 */
@RequestMapping("/app/HomeSelection")
public interface IAppHomeSelectionService {

    @ApiOperation("将内容选入推荐池")
    @PostMapping(path = "/usr/content/createContent")
    KbEntity<SelectionContentModel> createContent(
            @RequestHeader(KbHeader.KB_SITE_ID) Integer siteId,
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestBody() SelectionContentCreateModel model
    );

    @ApiOperation("将内容选入推荐池")
    @PostMapping(path = "/usr/content/updateContentData")
    KbEntity<SelectionContentModel> updateContentData(
            @RequestBody() SelectionContentUpdateModel model
    );

    @ApiOperation("将内容从推荐池移除, 会同时对已上线的内容进行下线")
    @PostMapping(path = "/usr/content/removeContentById")
    KbEntity<Void> removeContentById(
            @RequestParam() String contentId
    );

    @ApiOperation("将内容从推荐池移除, 会同时对已上线的内容进行下线")
    @PostMapping(path = "/usr/content/removeContentByRef")
    KbEntity<Void> removeContentByRef(
            @RequestParam() String origin,
            @RequestParam() String ref1,
            @RequestParam(required = false) String ref2,
            @RequestParam(required = false) String ref3
    );

    @ApiOperation("获取推荐池内数据列表")
    @PostMapping(path = "/obj/content/getContentListByRef")
    KbEntity<List<SelectionContentModel>> getContentListByRef(
            @RequestParam() String origin,
            @RequestParam() String ref1,
            @RequestParam(required = false) String ref2,
            @RequestParam(required = false) String ref3
    );

    @ApiOperation("获取推荐池内数据列表")
    @PostMapping(path = "/obj/content/getContentListBySite")
    KbEntity<List<SelectionContentModel>> getContentListBySite(
            @RequestHeader(KbHeader.KB_SITE_ID) Integer siteId,
            @RequestParam(required = false) @ApiParam("ALL/NONE 代表已推荐/未推荐") String target,
            @RequestParam(required = false) String origin,
            @RequestParam(required = false) String title,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @ApiOperation("获取推荐池数据")
    @PostMapping(path = "/obj/content/getContentById")
    KbEntity<SelectionContentModel> getContentById(
            @RequestParam() String contentId
    );

    @ApiOperation("将推荐池内容上线到推荐位")
    @PostMapping(path = "/usr/choice/setContentOnline")
    KbEntity<SelectionChoiceModel> setContentOnline(
            @RequestParam() String contentId,
            @RequestParam() @ApiParam("推荐位") String target,
            @RequestParam() Integer style,
            @RequestParam(required = false) @ApiParam("来源角标") String originName,
            @RequestParam(required = false) @ApiParam("手动排序依据") Long idx,
            @RequestParam(required = false) @ApiParam("预约下线时间") Long scheduleEndTime,
            @RequestParam(required = false) @ApiParam("预约取消置顶时间") Long scheduleDelIdxTime
    );

    @ApiOperation("从推荐位下线推荐内容, 但不会删除推荐池内容")
    @PostMapping(path = "/usr/choice/setContentOffline")
    KbEntity<Void> setContentOffline(
            @RequestParam() String choiceId
    );

    @ApiOperation("内容推送")
    @PostMapping(path = "/usr/choice/pushContent")
    KbEntity<Void> pushContent(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String contentId
    );

    @ApiOperation("更改推荐位样式")
    @PostMapping(path = "/usr/choice/updateChoiceStyle")
    KbEntity<Void> updateChoiceStyle(
            @RequestParam() String choiceId,
            @RequestParam() Integer style
    );

    @ApiOperation("更改推荐位的预约下线时间, 时间不传递表示清除预约下线时间")
    @PostMapping(path = "/usr/choice/updateChoiceScheduleEndTime")
    KbEntity<Void> updateChoiceScheduleEndTime(
            @RequestParam() String choiceId,
            @RequestParam(required = false) Long scheduleEndTime
    );

    @ApiOperation("更改推荐位的预约取消置顶时间, 时间不传递表示清除预约取消置顶时间")
    @PostMapping(path = "/usr/choice/updateChoiceScheduleDelIdxTime")
    KbEntity<Void> updateChoiceScheduleDelIdxTime(
            @RequestParam() String choiceId,
            @RequestParam(required = false) Long scheduleDelIdxTime
    );

    @ApiOperation("推荐位置顶")
    @PostMapping(path = "/usr/choice/setChoiceAsTop")
    KbEntity<SelectionChoiceModel> setChoiceAsTop(
            @RequestParam() String choiceId,
            @RequestParam() Long idx,
            @RequestParam(required = false) @ApiParam("预约取消置顶时间") Long scheduleDelIdxTime
    );

    @ApiOperation("推荐位取消置顶")
    @PostMapping(path = "/usr/choice/cancelChoiceAsTop")
    KbEntity<SelectionChoiceModel> cancelChoiceAsTop(
            @RequestParam() String choiceId
    );

    @ApiOperation("批量排序推荐位")
    @PostMapping(path = "/usr/choice/bulkUpdateChoiceIdx")
    KbEntity<Void> bulkUpdateChoiceIdx(
            @RequestBody() SelectionChoiceIdxUpdateModel model
    );

    @ApiOperation("获取已上线到指定推荐位的推荐内容列表")
    @PostMapping(path = "/obj/choice/getChoiceListByTarget")
    KbEntity<List<SelectionChoiceModel>> getChoiceListByTarget(
            @RequestHeader(KbHeader.KB_SITE_ID) Integer siteId,
            @RequestParam() @ApiParam("推荐位") String target,
            @RequestParam(required = false) String contentId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @ApiOperation("根据内容获取已上线的推荐位列表")
    @PostMapping(path = "/obj/choice/getChoiceListByContentId")
    KbEntity<List<SelectionChoiceModel>> getChoiceListByContentId(
            @RequestParam() String contentId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @ApiOperation("获取已上线到推荐位的推荐内容")
    @PostMapping(path = "/obj/choice/getChoiceById")
    KbEntity<SelectionChoiceModel> getChoiceById(
            @RequestParam() String choiceId
    );

    @PostMapping(path = "/obj/choice/getChoiceByContentAndTarget")
    KbEntity<SelectionChoiceModel> getChoiceByContentAndTarget(
            @RequestParam() String contentId,
            @RequestParam() @ApiParam("推荐位") String target
    );

}
