package com.kaiba.lib.base.service;

import com.kaiba.lib.base.constant.appwidget.WidgetOrder;
import com.kaiba.lib.base.domain.appwidget.banner.BannerIdxUpdateModel;
import com.kaiba.lib.base.domain.appwidget.banner.BannerImageModel;
import com.kaiba.lib.base.domain.appwidget.banner.BannerImageUpdateModel;
import com.kaiba.lib.base.domain.appwidget.banner.BannerInstanceModel;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * author: lyux
 * date: 2023-06-14
 */
@RequestMapping("/app/widget/banner")
public interface IAppBannerService {

    @PostMapping(path = "/createInstance")
    KbEntity<BannerInstanceModel> createInstance(
            @RequestBody() BannerInstanceModel model
    );

    @ApiOperation("修改实例名称和简介.")
    @PostMapping(path = "/updateInstanceDesc")
    KbEntity<Void> updateInstanceDesc(
            @RequestParam() String instanceId,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) Integer backendFilterType
    );

    @ApiOperation("修改实例自动翻页间隔配置, 单位毫秒. 非正值表示不会自动翻页.")
    @PostMapping(path = "/updateInstanceAutoplay")
    KbEntity<Void> updateInstanceAutoplay(
            @RequestParam() String instanceId,
            @RequestParam() Integer autoplay
    );

    @ApiOperation("修改实例图片尺寸配置. 对于已有数据的实例该字段不允许修改实例的图片尺寸配置")
    @PostMapping(path = "/updateInstanceImageSize")
    KbEntity<Void> updateInstanceImageSize(
            @RequestParam() String instanceId,
            @RequestParam() Integer width,
            @RequestParam() Integer height
    );

    @ApiOperation("只允许删除尚无数据的实例")
    @PostMapping(path = "/deleteInstance")
    KbEntity<Void> deleteInstance(
            @RequestParam() String instanceId
    );

    @ApiOperation("id 和 key 必须二选一. 两者都传则优先 id.")
    @PostMapping(path = "/getInstance")
    KbEntity<BannerInstanceModel> getInstance(
            @RequestParam(required = false) Integer siteId,
            @RequestParam(required = false) String instanceId,
            @RequestParam(required = false) String instanceKey,
            @RequestParam(required = false, defaultValue = "true") Boolean allowCache
    );

    @PostMapping(path = "/getInstanceList")
    KbEntity<List<BannerInstanceModel>> getInstanceList(
            @RequestParam(required = false) Integer siteId,
            @RequestParam(required = false) Integer backendFilterType,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    //-------------------------------------------------------------

    @PostMapping(path = "/createBanner")
    KbEntity<BannerImageModel> createBanner(
            @RequestBody() BannerImageModel model
    );

    @ApiOperation("更新不参与")
    @PostMapping(path = "/updateBannerData")
    KbEntity<BannerImageModel> updateBannerData(
            @RequestBody() BannerImageUpdateModel model
    );

    @PostMapping(path = "/updateBannerState")
    KbEntity<Void> updateBannerState(
            @RequestParam() String bannerId,
            @RequestParam() Integer state
    );

    @ApiOperation("设置预约开始/结束时间. 参数传 null 表示清空设置.")
    @PostMapping(path = "/updateBannerScheduleTime")
    KbEntity<Void> updateBannerScheduleTime(
            @RequestParam() String bannerId,
            @RequestParam(required = false) Long scheduledStartTime,
            @RequestParam(required = false) Long scheduledEndTime
    );

    @ApiOperation("调整排序. 仅支持对 signed 和 online 状态的 banner 进行排序.")
    @PostMapping(path = "/updateBannerListIdx")
    KbEntity<Void> updateBannerListIdx(
            @RequestBody() BannerIdxUpdateModel model
    );

    @ApiOperation("删除轮播图.")
    @PostMapping(path = "/deleteBannerById")
    KbEntity<Void> deleteBannerById(
            @RequestParam() String bannerId
    );

    @ApiOperation("id 和 key 必须二选一. 两者都传则优先 id. 不走缓存")
    @PostMapping(path = "/getBannerList")
    KbEntity<List<BannerImageModel>> getBannerList(
            @RequestParam() String instanceId,
            @RequestParam(required = false) Integer state,
            @RequestParam(required = false) Integer type,
            @RequestParam(required = false, defaultValue = "IDX_ASC") WidgetOrder order,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @ApiOperation("从缓存中获取状态为已上线的 banner 列表. id 和 key 必须二选一. 两者都传则优先 id.")
    @PostMapping(path = "/getCachedOnlineBannerList")
    KbEntity<List<BannerImageModel>> getCachedOnlineBannerList(
            @RequestParam() String instanceId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @ApiOperation("从缓存中获取状态为已签发的 banner 列表. id 和 key 必须二选一. 两者都传则优先 id.")
    @PostMapping(path = "/getCachedSignedBannerList")
    KbEntity<List<BannerImageModel>> getCachedSignedBannerList(
            @RequestParam() String instanceId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @ApiOperation("从缓存中获取类型为'默认'的 banner 列表. id 和 key 必须二选一. 两者都传则优先 id.")
    @PostMapping(path = "/getCachedDefaultBannerList")
    KbEntity<List<BannerImageModel>> getCachedDefaultBannerList(
            @RequestParam() String instanceId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @ApiOperation("id 和 key 必须二选一. 两者都传则优先 id.")
    @PostMapping(path = "/searchBannerListByTitle")
    KbEntity<List<BannerImageModel>> searchBannerListByTitle(
            @RequestParam() String instanceId,
            @RequestParam() String keyword,
            @RequestParam(required = false) Integer state,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

}
