package com.kaiba.lib.base.service;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.prize.PrizeModel;
import com.kaiba.lib.base.domain.rush.*;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * author: lyux
 * date: 19-11-20
 */
@RequestMapping("/rush")
public interface IRushService {

    @ApiOperation("抽奖动作")
    @PostMapping(path = "/rush")
    KbEntity<RushResult> rush(
            @RequestParam(name = "rushId", required = false) String rushId,
            @RequestParam(name = "rushKey", required = false) String rushKey,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "secret", required = false) String secret,
            @RequestParam(name = "password", required = false) String password
    );

    @ApiOperation("对于需要付款的奖品, 此为付款接口")
    @PostMapping(path = "/payForPrize")
    KbEntity<PrizeModel> payForPrize(
            @RequestParam(name = "rushPrizeId") String rushPrizeId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "addressId", required = false) String addressId,
            @RequestParam(name = "rechargeTransactionId", required = false) String rechargeTransactionId
    );

    @ApiOperation("对于需要付款的奖品, 此为付款原路退回接口")
    @PostMapping(path = "/payForPrizeRefund")
    KbEntity<PrizeModel> payForPrizeRefund(
            @RequestParam(name = "prizeId") String prizeId,
            @RequestParam(name = "userId") Integer userId
    );

    // --------------------------------------------------

    @PostMapping(path = "/getRushByKey")
    KbEntity<RushModel> getRushByKey(
            @RequestParam(name = "rushKey") String rushKey,
            @RequestParam(name = "userId", required = false) Integer userId,
            @RequestParam(name = "allowCache", required = false, defaultValue = "false") Boolean allowCache
    );

    @PostMapping(path = "/getRushById")
    KbEntity<RushModel> getRushById(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId", required = false) Integer userId,
            @RequestParam(name = "allowCache", required = false, defaultValue = "false") Boolean allowCache
    );

    @PostMapping(path = "/getRushIdByKey")
    KbEntity<String> getRushIdByKey(
            @RequestParam(name = "rushKey") String rushKey
    );

    @ApiOperation("获取抽奖实例的统计信息")
    @PostMapping(path = "/getRushSummaryById")
    KbEntity<RushSummaryModel> getRushSummaryById(
            @RequestParam(name = "rushId") String rushId
    );

    @ApiOperation("获取抽奖实例的统计信息, 已逐行格式化为用户可读的信息")
    @PostMapping(path = "/getSimpleRushSummaryById")
    KbEntity<List<String>> getSimpleRushSummaryById(
            @RequestParam(name = "rushId") String rushId
    );

    @ApiOperation("获取抽奖实例奖池内的奖品数量, 即待抢奖品数量")
    @PostMapping(path = "/getRushAvailablePrizeCountById")
    KbEntity<Long> getRushAvailablePrizeCountById(
            @RequestParam(name = "rushId") String rushId
    );

    @PostMapping(path = "/getRushListIn")
    KbEntity<List<RushModel>> getRushListIn(
            @RequestParam(name = "rushIds", required = false) String[] rushIds
    );

    @PostMapping(path = "/getRushList")
    KbEntity<List<RushModel>> getRushList(
            @RequestParam(name = "states", required = false) Integer[] states,
            @RequestParam(name = "siteId", required = false) Integer siteId,
            @RequestParam(name = "groupId", required = false) String groupId,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @PostMapping(path = "/createRush")
    KbEntity<RushModel> createRush(
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "siteId", required = false) Integer siteId,
            @RequestParam(name = "source", required = false) Integer source,
            @RequestParam(name = "description", required = false) String description,
            @RequestParam(name = "scheduledStartTime", required = false) Long scheduledStartTime,
            @RequestParam(name = "scheduledEndTime", required = false) Long scheduledEndTime,
            @RequestParam(name = "rushPoolType", required = false) Integer rushPoolType,
            @RequestParam(name = "ruleHitRate", required = false) Integer ruleHitRate,
            @RequestParam(name = "ruleHitFloor", required = false) Integer ruleHitFloor,
            @RequestParam(name = "ruleInitialChance", required = false) Integer ruleInitialChance,
            @RequestParam(name = "ruleChanceResetTime", required = false) @ApiParam("相对时间表达式") String ruleChanceResetTime
    );

    @PostMapping(path = "/createRushByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<RushModel> createRushByBody(
            @RequestBody RushModel rush
    );

    @PostMapping(path = "/createRushByCopy")
    KbEntity<RushModel> createRushByCopy(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String rushId,
            @RequestParam(required = false) @ApiParam("是否复制奖品") Boolean copyStock,
            @RequestParam(required = false) @ApiParam("是否仅使用可用奖品数") Boolean copyStockAvail
    );

    @PostMapping(path = "/updateRushByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<RushModel> updateRushByBody(
            @RequestBody RushModel rush
    );

    @ApiOperation("修改抽奖概率规则")
    @PostMapping(path = "/updateRushHitRate")
    KbEntity<RushModel> updateRushHitRate(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "ruleHitRate") Integer ruleHitRate
    );

    @ApiOperation("修改抽奖楼层规则")
    @PostMapping(path = "/updateRushHitFloor")
    KbEntity<RushModel> updateRushHitFloor(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "ruleHitFloor") Integer ruleHitFloor
    );

    @ApiOperation("修改用户的可用抽奖次数规则")
    @PostMapping(path = "/updateRushChanceRule")
    KbEntity<RushModel> updateRushChanceRule(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "ruleInitialChance") Integer ruleInitialChance,
            @RequestParam(name = "ruleChanceResetTime", required = false) @ApiParam("相对时间表达式") String ruleChanceResetTime
    );

    @ApiOperation("修改用户未中奖时的抽奖次数返还策略")
    @PostMapping(path = "/updateRushMissingChanceRule")
    KbEntity<RushModel> updateRushMissingChanceRule(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "emptyPolicy") @ApiParam("奖池中无奖品时, 是否消耗抽奖次数") Integer emptyPolicy,
            @RequestParam(name = "hitOutPolicy") @ApiParam("当用户没有中奖机会时, 是否消耗抽奖次数") Integer hitOutPolicy
    );

    @ApiOperation("修改抽奖的开始和结束时间")
    @PostMapping(path = "/updateRushScheduleTime")
    KbEntity<RushModel> updateRushScheduleTime(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "scheduledStartTime", required = false) Long scheduledStartTime,
            @RequestParam(name = "scheduledEndTime", required = false) Long scheduledEndTime
    );

    @ApiOperation("修改抽奖提示语")
    @PostMapping(path = "/updateRushMessage")
    KbEntity<RushModel> updateRushMessage(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "msgRushNotStart", required = false) String msgRushNotStart,
            @RequestParam(name = "msgRushMiss", required = false) String msgRushMiss,
            @RequestParam(name = "msgRushWrongPassword", required = false) String msgRushWrongPassword,
            @RequestParam(name = "msgRushNoMore", required = false) String msgRushNoMore,
            @RequestParam(name = "msgRushChanceOut", required = false) String msgRushChanceOut,
            @RequestParam(name = "msgHitChanceOut", required = false) String msgHitChanceOut,
            @RequestParam(name = "msgRushOnPause", required = false) String msgRushOnPause,
            @RequestParam(name = "msgRushEnded", required = false) String msgRushEnded
    );

    @ApiOperation("修改口令")
    @PostMapping(path = "/updateRushPassword")
    KbEntity<RushModel> updateRushPassword(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "password", required = false) String password
    );

    @ApiOperation("修改抽奖状态改变回调")
    @PostMapping(path = "/updateRushStateChangeCallbackUrl")
    KbEntity<RushModel> updateRushStateChangeCallbackUrl(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "callbackUrl", required = false) String callbackUrl
    );

    @ApiOperation("修改抽奖实例的组设置")
    @PostMapping(path = "/updateRushGroup")
    KbEntity<RushModel> updateRushGroup(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "groupId") String groupId
    );

    @ApiOperation("去掉抽奖实例的组设置")
    @PostMapping(path = "/removeRushGroup")
    KbEntity<RushModel> removeRushGroup(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId
    );

    @ApiOperation("抽奖实例增加业务标识")
    @PostMapping(path = "/addRushKey")
    KbEntity<RushModel> addRushKey(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "appKey") String appKey
    );

    @PostMapping(path = "/signRush")
    KbEntity<RushModel> signRush(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId
    );

    @PostMapping(path = "/startRush")
    KbEntity<RushModel> startRush(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId
    );

    @PostMapping(path = "/pauseRush")
    KbEntity<RushModel> pauseRush(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId
    );

    @PostMapping(path = "/sealRush")
    KbEntity<RushModel> sealRush(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId
    );

    // --------------------------------------------------

    @PostMapping(path = "/getStockById")
    KbEntity<RushStockModel> getStockById(
            @RequestParam(name = "stockId") String stockId,
            @RequestParam(name = "allowCache", required = false, defaultValue = "false") Boolean allowCache
    );

    @PostMapping(path = "/getStockListByRushId")
    KbEntity<List<RushStockModel>> getStockListByRushId(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "states", required = false) Integer[] states,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @ApiOperation("获取尚未发布且有预约发布设置的 stock 列表")
    @PostMapping(path = "/getStockListByRushIdAndScheduleTime")
    KbEntity<List<RushStockModel>> getStockListByRushIdAndScheduleTime(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "st") Long st,
            @RequestParam(name = "et") Long et,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @PostMapping(path = "/createStockByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<RushStockModel> createStockByBody(
            @RequestBody RushStockModel stock
    );

    @PostMapping(path = "/createStockByCopy")
    KbEntity<RushStockModel> createStockByCopy(
            @RequestParam(name = "stockId") String stockId,
            @RequestParam(name = "count", required = false) Integer count
    );

    @PostMapping(path = "/updateStockListByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<List<RushStockModel>> updateStockListByBody(
            @RequestBody RushStockListModel stockListModel
    );

    @PostMapping(path = "/updateStockBasic")
    KbEntity<RushStockModel> updateStockBasic(
            @RequestParam(name = "stockId") String stockId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "count", required = false) Integer count,
            @RequestParam(name = "pickupAddress", required = false) String pickupAddress,
            @RequestParam(name = "description", required = false) String description,
            @RequestParam(name = "v") Long v
    );

    /**
     * 奖品基础属性修改,不影响状态.
     * 必传属性:
     *      id, v
     * 现支持以下属性变更:
     *      name,count,pickupAddress,description,images
     *
     * @param stock 仅支持部分基础属性修改
     * @return
     */
    @PostMapping(path = "/updateStockBasicByBody")
    KbEntity<RushStockModel> updateStockBasicByBody(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestBody RushStockModel stock
    );

    @PostMapping(path = "/updateStockScheduleTime")
    KbEntity<RushStockModel> updateStockScheduleTime(
            @RequestParam(name = "stockId") String stockId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "scheduledStartTime") Long scheduledStartTime,
            @RequestParam(name = "v") Long v
    );

    @PostMapping(path = "/updateStockRushRule")
    KbEntity<RushStockModel> updateStockRushRule(
            @RequestParam(name = "stockId") String stockId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "ruleHitRate") Integer ruleHitRate,
            @RequestParam(name = "v") Long v
    );

    @PostMapping(path = "/publishStock")
    KbEntity<StockPublishResultModel> publishStock(
            @RequestParam(name = "stockId") String stockId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "v") Long v
    );

    @PostMapping(path = "/publishStockByRushId")
    KbEntity<List<StockPublishResultModel>> publishStockByRushId(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId
    );

    @PostMapping(path = "/closeStock")
    KbEntity<RushStockModel> closeStock(
            @RequestParam(name = "stockId") String stockId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "v") Long v
    );

    // --------------------------------------------------

    @PostMapping(path = "/getUserChance")
    KbEntity<Long> getUserChance(
            @RequestParam(name = "rushId", required = false) String rushId,
            @RequestParam(name = "rushKey", required = false) String rushKey,
            @RequestParam(name = "userId") Integer userId
    );

    @PostMapping(path = "/resetUserChance")
    KbEntity<Long> resetUserChance(
            @RequestParam(name = "rushId", required = false) String rushId,
            @RequestParam(name = "rushKey", required = false) String rushKey,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "targetUserId") Integer targetUserId
    );

    @PostMapping(path = "/setUserChance")
    KbEntity<Long> setUserChance(
            @RequestParam(name = "rushId", required = false) String rushId,
            @RequestParam(name = "rushKey", required = false) String rushKey,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "targetUserId") Integer targetUserId,
            @RequestParam(name = "chance") Integer chance
    );

    @PostMapping(path = "/increaseUserChance")
    KbEntity<Long> increaseUserChance(
            @RequestParam(name = "rushId", required = false) String rushId,
            @RequestParam(name = "rushKey", required = false) String rushKey,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "targetUserId") Integer targetUserId,
            @RequestParam(name = "chanceIncrease") Integer chanceIncrease
    );

    // --------------------------------------------------

    @PostMapping(path = "/createGroupByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<RushGroupModel> createGroupByBody(
            @RequestBody RushGroupModel group
    );

    @ApiOperation("更新名称或者描述")
    @RequestMapping(path = "/updateGroupNameOrDescription", method = RequestMethod.POST)
    KbEntity<RushGroupModel> updateGroupNameOrDescription(
            @RequestParam(name = "groupId") String groupId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "description", required = false) String description
    );

    @ApiOperation("更新附加信息")
    @RequestMapping(path = "/updateGroupExtra", method = RequestMethod.POST)
    KbEntity<RushGroupModel> updateGroupExtra(
            @RequestBody RushGroupUpdateExtraModel update
    );

    @ApiOperation("更新附加键信息中的具体键值")
    @RequestMapping(path = "/updateGroupExtraKeyValue", method = RequestMethod.POST)
    KbEntity<RushGroupModel> updateGroupExtraKeyValue(
            @RequestParam(name = "groupId") String groupId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "key") String key,
            @RequestParam(name = "value") String value
    );

    @ApiOperation("更新分组中的用户抽奖机会和中奖机会设置. 若接口中设置参数为空则表示清空该设置")
    @RequestMapping(path = "/updateGroupChance", method = RequestMethod.POST)
    KbEntity<RushGroupModel> updateGroupChance(
            @RequestParam(name = "groupId") String groupId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "ruleHitChance", required = false) Integer ruleHitChance,
            @RequestParam(name = "ruleInitialChance", required = false) Integer ruleInitialChance,
            @RequestParam(name = "ruleChanceResetTime", required = false) @ApiParam("相对时间表达式") String ruleChanceResetTime
    );

    @PostMapping(path = "/getGroupById")
    KbEntity<RushGroupModel> getGroupById(
            @RequestParam(name = "groupId") String groupId,
            @RequestParam(name = "allowCache", required = false, defaultValue = "true") Boolean allowCache
    );

    @PostMapping(path = "/getGroupListIn")
    KbEntity<List<RushGroupModel>> getGroupListIn(
            @RequestParam(name = "groupIds") String[] groupIds
    );

    @PostMapping(path = "/getGroupListBySiteId")
    KbEntity<List<RushGroupModel>> getGroupListBySiteId(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "type", required = false) String type,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    // --------------------------------------------------

    @PostMapping(path = "/getPrizeListByRushId")
    KbEntity<List<PrizeModel>> getPrizeListByRushId(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @PostMapping(path = "/getPrizeListByStockId")
    KbEntity<List<PrizeModel>> getPrizeListByStockId(
            @RequestParam(name = "stockId") String stockId,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @PostMapping(path = "/getPrizeByBackupPrizeId")
    KbEntity<PrizeModel> getPrizeByBackupPrizeId(
            @RequestParam(name = "prizeId") String prizeId
    );

    @PostMapping(path = "/getBackupPrizeById")
    KbEntity<RushBackupPrizeModel> getBackupPrizeById(
            @RequestParam(name = "prizeId") String prizeId
    );

    @PostMapping(path = "/getBackupPrizeListByRushId")
    KbEntity<List<RushBackupPrizeModel>> getBackupPrizeListByRushId(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "state", required = false) Integer state,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @PostMapping(path = "/getBackupPrizeListByRushIdAndUserId")
    KbEntity<List<RushBackupPrizeModel>> getBackupPrizeListByRushIdAndUserId(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "state", required = false) Integer state,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize,
            @RequestParam(name = "allowCache", required = false, defaultValue = "true") Boolean allowCache
    );

    @PostMapping(path = "/getBackupPrizeListByStockId")
    KbEntity<List<RushBackupPrizeModel>> getBackupPrizeListByStockId(
            @RequestParam(name = "stockId") String stockId,
            @RequestParam(name = "state", required = false) Integer state,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @PostMapping(path = "/getBackupPrizeListByUserId")
    KbEntity<List<RushBackupPrizeModel>> getBackupPrizeListByUserId(
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "state", required = false) Integer state,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @PostMapping(path = "/getBackupPrizeListBySiteId")
    KbEntity<List<RushBackupPrizeModel>> getBackupPrizeListBySiteId(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "state", required = false) Integer state,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @PostMapping(path = "/getBackupPrizeCount")
    KbEntity<Long> getBackupPrizeCount(
            @RequestParam(name = "siteId", required = false) Integer siteId,
            @RequestParam(name = "rushId", required = false) String rushId,
            @RequestParam(name = "stockId", required = false) String stockId,
            @RequestParam(name = "state", required = false) Integer state
    );

    @PostMapping(path = "/getBackupPrizeCountModel")
    KbEntity<RushBackupPrizeCountModel> getBackupPrizeCountModel(
            @RequestParam(name = "siteId", required = false) Integer siteId,
            @RequestParam(name = "rushId", required = false) String rushId,
            @RequestParam(name = "stockId", required = false) String stockId
    );

    @PostMapping(path = "/updateBackupPrizeAsResolved")
    KbEntity<RushBackupPrizeModel> updateBackupPrizeAsResolved(
            @RequestParam(name = "prizeId") String prizeId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "targetUserId") Integer targetUserId,
            @RequestParam(name = "remark") String remark
    );

    @ApiOperation("将奖品指派给指定用户. 如果奖品已经有其他主人了, 则指派会失败")
    @PostMapping(path = "/appointBackupPrizeToUserIfNoOwner")
    KbEntity<RushBackupPrizeModel> appointBackupPrizeToUserIfNoOwner(
            @RequestParam(name = "prizeId") String prizeId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "targetUserId") Integer targetUserId,
            @RequestParam(name = "expire", required = false) Long expire,
            @RequestParam(name = "remark", required = false) String remark
    );

    @PostMapping(path = "/resolveBackupPrize")
    KbEntity<PrizeModel> resolveBackupPrize(
            @RequestParam(name = "prizeId") String prizeId,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "remark", required = false) String remark
    );

    @PostMapping(path = "/tryResolveBackupPrizeByRush")
    KbEntity<Long> tryResolveBackupPrizeByRush(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "userId") Integer userId
    );

    // --------------------------------------------------

    default KbEntity<RushResult> rush(String rushId, Integer userId, String password) {
        return rush(rushId, null, userId, null, password);
    }

    default KbEntity<Long> getUserChance(String rushId, Integer userId) {
        return getUserChance(rushId, null, userId);
    }

    default KbEntity<Long> resetUserChance(String rushId, Integer userId, Integer targetUserId) {
        return resetUserChance(rushId, null, userId, targetUserId);
    }

    default KbEntity<Long> setUserChance(String rushId,  Integer userId, Integer targetUserId, Integer chance) {
        return setUserChance(rushId, null, userId, targetUserId, chance);
    }

    default KbEntity<Long> increaseUserChance(
            String rushId, Integer userId, Integer targetUserId, Integer chanceIncrease) {
        return increaseUserChance(rushId, null, userId, targetUserId, chanceIncrease);
    }

}
