package com.kaiba.lib.base.service;

import com.kaiba.lib.base.annotation.apiparam.KbUserId;
import com.kaiba.lib.base.domain.replydict.ReplyDictContentModel;
import com.kaiba.lib.base.domain.replydict.ReplyContentModel;
import com.kaiba.lib.base.domain.replydict.ReplyKeyModel;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;

@RequestMapping("/replyDict")
public interface IReplyDictService {

    @ApiOperation("创建回复内容")
    @RequestMapping(value = "/createReplyContentByBody", method = RequestMethod.POST)
    KbEntity<ReplyDictContentModel> createReplyContentByBody(
            @RequestBody ReplyDictContentModel data
    );

    @ApiOperation("更新回复内容")
    @RequestMapping(value = "/updateReplyContentByBody", method = RequestMethod.POST)
    KbEntity<ReplyDictContentModel> updateReplyContentByBody(
            @RequestBody ReplyDictContentModel data
    );

    @ApiOperation("创建关键词")
    @RequestMapping(value = "/createReplyKey", method = RequestMethod.POST)
    KbEntity<ReplyKeyModel> createReplyKey(
            @RequestParam(name = "userId") @KbUserId Integer userId,
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "groupId") String groupId,
            @RequestParam(name = "key") String key
    );

    @ApiOperation("根据siteId与groupId获取关键词列表")
    @RequestMapping(value = "/getReplyKeyListInByGroupIds", method = RequestMethod.POST)
    KbEntity<List<ReplyKeyModel>> getReplyKeyListInByGroupIds(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "groupIds") String[] groupIds
    );

    @ApiOperation("根据siteId与key获取关键词列表")
    @RequestMapping(value = "/getReplyKeyListInByKeys", method = RequestMethod.POST)
    KbEntity<List<ReplyKeyModel>> getReplyKeyListInByKeys(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "keys") String[] keys
    );

    @ApiOperation("根据siteId获取关键词列表")
    @RequestMapping(value = "/getReplyKeyListBySiteId", method = RequestMethod.POST)
    KbEntity<List<ReplyKeyModel>> getReplyKeyListBySiteId(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize,
            @RequestParam(name = "allowCache", required = false, defaultValue = "false") Boolean allowCache
    );

    @ApiOperation("根据siteId与groupId获取关键词列表")
    @RequestMapping(value = "/getReplyKeyListBySiteIdAndGroupId", method = RequestMethod.POST)
    KbEntity<List<ReplyKeyModel>> getReplyKeyListBySiteIdAndGroupId(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "groupId") String groupId,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @ApiOperation("根据id删除关键词")
    @RequestMapping(value = "/deleteReplyKeyById", method = RequestMethod.POST)
    KbEntity<String> deleteReplyKeyById(
            @RequestParam(name = "userId") @KbUserId Integer userId,
            @RequestParam(name = "id") String id
    );

    @ApiOperation("根据id获取关键词")
    @RequestMapping(value = "/getReplyKeyById", method = RequestMethod.POST)
    KbEntity<ReplyKeyModel> getReplyKeyById(
            @RequestParam(name = "id") String id
    );

    @ApiOperation("根据id获取回复内容")
    @RequestMapping(value = "/getReplyContentById", method = RequestMethod.POST)
    KbEntity<ReplyContentModel> getReplyContentById(
            @RequestParam(name = "id") String id
    );

    @ApiOperation("根据id删除回复内容")
    @RequestMapping(value = "/deleteReplyContentById", method = RequestMethod.POST)
    KbEntity<String> deleteReplyContentById(
            @RequestParam(name = "userId") @KbUserId Integer userId,
            @RequestParam(name = "id") String id
    );

    @ApiOperation("更新回复内容-groupId")
    @RequestMapping(value = "/updateReplyContentGroupIdById", method = RequestMethod.POST)
    KbEntity<ReplyContentModel> updateReplyContentGroupIdById(
            @RequestParam(name = "userId") @KbUserId Integer userId,
            @RequestParam(name = "id") String id,
            @RequestParam(name = "groupId") String groupId
    );

    @ApiOperation("根据siteId获取回复列表")
    @RequestMapping(value = "/getReplyContentListBySiteId", method = RequestMethod.POST)
    KbEntity<List<ReplyContentModel>> getReplyContentListBySiteId(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @ApiOperation("根据siteId和key获取回复内容")
    @RequestMapping(value = "/getReplyContentBySiteIdAndKey", method = RequestMethod.POST)
    KbEntity<ReplyContentModel> getReplyContentBySiteIdAndKey(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "key") String key
    );
}
