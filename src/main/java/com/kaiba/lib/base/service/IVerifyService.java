package com.kaiba.lib.base.service;

import com.kaiba.lib.base.domain.note.NoteVerifyModel;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * author yanghy
 * date 2019/11/21
 */
public interface IVerifyService {

    @ApiOperation("获取审核列表")
    @RequestMapping(path = "/verify/note/getNoteVerifyList", method = RequestMethod.POST)
    KbEntity<List<NoteVerifyModel>> getNoteVerifyList(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "page", required = false) Integer page,
            @RequestParam(name = "pageSize", required = false) Integer pageSize
    );

    @ApiOperation("注册到审核列表")
    @RequestMapping(path = "/verify/note/createNoteThreadVerify", method = RequestMethod.POST)
    KbEntity<NoteVerifyModel> createNoteThreadVerify(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "title") String title,
            @RequestParam(name = "description") String description,
            @RequestParam(name = "threadId") String threadId,
            @RequestParam(name = "userId") Integer userId
    );

    @RequestMapping(path = "/verify/note/getNoteVerifyById", method = RequestMethod.POST)
    KbEntity<NoteVerifyModel> getNoteVerifyById(
            @RequestParam(name = "noteVerifyId") String noteVerifyId
    );
}
