package com.kaiba.lib.base.service;

import com.kaiba.lib.base.domain.user.UserLoginLogCheckModel;
import com.kaiba.lib.base.domain.user.UserLoginLogModel;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;

@RequestMapping("/user/log")
public interface IUserLogService {

    @ApiOperation("根据userId获取最新一条用户登录日志")
    @PostMapping(path = "/getUserLoginLogByUserId")
    KbEntity<UserLoginLogModel> getUserLoginLogByUserId(
            @RequestParam(name = "userId") Integer userId
    );

    @ApiOperation("根据userId获取用户登录日志列表")
    @PostMapping(path = "/getUserLoginLogListByUserId")
    KbEntity<List<UserLoginLogModel>> getUserLoginLogListByUserId(
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "startTime",required = false) Long startTime,
            @RequestParam(name = "endTime",required = false) Long endTime,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @ApiOperation("根据userId和时间获取用户登录日志列表")
    @PostMapping(path = "/getUserLoginLogListByUserIdAndCreateTime")
    KbEntity<List<UserLoginLogModel>> getUserLoginLogListByUserIdAndCreateTime(
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "createTime",required = false) Long createTime,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @ApiOperation("检测用户上次登录装台")
    @PostMapping(path = "/checkUserLastLoginByUserId")
    KbEntity<UserLoginLogCheckModel> checkUserLastLoginByUserId(
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "level") Integer level
    );

    @ApiOperation("获取最新不同状态登录日志")
    @PostMapping(path = "/getLastDifferentStatusLoginLog")
    KbEntity<List<UserLoginLogCheckModel>> getLastDifferentStatusLoginLog(
            @RequestParam(name = "userId") Integer userId
    );
}
