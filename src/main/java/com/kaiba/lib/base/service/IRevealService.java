package com.kaiba.lib.base.service;

import com.kaiba.lib.base.domain.reveal.RevealProgramModel;
import com.kaiba.lib.base.domain.reveal.RevealScheduleModel;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.util.ArrayTypeHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version IRevealService, v0.1 2025/4/10 10:15 daopei Exp $
 **/
@RequestMapping("/reveal")
public interface IRevealService {

    @PostMapping(value = "/getProgramById")
    KbEntity<RevealProgramModel> getProgramById(
            @RequestParam() String programId
    );

    @PostMapping(value = "/getProgramByKey")
    KbEntity<RevealProgramModel> getProgramByKey(
            @RequestParam() String programKey
    );

    @PostMapping(value = "/getProgramListByIdIn")
    KbEntity<List<RevealProgramModel>> getProgramListByIdIn(
            @RequestParam() String[] programIds
    );

    @PostMapping(value = "/getProgramList")
    KbEntity<List<RevealProgramModel>> getProgramList(
            @RequestParam Integer siteId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @PostMapping(value = "/getScheduleById")
    KbEntity<RevealScheduleModel> getScheduleById(
            @RequestParam() String scheduleId
    );

    @PostMapping(value = "/getScheduleListByIdIn")
    KbEntity<List<RevealScheduleModel>> getScheduleListByIdIn(
            @RequestParam() String[] scheduleIds
    );

    @PostMapping(value = "/getScheduleListByProgram")
    KbEntity<List<RevealScheduleModel>> getScheduleListByProgram(
            @RequestParam() String programId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @PostMapping(value = "/getScheduleList")
    KbEntity<List<RevealScheduleModel>> getScheduleList(
            @RequestParam Integer siteId,
            @RequestParam(required = false) Long updateTimeStart,
            @RequestParam(required = false) Long updateTimeEnd,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    // --------------------------------------------------------

    default KbEntity<Map<String, RevealProgramModel>> getProgramMapByIds(Set<String> idSet) {
        return getProgramListByIdIn(idSet.toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY)).map(list -> list.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(RevealProgramModel::getId, m -> m, (k1, k2) -> k2)));
    }

    default KbEntity<Map<String, RevealScheduleModel>> getScheduleMapByIds(Set<String> idSet) {
        return getScheduleListByIdIn(idSet.toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY)).map(list -> list.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(RevealScheduleModel::getId, m -> m, (k1, k2) -> k2)));
    }

}
