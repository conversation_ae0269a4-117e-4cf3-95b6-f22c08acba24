package com.kaiba.lib.base.service;

import com.kaiba.lib.base.domain.apphome.AppServiceFrameModel;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version IAppServiceFrameService, v0.1 2023/7/28 15:55 daopei Exp $
 **/
@RequestMapping("/app/widget/serviceFrame")
public interface IAppServiceFrameService {


    @PostMapping(path = "/create")
    KbEntity<AppServiceFrameModel> create(
            @RequestBody() AppServiceFrameModel model
    );

    @PostMapping(path = "/update")
    KbEntity<AppServiceFrameModel> update(
            @RequestBody() AppServiceFrameModel model
    );

    @PostMapping(path = "/deleteById")
    KbEntity<Void> deleteById(
            @RequestParam String id
    );

    @PostMapping(path = "/updateStateById")
    KbEntity<Void> updateStateById(
            @RequestParam() String id,
            @RequestParam() @ApiParam("tab状态,1-草稿,2-内测版本,3-上线") Integer state
    );

    @ApiOperation("刷新上线时间为当前时间强制切换当前最新使用配置,【避险接口,不要随便调用,使用场景仅限需要快速切换到其他配置中】")
    @PostMapping(path = "/refreshOnlineTime")
    KbEntity<Void> refreshOnlineTime(
            @RequestParam() String id
    );

    @PostMapping(path = "/getById")
    KbEntity<AppServiceFrameModel> getById(
            @RequestParam() String id
    );

    @PostMapping(path = "/getListBySiteIdAndStates")
    KbEntity<List<AppServiceFrameModel>> getListBySiteIdAndStates(
            @RequestParam() Integer siteId,
            @RequestParam(required = false) List<Integer> states,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );


}
