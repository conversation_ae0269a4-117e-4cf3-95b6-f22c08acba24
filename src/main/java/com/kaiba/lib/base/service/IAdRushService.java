package com.kaiba.lib.base.service;

import com.kaiba.lib.base.annotation.api.KbCheckSignature;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.domain.program.adrush.AdRushModel;
import com.kaiba.lib.base.domain.program.adrush.ActiveTimeModel;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;

/**
 * author: yeqq
 * date: 21-02-05
 */
@RequestMapping("/adRush")
public interface IAdRushService {

    @ApiOperation("创建广告抽奖")
    @PostMapping(path = "/createAdRushByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<AdRushModel> createAdRushByBody(
            @RequestBody() AdRushModel adRushModel
    );

    @ApiOperation("更新广告抽奖")
    @PostMapping(path = "/updateAdRushByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<AdRushModel> updateAdRushByBody(
            @RequestBody() AdRushModel adRushModel
    );

    @ApiOperation("更新广告抽奖关联时间")
    @PostMapping(path = "/updateActiveTimeByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<ActiveTimeModel> updateActiveTimeByBody(
            @RequestBody() ActiveTimeModel activeTimeModel
    );

    @ApiOperation("根据adRushId删除")
    @PostMapping(path = "/deleteAdRushById")
    KbEntity<Void> deleteAdRushById(
            @RequestParam(name = "adRushId") String adRushId
    );

    @KbCheckSignature(type = KbSignType.CALLBACK)
    @PostMapping(path = "/rushStateChangeCallback")
    KbEntity<Void> rushStateChangeCallback(
            @RequestParam(name = "rushId") String rushId,
            @RequestParam(name = "lastState") Integer lastState,
            @RequestParam(name = "currentState") Integer currentState
    );

    @ApiOperation("根据adRushId获取广告抽奖")
    @PostMapping(path = "/getAdRushById")
    KbEntity<AdRushModel> getAdRushById(
            @RequestParam(name = "adRushId") String adRushId
    );

    @ApiOperation("根据siteId与时间获取广告抽奖")
    @PostMapping(path = "/getAdRushListBySiteIdAndTime")
    KbEntity<List<AdRushModel>> getAdRushListBySiteIdAndTime(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "startTime") Long startTime,
            @RequestParam(name = "endTime") Long endTime,
            @RequestParam(name = "states",required = false) Integer[] states,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @PostMapping(path = "/getActiveTimeListByTimeBetween")
    KbEntity<List<ActiveTimeModel>> getActiveTimeListByTimeBetween(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "states",required = false) Integer[] states,
            @RequestParam(name = "startTime") Long startTime,
            @RequestParam(name = "endTime") Long endTime,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @ApiOperation("根据siteId获取广告抽奖列表")
    @PostMapping(path = "/getAdRushListBySiteId")
    KbEntity<List<AdRushModel>> getAdRushListBySiteId(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "states", required = false) Integer[] states,
            @RequestParam(name = "types", required = false) Integer[] types,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );
}
