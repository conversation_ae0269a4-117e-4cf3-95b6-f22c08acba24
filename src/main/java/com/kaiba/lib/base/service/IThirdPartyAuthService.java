package com.kaiba.lib.base.service;

import com.kaiba.lib.base.domain.thirdparty.ThirdPartyAccessModel;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * author: lyux
 * date: 2020-09-03
 */
public interface IThirdPartyAuthService {

    @ApiOperation("三方根据规则申请 token. 过期时间单位为秒.")
    @RequestMapping(path = "/thirdparty/admin/auth/acquireToken", method = RequestMethod.POST)
    KbEntity<String> acquireToken(
            @RequestParam(name = "accessId") String accessId,
            @RequestParam(name = "expire", required = false, defaultValue = "10800") Long expire
    );

    @ApiOperation("获取三方凭证信息. 此接口不会返回私钥信息.")
    @RequestMapping(path = "/thirdparty/admin/auth/getAccessById", method = RequestMethod.POST)
    KbEntity<ThirdPartyAccessModel> getAccessById(
            @RequestParam(name = "accessId") String accessId,
            @RequestParam(name = "allowCache", required = false, defaultValue = "true") Boolean allowCache
    );

    @ApiOperation("获取三方凭证中的签名盐值. 注意! 盐值应尽量保密.")
    @RequestMapping(path = "/thirdparty/admin/auth/getAccessSaltById", method = RequestMethod.POST)
    KbEntity<String> getAccessSaltById(
            @RequestParam(name = "accessId") String accessId,
            @RequestParam(name = "allowCache", required = false, defaultValue = "true") Boolean allowCache
    );

    @ApiOperation("获取三方凭证中的私钥信息. 注意! 私钥应尽量保密.")
    @RequestMapping(path = "/thirdparty/admin/auth/getAccessPrivateKeyById", method = RequestMethod.POST)
    KbEntity<String> getAccessPrivateKeyById(
            @RequestParam(name = "accessId") String accessId
    );

    @ApiOperation("获取三方凭证中的公钥信息. 注意! 公钥应尽量保密.")
    @RequestMapping(path = "/thirdparty/admin/auth/getAccessPublicKeyById", method = RequestMethod.POST)
    KbEntity<String> getAccessPublicKeyById(
            @RequestParam(name = "accessId") String accessId
    );

    @ApiOperation("获取全部三方凭证信息. 此接口不会返回私钥信息.")
    @RequestMapping(path = "/thirdparty/admin/auth/getAccessListAll", method = RequestMethod.POST)
    KbEntity<List<ThirdPartyAccessModel>> getAccessListAll(
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );

    @ApiOperation("创建三方凭证")
    @RequestMapping(path = "/thirdparty/admin/auth/createAccess", method = RequestMethod.POST)
    KbEntity<ThirdPartyAccessModel> createAccess(
            @RequestParam(name = "name") String name,
            @RequestParam(name = "description", required = false) String description
    );

    @ApiOperation("更新凭证描述")
    @RequestMapping(path = "/thirdparty/admin/auth/updateAccessDescription", method = RequestMethod.POST)
    KbEntity<Void> updateAccessDescription(
            @RequestParam(name = "accessId") String accessId,
            @RequestParam(name = "description") String description
    );

    @ApiOperation("启用/禁用 三方凭证. 禁用凭证后三方将无法获取 token 或根据 token 请求接口.")
    @RequestMapping(path = "/thirdparty/admin/auth/updateAccessEnabled", method = RequestMethod.POST)
    KbEntity<Void> updateAccessEnabled(
            @RequestParam(name = "accessId") String accessId,
            @RequestParam(name = "enabled") Boolean enabled
    );

    @ApiOperation("重新生成密钥对. 谨慎操作! 需要知会第三方伙伴.")
    @RequestMapping(path = "/thirdparty/admin/auth/refreshAccessKeyPair", method = RequestMethod.POST)
    KbEntity<String> refreshAccessKeyPair(
            @RequestParam(name = "accessId") String accessId
    );

    @ApiOperation("重新生成签名盐值. 谨慎操作! 需要知会第三方伙伴.")
    @RequestMapping(path = "/thirdparty/admin/auth/refreshAccessSalt", method = RequestMethod.POST)
    KbEntity<String> refreshAccessSalt(
            @RequestParam(name = "accessId") String accessId
    );

    @ApiOperation("重新生成长效鉴权令牌. 谨慎操作! 需要知会第三方伙伴.")
    @RequestMapping(path = "/thirdparty/admin/auth/refreshAccessToken", method = RequestMethod.POST)
    KbEntity<String> refreshAccessToken(
            @RequestParam(name = "accessId") String accessId,
            @RequestParam(name = "expireAt") @ApiParam("过期时间点, 单位毫秒") Long expireAtMS
    );

    @ApiOperation("根据 access 加密数据. 产生的数据为 base64 后的字符串.")
    @RequestMapping(path = "/thirdparty/admin/auth/encryptByAccess", method = RequestMethod.POST)
    KbEntity<String> encryptByAccess(
            @RequestParam(name = "accessId") String accessId,
            @RequestParam(name = "data") String data,
            @RequestParam(name = "considerEnabled", required = false, defaultValue = "true") Boolean considerEnabled
    );

    @ApiOperation("根据 access 解密数据. 提供 data 参数为 base64 后的字符串.")
    @RequestMapping(path = "/thirdparty/admin/auth/decryptByAccess", method = RequestMethod.POST)
    KbEntity<String> decryptByAccess(
            @RequestParam(name = "accessId") String accessId,
            @RequestParam(name = "data") String data,
            @RequestParam(name = "considerEnabled", required = false, defaultValue = "true") Boolean considerEnabled
    );

}
