package com.kaiba.lib.base.service;

import com.kaiba.lib.base.domain.splash.SplashModel;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import java.util.List;
/**
 * author: yeqq
 * date: 20-11-25
 */
@RequestMapping("/splash")
public interface ISplashService {

    @ApiOperation("创建开屏广告")
    @PostMapping(path = "/createSplashByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<SplashModel> createSplashByBody(
            @RequestBody() SplashModel splash
    );

    @ApiOperation("更新开屏广告")
    @PostMapping(path = "/updateSplashByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<SplashModel> updateSplashByBody(
            @RequestBody() SplashModel splash
    );

    @ApiOperation("开始开屏广告")
    @PostMapping(path = "/startSplash")
    KbEntity<SplashModel> startSplash(
            @RequestParam(name = "splashId") String splashId,
            @RequestParam(name = "userId") Integer userId
    );

    @ApiOperation("结束开屏广告")
    @PostMapping(path = "/endSplash")
    KbEntity<SplashModel> endSplash(
            @RequestParam(name = "splashId") String splashId,
            @RequestParam(name = "userId") Integer userId
    );

    @ApiOperation("取消开屏广告")
    @PostMapping(path = "/cancelSplash")
    KbEntity<SplashModel> cancelSplash(
            @RequestParam(name = "splashId") String splashId,
            @RequestParam(name = "userId") Integer userId
    );

    @ApiOperation("根据splashId获取开屏广告")
    @PostMapping(path = "/getSplashById")
    KbEntity<SplashModel> getSplashById(
            @RequestParam(name = "splashId") String splashId,
            @RequestParam(name = "needCount", required = false, defaultValue = "false") Boolean needCount
    );

    @ApiOperation("根据siteId获取开屏广告列表")
    @PostMapping(path = "/getSplashListBySiteId")
    KbEntity<List<SplashModel>> getSplashListBySiteId(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "states",required = false) Integer[] states,
            @RequestParam(name = "startTime",required = false) Long startTime,
            @RequestParam(name = "endTime",required = false) Long endTime,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize,
            @RequestParam(name = "needCount", required = false, defaultValue = "false") Boolean needCount
    );

    @ApiOperation("获取结束时间大于当前时间的开屏广告列表")
    @PostMapping(path = "/getSplashListByEndTimeGT")
    KbEntity<List<SplashModel>> getSplashListByEndTimeGT(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "states",required = false) Integer[] states,
            @RequestParam(name = "endTime",required = false) Long endTime,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize,
            @RequestParam(name = "needCount", required = false, defaultValue = "false") Boolean needCount
    );
}
