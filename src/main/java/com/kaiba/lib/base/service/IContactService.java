package com.kaiba.lib.base.service;

import com.kaiba.lib.base.domain.AttrUpdater;
import com.kaiba.lib.base.domain.contact.ContactGroupModel;
import com.kaiba.lib.base.domain.contact.ContactModel;
import com.kaiba.lib.base.domain.contact.ContactModifyModel;
import com.kaiba.lib.base.domain.contact.ContactRelationModel;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.util.ArrayTypeHolder;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 20-09-29
 */
public interface IContactService {

    // ------------------------------------------------------------
    // about user contact

    @ApiOperation("获取用户模板地址列表")
    @RequestMapping(path = "/contact/user/address/getUserTemplateAddressList", method = RequestMethod.POST)
    KbEntity<List<ContactModel>> getUserTemplateAddressList(
            @RequestParam() Integer userId,
            @RequestParam(required = false) Integer[] state,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "15") Integer pageSize
    );

    @ApiOperation("获取用户模板地址. 如果模板地址的用户 ID 和 userId 参数不同, 则不返回指定地址")
    @RequestMapping(path = "/contact/user/address/getUserTemplateAddress", method = RequestMethod.POST)
    KbEntity<ContactModel> getUserTemplateAddress(
            @RequestParam() Integer userId,
            @RequestParam() String contactId
    );

    @ApiOperation("获取用户默认模板地址")
    @RequestMapping(path = "/contact/user/address/getDefaultUserTemplateAddress", method = RequestMethod.POST)
    KbEntity<ContactModel> getDefaultUserTemplateAddress(
            @RequestParam() Integer userId
    );

    @ApiOperation("获取用户默认模板地址, 如果未设置默认地址, 则获取按权重排序的第一个地址")
    @RequestMapping(path = "/contact/user/address/getFirstUserTemplateAddress", method = RequestMethod.POST)
    KbEntity<ContactModel> getFirstUserTemplateAddress(
            @RequestParam() Integer userId
    );

    @ApiOperation("设置默认的用户模板地址")
    @RequestMapping(path = "/contact/user/address/setUserTemplateAddressAsDefault", method = RequestMethod.POST)
    KbEntity<Void> setUserTemplateAddressAsDefault(
            @RequestParam() String contactId
    );

    @ApiOperation("创建模板地址")
    @RequestMapping(path = "/contact/user/address/createUserTemplateAddress", method = RequestMethod.POST)
    KbEntity<ContactModel> createUserTemplateAddress(
            @RequestBody() ContactModifyModel address
    );

    @ApiOperation("根据用户模板地址创建实用地址")
    @RequestMapping(path = "/contact/user/address/createUserImplAddressFromTemplate", method = RequestMethod.POST)
    KbEntity<ContactModel> createUserImplAddressFromTemplate(
            @RequestParam() Integer userId,
            @RequestParam() String contactId
    );

    // ---------------------------------------------------------------

    @ApiOperation("创建用户手机联系方式")
    @RequestMapping(path = "/contact/user/phone/createUserPhoneContactWithVCode", method = RequestMethod.POST)
    KbEntity<ContactModel> createUserPhoneContactWithVCode(
            @RequestParam() Integer userId,
            @RequestParam() String phone,
            @RequestParam() String vcode,
            @RequestParam() @ApiParam("新生成的联系人的分组") String groupKey,
            @RequestParam(required = false) String remark,
            @RequestParam(required = false, defaultValue = "true") @ApiParam("如果满足条件的联系人已存在, 是否新建") Boolean createOnExist
    );

    @ApiOperation("创建用户手机联系方式")
    @RequestMapping(path = "/contact/user/phone/createUserPhoneContact", method = RequestMethod.POST)
    KbEntity<ContactModel> createUserPhoneContact(
            @RequestParam() Integer userId,
            @RequestParam() String phone,
            @RequestParam() @ApiParam("新生成的联系人的分组") String groupKey,
            @RequestParam(required = false) String remark,
            @RequestParam(required = false, defaultValue = "true") @ApiParam("如果满足条件的联系人已存在, 是否新建") Boolean createOnExist
    );

    @ApiOperation("创建用户手机绑定历史记录")
    @RequestMapping(path = "/contact/user/phone/createUserPhoneHistory", method = RequestMethod.POST)
    KbEntity<ContactModel> createUserPhoneHistory(
            @RequestParam() Integer userId,
            @RequestParam() String phone
    );

    @ApiOperation("创建用户手机联系方式, 根据用户信息中的手机号创建")
    @RequestMapping(path = "/contact/user/phone/createUserPhoneContactFromUser", method = RequestMethod.POST)
    KbEntity<ContactModel> createUserPhoneContactFromUser(
            @RequestParam() Integer userId,
            @RequestParam() @ApiParam("新生成的联系人的分组") String groupKey,
            @RequestParam(required = false) String remark,
            @RequestParam(required = false, defaultValue = "true") @ApiParam("如果满足条件的联系人已存在, 是否新建") Boolean createOnExist
    );

    @ApiOperation("创建用户手机联系方式")
    @RequestMapping(path = "/contact/user/phone/createUserPhoneContactFromContact", method = RequestMethod.POST)
    KbEntity<ContactModel> createUserPhoneContactFromContact(
            @RequestParam() Integer userId,
            @RequestParam() String contactId,
            @RequestParam() @ApiParam("新生成的联系人的分组") String groupKey,
            @RequestParam(required = false) String remark
    );

    @ApiOperation(
            "获取可以用作创建用户手机联系方式的手机号列表. 包括用户信息中的手机号, 和模板地址薄中的手机号. " +
            "出于隐私考虑, 暂时只返回这两个渠道的用户手机号.")
    @RequestMapping(path = "/contact/user/phone/getUserPhoneCandidateList", method = RequestMethod.POST)
    KbEntity<List<String>> getUserPhoneCandidateList(
            @RequestParam() Integer userId
    );

    // ---------------------------------------------------------------

    @RequestMapping(path = "/contact/createContact", method = RequestMethod.POST)
    KbEntity<ContactModel> createContact(
            @RequestBody() ContactModifyModel createModel
    );

    @RequestMapping(path = "/contact/createContactFromContact", method = RequestMethod.POST)
    KbEntity<ContactModel> createContactFromContact(
            @RequestParam(required = false) Integer userId,
            @RequestParam() String contactId,
            @RequestParam() @ApiParam("新生成的联系人的分组") String groupKey,
            @RequestParam(required = false) String remark,
            @RequestParam(required = false, defaultValue = "false") @ApiParam("是否刷新 contactId 对应的联系人. 只影响其排序") Boolean refreshTemplate
    );

    @RequestMapping(path = "/contact/updateContactData", method = RequestMethod.POST)
    KbEntity<ContactModel> updateContactData(
            @RequestBody() ContactModifyModel modifyModel
    );

    @RequestMapping(path = "/contact/updateContactState", method = RequestMethod.POST)
    KbEntity<Void> updateContactState(
            @RequestParam() String contactId,
            @RequestParam() Integer state
    );

    @RequestMapping(path = "/contact/updateContactAsDefault", method = RequestMethod.POST)
    KbEntity<Void> updateContactAsDefault(
            @RequestParam() String contactId
    );

    @RequestMapping(path = "/contact/updateContactAttr", method = RequestMethod.POST)
    KbEntity<ContactModel> updateContactAttr(
            @RequestBody() AttrUpdater attrUpdater
    );

    @ApiOperation("更新或新增 attr 字段键值")
    @RequestMapping(path = "/contact/putContactAttrKeyValue", method = RequestMethod.POST)
    KbEntity<ContactModel> updateContactAttrKeyValue(
            @RequestParam() String contactId,
            @RequestParam() String key,
            @RequestParam() String value
    );

    @RequestMapping(path = "/contact/cancelContactAsDefault", method = RequestMethod.POST)
    KbEntity<Void> cancelContactAsDefault(
            @RequestParam() String contactId
    );

    @RequestMapping(path = "/contact/refreshContact", method = RequestMethod.POST)
    KbEntity<Void> refreshContact(
            @RequestParam() String contactId
    );

    @RequestMapping(path = "/contact/deleteContact", method = RequestMethod.POST)
    KbEntity<Void> deleteContact(
            @RequestParam() String contactId
    );

    @RequestMapping(path = "/contact/getContactById", method = RequestMethod.POST)
    KbEntity<ContactModel> getContactById(
            @RequestParam() String contactId
    );

    @RequestMapping(path = "/contact/getContactListByIdIn", method = RequestMethod.POST)
    KbEntity<List<ContactModel>> getContactListByIdIn(
            @RequestParam() String[] contactIds
    );

    @RequestMapping(path = "/contact/getContactListByUserId", method = RequestMethod.POST)
    KbEntity<List<ContactModel>> getContactListByUserId(
            @RequestParam() Integer userId,
            @RequestParam(required = false) String groupKey,
            @RequestParam(required = false) Integer[] state,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "15") Integer pageSize
    );

    @RequestMapping(path = "/contact/getContactListByPhone", method = RequestMethod.POST)
    KbEntity<List<ContactModel>> getContactListByPhone(
            @RequestParam() String phone,
            @RequestParam(required = false) String groupKey,
            @RequestParam(required = false) Integer[] state,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "15") Integer pageSize
    );

    @RequestMapping(path = "/contact/getContactListByGroup", method = RequestMethod.POST)
    KbEntity<List<ContactModel>> getContactListByGroup(
            @RequestParam(required = false) String groupKey,
            @RequestParam(required = false) Integer[] state,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "15") Integer pageSize
    );

    @RequestMapping(path = "/contact/existsByContactGroupAndUserId", method = RequestMethod.POST)
    KbEntity<Boolean> existsByContactGroupAndUserId(
            @RequestParam() Integer userId,
            @RequestParam() String groupKey
    );

    // ---------------------------------------------------------------

    @ApiParam("获取关系. 字段意义举例: 张三(主语) 是 小明(谓语) 的 舅舅(关系)")
    @RequestMapping(path = "/contact/getRelation", method = RequestMethod.POST)
    KbEntity<ContactRelationModel> getRelation(
            @RequestParam() @ApiParam("主语") String subjectContactId,
            @RequestParam() @ApiParam("谓语") String contactId
    );

    @ApiParam("根据谓语获取关系列表. 在条件只有 contactId 时翻页参数才有效. 字段意义举例: 张三(主语) 是 小明(谓语) 的 舅舅(关系)")
    @RequestMapping(path = "/contact/getRelationListByContact", method = RequestMethod.POST)
    KbEntity<List<ContactRelationModel>> getRelationListByContact(
            @RequestParam() @ApiParam("谓语") String contactId,
            @RequestParam(required = false) @ApiParam("主语") String[] subjectContactIds,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "15") Integer pageSize
    );

    @ApiParam("获取联系人列表, 并根据谓语获取关系列表.")
    @RequestMapping(path = "/contact/getContactListWithContactRelation", method = RequestMethod.POST)
    KbEntity<List<ContactModel>> getContactListWithContactRelation(
            @RequestParam() @ApiParam("谓语") String contactId,
            @RequestParam() @ApiParam("主语") String[] subjectContactIds
    );

    @ApiParam("根据主语获取关系列表. 在条件只有 subjectContactId 时翻页参数才有效. 字段意义举例: 张三(主语) 是 小明(谓语) 的 舅舅(关系)")
    @RequestMapping(path = "/contact/getRelationListBySubjectContact", method = RequestMethod.POST)
    KbEntity<List<ContactRelationModel>> getRelationListBySubjectContact(
            @RequestParam() @ApiParam("主语") String subjectContactId,
            @RequestParam(required = false) @ApiParam("谓语") String[] contactIds,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "15") Integer pageSize
    );

    @ApiParam("获取联系人列表, 并根据谓语获取关系列表.")
    @RequestMapping(path = "/contact/getContactListWithSubjectRelation", method = RequestMethod.POST)
    KbEntity<List<ContactModel>> getContactListWithSubjectRelation(
            @RequestParam() @ApiParam("主语") String subjectContactId,
            @RequestParam() @ApiParam("谓语") String[] contactIds
    );

    @ApiParam("设置关系. 字段意义举例: 张三(主语) 是 小明(谓语) 的 舅舅(关系)")
    @RequestMapping(path = "/contact/setRelation", method = RequestMethod.POST)
    KbEntity<ContactRelationModel> setRelation(
            @RequestParam() @ApiParam("主语") String subjectContactId,
            @RequestParam() @ApiParam("谓语") String contactId,
            @RequestParam() @ApiParam("关系") String relation
    );

    @ApiParam("设置关系. 字段意义举例: 张三(主语) 是 小明(谓语) 的 舅舅(关系)")
    @RequestMapping(path = "/contact/setRelationListByBody", method = RequestMethod.POST)
    KbEntity<List<ContactRelationModel>> setRelationListByBody(
            @RequestBody() List<ContactRelationModel> relations
    );

    @ApiParam("清除关系.")
    @RequestMapping(path = "/contact/removeRelation", method = RequestMethod.POST)
    KbEntity<Void> removeRelation(
            @RequestParam() String relationId
    );

    @ApiParam("根据主谓清除关系. 字段意义举例: 张三(主语) 是 小明(谓语) 的 舅舅(关系)")
    @RequestMapping(path = "/contact/removeRelationByContact", method = RequestMethod.POST)
    KbEntity<Void> removeRelationByContact(
            @RequestParam() @ApiParam("主语") String subjectContactId,
            @RequestParam() @ApiParam("谓语") String contactId
    );

    // ---------------------------------------------------------------

    @RequestMapping(path = "/contact/group/getContactGroupList", method = RequestMethod.POST)
    KbEntity<List<ContactGroupModel>> getContactGroupList(
            @RequestParam(required = false) Long[] constraint,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "15") Integer pageSize
    );

    @ApiOperation("获取组实例, id 和 key 至少提供一个")
    @RequestMapping(path = "/contact/group/getContactGroup", method = RequestMethod.POST)
    KbEntity<ContactGroupModel> getContactGroup(
            @RequestParam(required = false) String groupId,
            @RequestParam(required = false) String groupKey
    );

    @RequestMapping(path = "/contact/group/createContactGroup", method = RequestMethod.POST)
    KbEntity<ContactGroupModel> createContactGroup(
            @RequestParam() String groupKey,
            @RequestParam() Long constraint,
            @RequestParam(required = false, defaultValue = "false") Boolean mutable,
            @RequestParam(required = false, defaultValue = "false") Boolean allowDefault,
            @RequestParam(required = false) Integer limitPerUser,
            @RequestParam(required = false) String description
    );

    @ApiOperation("更新分组实例")
    @RequestMapping(path = "/contact/group/updateContactGroupConstraint", method = RequestMethod.POST)
    KbEntity<ContactGroupModel> updateContactGroupConstraint(
            @RequestParam() String groupKey,
            @RequestParam() Long constraint,
            @RequestParam(required = false, defaultValue = "false") Boolean scanContacts
    );

    @ApiOperation("更新分组实例")
    @RequestMapping(path = "/contact/group/updateContactGroupMutable", method = RequestMethod.POST)
    KbEntity<ContactGroupModel> updateContactGroupMutable(
            @RequestParam() String groupKey,
            @RequestParam() Boolean mutable
    );

    @ApiOperation("更新固定实例分组. 限内部使用.")
    @RequestMapping(path = "/contact/group/createOrUpdateFixedGroups", method = RequestMethod.POST)
    KbEntity<Void> createOrUpdateFixedGroups(
            @RequestParam() String secret
    );

    // ---------------------------------------------------------------

    default KbEntity<Map<String, ContactModel>> getContactMapByIds(Set<String> set) {
        KbEntity<List<ContactModel>> result = getContactListByIdIn(
                set.toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY));
        if (result.isOk()) {
            return new KbEntity<>(result.getData().stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(ContactModel::getId, add -> add, (k1, k2) -> k2)));
        } else {
            KbEntity<Map<String, ContactModel>> entity = new KbEntity<>(result.getKbCode());
            entity.addKbDebug(result.getKbDebugs());
            return entity;
        }
    }

}
