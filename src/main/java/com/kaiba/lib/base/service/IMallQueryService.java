package com.kaiba.lib.base.service;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.mall.commodity.MallInfoVO;
import com.kaiba.lib.base.domain.mall.commodity.MallQueryVO;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Description: 商城查询Service
 * Author: ZM227
 * Date: 2025/5/21 11:41
 */
public interface IMallQueryService {

    @PostMapping(path = "/service/mall/usr/mall/query/queryMallByCondition")
    @ApiOperation(value = "条件查询商城列表")
    KbEntity<List<MallInfoVO>> queryMallByCondition(
        @RequestHeader(value = KbHeader.KB_USER_ID) Integer userId,
        @RequestBody MallQueryVO queryVO);

    @PostMapping(path = "/service/mall/usr/mall/query/queryMallDetail")
    @ApiOperation(value = "查询商城详情")
    KbEntity<MallInfoVO> queryMallDetail(@RequestHeader(value = KbHeader.KB_USER_ID) Integer userId,
        @RequestParam(required = false) String mallKey,
        @RequestParam(required = false) String mallCode);

}
