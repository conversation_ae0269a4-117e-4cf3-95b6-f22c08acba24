package com.kaiba.lib.base.service;

import com.kaiba.lib.base.domain.banner.AppBannerModel;
import com.kaiba.lib.base.domain.banner.BannerModel;
import com.kaiba.lib.base.domain.banner.BannerModuleModel;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import java.util.List;

/**
 * deprecated in honor of {@link IAppBannerService}
 */
@RequestMapping("/banner")
public interface IBannerService {

    @ApiOperation("创建BannerModule")
    @PostMapping(path = "/createBannerModuleByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<BannerModuleModel> createBannerModuleByBody(
            @RequestBody() BannerModuleModel bannerModuleModel
    );

    @ApiOperation("更新BannerModule")
    @PostMapping(path = "/updateBannerModuleByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<BannerModuleModel> updateBannerModuleByBody(
            @RequestBody() BannerModuleModel bannerModuleModel
    );

    @ApiOperation("创建banner")
    @PostMapping(path = "/createBannerByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<BannerModel> createBannerByBody(
            @RequestBody() BannerModel bannerModel
    );

    @ApiOperation("更新banner")
    @PostMapping(path = "/updateBannerByBody", consumes = "application/json;charset=UTF-8")
    KbEntity<BannerModel> updateBannerByBody(
            @RequestBody() BannerModel bannerModel
    );

    @ApiOperation("更新所占屏位")
    @PostMapping(path = "/updateBannerHomeOrder")
    KbEntity<BannerModel> updateBannerHomeOrder(
            @RequestParam(name = "bannerId") String bannerId,
            @RequestParam(name = "homeOrder") Integer homeOrder
    );

    @ApiOperation("根据banner状态")
    @PostMapping(path = "/updateBannerState")
    KbEntity<BannerModel> updateBannerState(
            @RequestParam(name = "bannerId") String bannerId,
            @RequestParam(name = "state") Integer state
    );

    @ApiOperation("签发")
    @PostMapping(path = "/sign")
    KbEntity<BannerModel> sign(
            @RequestParam(name = "bannerId") String bannerId,
            @RequestParam(name = "homeOrder") Integer homeOrder
    );

    @ApiOperation("开始")
    @PostMapping(path = "/start")
    KbEntity<BannerModel> start(
            @RequestParam(name = "bannerId") String bannerId
    );

    @ApiOperation("暂停")
    @PostMapping(path = "/pause")
    KbEntity<BannerModel> pause(
            @RequestParam(name = "bannerId") String bannerId
    );

    @ApiOperation("结束")
    @PostMapping(path = "/end")
    KbEntity<BannerModel> end(
            @RequestParam(name = "bannerId") String bannerId
    );

    @ApiOperation("根据bannerId获取banner")
    @PostMapping(path = "/getBannerById")
    KbEntity<BannerModel> getBannerById(
            @RequestParam(name = "bannerId") String bannerId
    );

    @ApiOperation("根据bannerModuleId获取bannerModule")
    @PostMapping(path = "/getBannerModuleById")
    KbEntity<BannerModuleModel> getBannerModuleById(
            @RequestParam(name = "bannerModuleId") String bannerModuleId
    );

    @ApiOperation("根据bannerId获取banner")
    @PostMapping(path = "/app/getAppBannerById")
    KbEntity<BannerModel> getAppBannerById(
            @RequestParam(name = "bannerId") String bannerId
    );

    @ApiOperation("获取banner列表")
    @PostMapping(path = "/app/getAppBannerList")
    KbEntity<List<AppBannerModel>> getAppBannerList(
            @RequestParam(name = "module") String module,
            @RequestParam(name = "siteId", required = false) Integer siteId
    );

    @ApiOperation("根据siteId获取banner列表")
    @PostMapping(path = "/getBannerList")
    KbEntity<List<BannerModel>> getBannerList(
            @RequestParam(name = "module") String module,
            @RequestParam(name = "key", required = false) String key,
            @RequestParam(name = "siteId", required = false) Integer siteId,
            @RequestParam(name = "states", required = false) Integer[] states,
            @RequestParam(name = "homeOrder", required = false) Integer homeOrder,
            @RequestParam(name = "startTime", required = false) Long startTime,
            @RequestParam(name = "endTime", required = false) Long endTime,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    );
}
