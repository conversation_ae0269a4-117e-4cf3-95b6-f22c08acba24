package com.kaiba.lib.base.service;

import com.kaiba.lib.base.domain.car.CarBrandModel;
import com.kaiba.lib.base.domain.car.CarModel;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.util.ArrayTypeHolder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 18-10-2
 */
public interface ICarService {

    @RequestMapping(path = "/car/getCar", method = RequestMethod.POST)
    KbEntity<CarModel> getCar(@RequestParam(name = "carCode") String carCode);

    @RequestMapping(path = "/car/getCarListIn", method = RequestMethod.POST)
    KbEntity<List<CarModel>> getCarListIn(@RequestParam(name = "carCodeList", required = false) String[] carCodeList);

    @RequestMapping(path = "/car/getCarListByBrand", method = RequestMethod.POST)
    KbEntity<List<CarModel>> getCarListByBrand(@RequestParam() String carBrand);

    @RequestMapping(path = "/car/getCarBrand", method = RequestMethod.POST)
    KbEntity<CarBrandModel> getCarBrand(@RequestParam(name = "brandCode") String brandCode);

    @RequestMapping(path = "/car/getCarBrandByCar", method = RequestMethod.POST)
    KbEntity<CarBrandModel> getCarBrandByCar(@RequestParam(name = "carCode") String carCode);

    @RequestMapping(path = "/car/getCarBrandListIn", method = RequestMethod.POST)
    KbEntity<List<CarBrandModel>> getCarBrandListIn(@RequestParam(name = "brandCodeList", required = false) String[] brandCodeList);

    @RequestMapping(path = "/car/invalidateCache", method = RequestMethod.POST)
    KbEntity<String> invalidateCache();

    // -------------------------------------------------

    default KbEntity<Map<String, CarModel>> getCarMapByIds(Set<String> set) {
        KbEntity<List<CarModel>> result = getCarListIn(set.toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY));
        if (result.isOk()) {
            return new KbEntity<>(result.getData().stream()
                    .collect(Collectors.toMap(CarModel::getCode, car -> car, (k1, k2) -> k2)));
        } else {
            return new KbEntity<Map<String, CarModel>>(result.getKbCode())
                    .addKbDebug(result.getKbDebugs());
        }
    }

    default KbEntity<Map<String, CarBrandModel>> getCarBrandMapByIds(Set<String> set) {
        String[] brands = new String[set.size()];
        KbEntity<List<CarBrandModel>> result = getCarBrandListIn(set.toArray(brands));
        if (result.isOk()) {
            return new KbEntity<>(result.getData().stream()
                    .collect(Collectors.toMap(CarBrandModel::getCode, brand -> brand, (k1, k2) -> k2)));
        } else {
            return new KbEntity<Map<String, CarBrandModel>>(result.getKbCode())
                    .addKbDebug(result.getKbDebugs());
        }
    }

}
