package com.kaiba.lib.base.service;

import com.kaiba.lib.base.annotation.apiparam.KbPage;
import com.kaiba.lib.base.annotation.apiparam.KbPageSize;
import com.kaiba.lib.base.annotation.apiparam.KbUserId;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.workorder.WOOperation;
import com.kaiba.lib.base.domain.AttrUpdater;
import com.kaiba.lib.base.domain.workorder.*;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 2024-01-17
 *
 * 通用工单系统
 */
@RequestMapping("/WorkOrder/service")
public interface IWorkOrderService {

    // ------------------------------------------------------------
    // 案件相关接口

    /** {@link WOOperation#ACCEPT} */
    @ApiOperation("受理案件")
    @PostMapping("/case/act/acceptCase")
    KbEntity<WOEventModel> acceptCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() @ApiParam("解决者") String resolver,
            @RequestParam() String caseId,
            @RequestParam(required = false) String noteId,
            @RequestParam(required = false) String message
    );

    /** {@link WOOperation#SUGGEST} */
    @ApiOperation("向主持者提出处理建议. noteId 和 message 必传一个, 若都传则只 noteId 生效.")
    @PostMapping("/case/act/suggestCase")
    KbEntity<WOEventModel> suggestCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam(required = false) String noteId,
            @RequestParam(required = false) String message
    );

    /** {@link WOOperation#BACK} */
    @ApiOperation("将案件交由主持者处理. noteId 和 message 必传一个, 若都传则只 noteId 生效.")
    @PostMapping("/case/act/backCase")
    KbEntity<WOEventModel> backCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam(required = false) String noteId,
            @RequestParam(required = false) String message
    );

    /** {@link WOOperation#HANDOVER} */
    @ApiOperation("转交案件给另一位处理者. noteId 和 message 都可以留空, 两个都传则 noteId 优先生效.")
    @PostMapping("/case/act/handoverCase")
    KbEntity<WOEventModel> handoverCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam() String resolver,
            @RequestParam(required = false) String noteId,
            @RequestParam(required = false) String message
    );

    /** {@link WOOperation#CLOSE_SUGGEST} */
    @ApiOperation("建议结案. noteId 和 message 都可以留空, 两个都传则 noteId 优先生效.")
    @PostMapping("/case/act/suggestCloseCase")
    KbEntity<WOEventModel> suggestCloseCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam(required = false) String noteId,
            @RequestParam(required = false) String message
    );

    /** {@link WOOperation#CLOSE} */
    @ApiOperation("关闭案件. noteId 和 message 都可以留空, 两个都传则 noteId 优先生效.")
    @PostMapping("/case/act/closeCase")
    KbEntity<WOEventModel> closeCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam() String closeType,
            @RequestParam(required = false) String closeReason
    );

    @ApiOperation("归档案件. 归档后案件将不可再做变更, 也不可再添加新的事件.")
    @PostMapping("/case/act/archiveCase")
    KbEntity<Void> archiveCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam() Boolean archived
    );

    @ApiOperation("变更案件的可见性配置.")
    @PostMapping("/case/act/updateCaseACL")
    KbEntity<Void> updateCaseACL(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() WOACLUpdateModel model
    );

    @ApiOperation("变更案件的统计评分.")
    @PostMapping("/case/act/updateCaseStatRating")
    KbEntity<Void> updateCaseStatRating(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam(required = false) Integer statRating
    );

    @ApiOperation("变更案件备注内容.")
    @PostMapping("/case/act/updateCaseRemark")
    KbEntity<Void> updateCaseRemark(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam() String remark
    );

    @ApiOperation("变更案件事件的内容.")
    @PostMapping("/case/act/updateEventContent")
    KbEntity<Void> updateEventContent(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam() String eventId,
            @RequestParam() String content
    );

    @ApiOperation("获取案件, 内容以业务内容给出")
    @PostMapping("/case/obj/getCaseById")
    KbEntity<WOCaseModel<Object>> getCaseById(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam(required = false) @ApiParam("返回的事件数") Integer eventSize
    );

    @ApiOperation("获取案件, 内容以摘要形式给出")
    @PostMapping("/case/obj/getCaseBriefById")
    KbEntity<WOCaseModel<WOCaseContentBrief>> getCaseBriefById(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam(required = false) @ApiParam("返回的事件数") Integer eventSize
    );

    @ApiOperation("获取案件列表, 内容以业务内容形式给出, 因此每个业务的内容结构可能会不一样")
    @PostMapping("/case/obj/getCaseList")
    KbEntity<List<WOCaseModel<Object>>> getCaseList(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() WOCaseListQueryModel query
    );

    @ApiOperation("获取案件列表, 内容以摘要形式给出")
    @PostMapping("/case/obj/getCaseBriefList")
    KbEntity<List<WOCaseModel<WOCaseContentBrief>>> getCaseBriefList(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() WOCaseListQueryModel query
    );

    @ApiOperation("获取案件列表, 数据由openSearch获取, 内容以业务内容形式给出, 因此每个业务的内容结构可能会不一样")
    @PostMapping("/case/obj/searchCaseList")
    KbEntity<List<WOCaseSearchResult>> searchCaseList(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() WOCaseListSearchModel query
    );

    @ApiOperation("获取案件的事件列表")
    @PostMapping("/case/obj/getEventListByCase")
    KbEntity<List<WOEventModel>> getEventListByCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "20") @KbPageSize Integer pageSize
    );

    @ApiOperation("获取案件的关注者")
    @PostMapping("/case/obj/getFollowerListByCase")
    KbEntity<List<WOFollowerModel>> getFollowerListByCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "20") @KbPageSize Integer pageSize
    );

    @ApiOperation("获取案件的关注者数目")
    @PostMapping("/case/obj/getFollowerCountByCase")
    KbEntity<Long> getFollowerCountByCase(
            @RequestParam() String caseId
    );

    @ApiOperation("获取案件内容ACL配置")
    @PostMapping("/case/obj/getContentACLByCase")
    KbEntity<List<WOACLStringData>> getContentACLByCase(
            @RequestParam() String caseId
    );


    // ------------------------------------------------------------
    // 标签和搜索

    @ApiOperation("创建一级标签")
    @PostMapping("/tag/act/createTopTag")
    KbEntity<WOTagModel> createTopTag(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam(required = false) String key,
            @RequestParam() String name,
            @RequestParam(required = false) String desc,
            @RequestParam(required = false, defaultValue = "-1") Integer per,
            @RequestParam() Integer sub
    );

    @ApiOperation("创建子标签")
    @PostMapping("/tag/act/createSubTag")
    KbEntity<WOTagModel> createSubTag(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() Long parent,
            @RequestParam(required = false) String key,
            @RequestParam() String name,
            @RequestParam(required = false) String desc,
            @RequestParam(required = false, defaultValue = "-1") Integer per
    );

    @ApiOperation("修改标签名. 注意: 此为敏感操作, 需要用户主动确认.")
    @PostMapping("/tag/act/updateTagName")
    KbEntity<Void> updateTagName(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String tagId,
            @RequestParam() String name
    );

    @ApiOperation("修改标签标识. 注意: 此为敏感操作, 需要用户主动确认.")
    @PostMapping("/tag/act/updateTagKey")
    KbEntity<Void> updateTagKey(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String tagId,
            @RequestParam() String key
    );

    @ApiOperation("修改标签描述.")
    @PostMapping("/tag/act/updateTagDesc")
    KbEntity<Void> updateTagDesc(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String tagId,
            @RequestParam() String desc
    );

    @ApiOperation("修改标签状态")
    @PostMapping("/tag/act/updateTagState")
    KbEntity<Void> updateTagState(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String tagId,
            @RequestParam() String state
    );

    @ApiOperation("修改标签状态")
    @PostMapping("/tag/act/removeTag")
    KbEntity<Void> removeTag(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String tagId
    );

    @ApiOperation("获取标签")
    @PostMapping("/tag/obj/getTagById")
    KbEntity<WOTagModel> getTagByIdOrCode(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String tagId
    );

    @ApiOperation("获取标签")
    @PostMapping("/tag/obj/getTagByCode")
    KbEntity<WOTagModel> getTagByCode(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() Long tag,
            @RequestParam(required = false, defaultValue = "true") Boolean allowCache
    );

    @ApiOperation("获取标签")
    @PostMapping("/tag/obj/getTagByKey")
    KbEntity<WOTagModel> getTagByKey(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String key,
            @RequestParam(required = false, defaultValue = "true") Boolean allowCache
    );

    @ApiOperation("获取标签")
    @PostMapping("/tag/obj/getTagListByCodeIn")
    KbEntity<List<WOTagModel>> getTagListByCodeIn(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() Long[] tags,
            @RequestParam(required = false, defaultValue = "true") Boolean allowCache
    );

    @ApiOperation("获取子标签列表")
    @PostMapping("/tag/obj/getTagListByParent")
    KbEntity<List<WOTagModel>> getTagListByParent(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() Long tag,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer pageSize
    );

    @ApiOperation("案件添加标签")
    @PostMapping("/tagged/act/addTagToCase")
    KbEntity<Void> addTagToCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam() Long tag
    );

    @ApiOperation("案件添加标签")
    @PostMapping("/tagged/act/addTagListToCase")
    KbEntity<Void> addTagListToCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam() @ApiParam("最大仅支持同时绑带10个标签") List<Long> tags
    );

    @ApiOperation("案件删除标签")
    @PostMapping("/tagged/act/removeTagFromCase")
    KbEntity<Void> removeTagFromCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam() Long tag
    );

    @ApiOperation("案件删除标签")
    @PostMapping("/tagged/act/removeTagListFromCase")
    KbEntity<Void> removeTagListFromCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam() List<Long> tags
    );

    @ApiOperation("置顶管理: 更新案件在某标签下的排序")
    @PostMapping("/tagged/act/updateTaggedIdx")
    KbEntity<Void> updateTaggedIdx(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam() Long tag,
            @RequestParam(required = false) @ApiParam("为空表示清空") Long idx
    );

    @ApiOperation("置顶管理: 批量更新案件在某标签下的排序")
    @PostMapping("/tagged/act/bulkUpdateTaggedIdx")
    KbEntity<Void> bulkUpdateTaggedIdx(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() WOTaggedIdxUpdateModel model
    );

    @ApiOperation("根据标签获取案件列表")
    @PostMapping("/tagged/obj/getCaseListByTag")
    KbEntity<List<WOCaseModel<WOCaseContentBrief>>> getCaseListByTag(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() Long tag
    );

    @ApiOperation("根据案件获取标签列表")
    @PostMapping("/tagged/obj/getTagListByCase")
    KbEntity<List<WOTagModel>> getTagListByCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId
    );

    @ApiOperation("使用 OpenSearch 获取案件列表")
    @PostMapping("/tagged/obj/searchCaseList")
    KbEntity<List<WOCaseModel<Object>>> searchCaseList(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() WOTagQueryModel query
    );

    @ApiOperation("使用 OpenSearch 获取案件列表")
    @PostMapping("/tagged/obj/searchCaseBriefList")
    KbEntity<List<WOCaseModel<WOCaseContentBrief>>> searchCaseBriefList(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() WOTagQueryModel query
    );

    // ------------------------------------------------------------
    // 用户组相关接口

    @ApiOperation("创建用户组")
    @PostMapping("/team/act/createTeam")
    KbEntity<WOTeamModel> createTeam(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() WOTeamModel team
    );

    @ApiOperation("修改用户组信息")
    @PostMapping("/team/act/updateTeamInfo")
    KbEntity<WOTeamModel> updateTeamInfo(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String teamId,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String abbr,
            @RequestParam(required = false) String avatar
    );

    @ApiOperation("修改用户组状态")
    @PostMapping("/team/act/updateTeamState")
    KbEntity<WOTeamModel> updateTeamState(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String teamId,
            @RequestParam() String state
    );

    @ApiOperation("修改用户组角色")
    @PostMapping("/team/act/updateTeamRole")
    KbEntity<WOTeamModel> updateTeamRole(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String teamId,
            @RequestParam() String[] roles
    );

    @ApiOperation("删除用户组")
    @PostMapping("/team/act/deleteTeam")
    KbEntity<Void> deleteTeam(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String teamId
    );

    @ApiOperation("更新用户组附加属性")
    @PostMapping("/team/act/updateTeamAttr")
    KbEntity<WOTeamModel> updateTeamAttr(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() AttrUpdater attrUpdater
    );

    @ApiOperation("更新用户组指定的附加属性. 若 value 为空表示删除该属性.")
    @PostMapping("/team/act/updateTeamAttrKeyValue")
    KbEntity<WOTeamModel> updateTeamAttrKeyValue(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String teamId,
            @RequestParam() String key,
            @RequestParam(required = false) @ApiParam("为空表示删除") String value
    );

    @ApiOperation("获取用户组详情")
    @PostMapping("/team/obj/getTeamById")
    KbEntity<WOTeamModel> getTeamById(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam(required = false) String teamId
    );

    @ApiOperation("获取用户组详情")
    @PostMapping("/team/obj/getTeamByKey")
    KbEntity<WOTeamModel> getTeamByKey(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam(required = false) String team
    );

    @ApiOperation("获取处理者列表")
    @PostMapping("/team/obj/getResolverTeamListByBiz")
    KbEntity<List<WOTeamModel>> getResolverTeamListByBiz(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam(required = false) String state,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "20") @KbPageSize Integer pageSize
    );

    @ApiOperation("获取协助者列表")
    @PostMapping("/team/obj/getExpertTeamListByBiz")
    KbEntity<List<WOTeamModel>> getExpertTeamListByBiz(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam(required = false) String state,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "20") @KbPageSize Integer pageSize
    );

    @ApiOperation("向用户组添加用户")
    @PostMapping("/member/act/addTeamMember")
    KbEntity<WOTeamMemberModel> addTeamMember(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() WOTeamMemberCreateModel member
    );

    @ApiOperation("更新组员手机号")
    @PostMapping("/member/act/updateTeamMemberMobile")
    KbEntity<Void> updateTeamMemberMobile(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String memberId,
            @RequestParam() String mobile
    );

    @ApiOperation("更新组员信息")
    @PostMapping("/member/act/updateTeamMemberInfo")
    KbEntity<Void> updateTeamMemberInfo(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String memberId,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String nickname,
            @RequestParam(required = false) String realName,
            @RequestParam(required = false) String mobile
    );

    @ApiOperation("更新组员状态")
    @PostMapping("/member/act/updateTeamMemberState")
    KbEntity<Void> updateTeamMemberState(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String memberId,
            @RequestParam() String state
    );

    @ApiOperation("将用户关联到组成员")
    @PostMapping("/member/act/attachUserToTeamMember")
    KbEntity<Void> attachUserToTeamMember(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String memberId,
            @RequestParam() Integer memberUserId
    );

    @ApiOperation("解除组成员用户关联")
    @PostMapping("/member/act/detachUserFromTeamMember")
    KbEntity<Void> detachUserFromTeamMember(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String memberId
    );

    @ApiOperation("从用户组移除用户")
    @PostMapping("/member/act/removeTeamMember")
    KbEntity<Void> removeTeamMember(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String memberId
    );

    @ApiOperation("获取某个组内的用户")
    @PostMapping("/member/obj/getTeamMemberList")
    KbEntity<List<WOTeamMemberModel>> getTeamMemberList(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String teamId,
            @RequestParam(required = false) String state
    );

    // ------------------------------------------------------------
    // 渠道管理. 渠道目前仅影响统计, 不参与逻辑.

    @ApiOperation("创建渠道")
    @PostMapping("/origin/act/createOrigin")
    KbEntity<WOCaseOriginModel> createOrigin(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String origin,
            @RequestParam() String name,
            @RequestParam(required = false) String desc
    );

    @ApiOperation("更新渠道信息")
    @PostMapping("/origin/act/updateOriginData")
    KbEntity<WOCaseOriginModel> updateOriginData(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String originId,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String desc
    );

    @ApiOperation("更新渠道信息: 是否创建时的可选渠道")
    @PostMapping("/origin/act/updateOriginShowOnCreate")
    KbEntity<Void> updateOriginShowOnCreate(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String originId,
            @RequestParam() Boolean show
    );

    @ApiOperation("更新渠道信息: 是否在统计页面展示该渠道数据")
    @PostMapping("/origin/act/updateOriginShowOnStat")
    KbEntity<Void> updateOriginShowOnStat(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String originId,
            @RequestParam() Boolean show
    );

    @ApiOperation("更新渠道排序")
    @PostMapping("/origin/act/updateOriginIdx")
    KbEntity<Void> updateOriginIdx(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String originId,
            @RequestParam(required = false) @ApiParam("为空表示清空") Long idx
    );

    @ApiOperation("批量更新渠道排序")
    @PostMapping("/origin/act/bulkUpdateOriginIdx")
    KbEntity<Void> bulkUpdateOriginIdx(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() WOCaseOriginIdxUpdateModel model
    );

    @ApiOperation("删除渠道")
    @PostMapping("/origin/act/deleteOrigin")
    KbEntity<Void> createOrigin(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String originId
    );

    @ApiOperation("获取渠道列表")
    @PostMapping("/origin/obj/getOriginListByBiz")
    KbEntity<List<WOCaseOriginModel>> getOriginListByBiz(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() Boolean forCaseCreate,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    // ------------------------------------------------------------
    // 统计接口

    @ApiOperation("获取全局统计")
    @PostMapping("/stat/getOverallStat")
    KbEntity<WOStatOverall> getOverallStat(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam(required = false, defaultValue = "true") Boolean allowCache
    );

    @ApiOperation("重新统计某时段内的每日汇总数据")
    @PostMapping("/stat/refreshDailyStat")
    KbEntity<Void> refreshDailyStat(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() @ApiParam("开始日期的时间戳, 单位毫秒") long st,
            @RequestParam() @ApiParam("终止日期的时间戳, 单位毫秒") long et,
            @RequestParam(required = false, defaultValue = "true") Boolean async
    );

    @ApiOperation("按天获取案件的统计数量, 返回表格数据")
    @PostMapping("/stat/getDailyStatTable")
    KbEntity<WOStatNumberTable> getDailyStatTable(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() @ApiParam("开始日期的时间戳, 单位毫秒") long st,
            @RequestParam() @ApiParam("终止日期的时间戳, 单位毫秒") long et
    );

    // ------------------------------------------------------------

    @ApiOperation("获取用户组的操作权限列表")
    @PostMapping("/util/obj/getOperationListByTeam")
    KbEntity<List<WOConfigRole.Opt>> getOperationListByTeam(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() @ApiParam("用户组") String team,
            @RequestParam() @ApiParam("业务标识") String biz
    );

    @ApiOperation("将 ACL 配置转为用户可读的文案")
    @PostMapping("/util/obj/getAclDesc")
    KbEntity<List<WOAccessDescModel>> getAclDesc(
            @RequestBody() WOACLUpdateModel model
    );

    @ApiOperation("新建工单草稿")
    @PostMapping("/util/obj/createDraft")
    KbEntity<WOCaseDraftModel> createDraft(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String content
    );

    @ApiOperation("修改工单草稿")
    @PostMapping("/util/obj/updateDraft")
    KbEntity<WOCaseDraftModel> updateDraft(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String draftId,
            @RequestParam() String content
    );

    @ApiOperation("删除工单草稿")
    @PostMapping("/util/obj/deleteDraft")
    KbEntity<Void> deleteDraft(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String draftId
    );

    @ApiOperation("查询工单草稿")
    @PostMapping("/util/obj/getDraftList")
    KbEntity<List<WOCaseDraftModel>> getDraftList(
            @RequestParam(required = false) Integer operator,
            @RequestParam(required = false) String biz,
            @RequestParam(required = false) Long createTimeST,
            @RequestParam(required = false) Long createTimeET,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @ApiOperation("查询工单草稿")
    @PostMapping("/util/obj/getDraftById")
    KbEntity<WOCaseDraftModel> getDraftById(
            @RequestParam() String draftId
    );


    // ----------------结案原因配置相关-----------------------------------

    @ApiOperation("获取结案配置列表")
    @PostMapping("/close/obj/getCloseTypeList")
    KbEntity<List<WOCloseTypeModel>> getCloseTypeList(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam(required = false) @ApiParam("身份标识") String identity,
            @RequestParam(required = false) @ApiParam("业务类型") List<String> biz,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @ApiOperation("新增结案原因配置")
    @PostMapping("/close/act/createCloseType")
    KbEntity<WOCloseTypeModel> createCloseType(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() WOCloseTypeModel model
    );

    @ApiOperation("新增结案原因配置")
    @PostMapping("/close/act/updateCloseTypeState")
    KbEntity<Void> updateCloseTypeState(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String code,
            @RequestParam() String state
    );

    @ApiOperation("结案原因根据code查询")
    @PostMapping("/close/obj/getCloseTypeByCode")
    KbEntity<WOCloseTypeModel> getCloseTypeByCode(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() String code
    );

}
