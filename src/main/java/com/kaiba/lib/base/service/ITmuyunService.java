package com.kaiba.lib.base.service;

import com.kaiba.lib.base.annotation.apiparam.KbUserId;
import com.kaiba.lib.base.domain.tmuyun.latch.EventPositionModel;
import com.kaiba.lib.base.domain.tmuyun.tmuyun.ArticleAggrModel;
import com.kaiba.lib.base.domain.tmuyun.tmuyun.ArticleApiModel;
import com.kaiba.lib.base.domain.tmuyun.tmuyun.ArticleGroupModel;
import com.kaiba.lib.base.domain.tmuyun.tmuyun.ArticleRecordModel;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/07/22 17:30
 **/
public interface ITmuyunService {

    Integer SITE_ID_FOR_DEFAULT = -1;

    @ApiOperation("递送临时活动稿件")
    @RequestMapping(path = "/tmuyun/call/sendArticleForActivity", method = RequestMethod.POST)
    KbEntity<Void> sendArticleForActivity(
        @RequestParam @KbUserId Integer userId,
        @RequestParam String id,
        @RequestParam String docTitle,
        @RequestParam String content,
        @RequestParam String pubUrl
    );

    @ApiOperation("递送稿件")
    @RequestMapping(path = "/tmuyun/call/sendArticle", method = RequestMethod.POST)
    KbEntity<Void> sendArticle(
            @RequestBody ArticleApiModel data
    );

    @ApiOperation("获取递送稿件")
    @RequestMapping(path = "/tmuyun/call/getArticle", method = RequestMethod.POST)
    KbEntity<ArticleApiModel> getArticle(
            @RequestParam() String refId,
            @RequestParam() String type,
            @RequestParam(required = false, defaultValue = "true") Boolean allowCache
    );

    @ApiOperation("获取递送稿件")
    @RequestMapping(path = "/tmuyun/call/getArticleByTitle", method = RequestMethod.POST)
    KbEntity<ArticleApiModel> getArticleByTitle(
            @RequestParam() String title,
            @RequestParam(required = false) Integer sendResult
    );

    @ApiOperation("根据groupKey获取稿件")
    @RequestMapping(path = "/tmuyun/call/getArticleByGroupKey", method = RequestMethod.POST)
    KbEntity<ArticleApiModel> getArticleByGroupKey(
            @RequestParam String key
    );

    @ApiOperation("根据id列表获取递送稿件列表")
    @RequestMapping(path = "/tmuyun/call/getArticleListByPubIdIn", method = RequestMethod.POST)
    KbEntity<List<ArticleApiModel>> getArticleListByPubIdIn(
        @RequestParam() List<String> pubIds
    );

    @ApiOperation("获取投稿记录")
    @RequestMapping(path = "/tmuyun/call/getArticleRecord", method = RequestMethod.POST)
    KbEntity<ArticleRecordModel> getArticleRecord(
            @RequestParam() String refId,
            @RequestParam() String type,
            @RequestParam(required = false, defaultValue = "false") Boolean requireArticle,
            @RequestParam(required = false, defaultValue = "true") Boolean allowCache
    );

    @ApiOperation("创建电台点位")
    @RequestMapping(path = "/tmuyun/position/createPosition", method = RequestMethod.POST)
    KbEntity<EventPositionModel> createPosition(
            @RequestParam Integer siteId,
            @RequestParam Integer type,
            @RequestParam Integer state,
            @RequestParam Integer userId,
            @RequestParam String position,
            @RequestParam String description
    );

    @ApiOperation("创建默认点位")
    @RequestMapping(path = "/tmuyun/position/createPositionForDefault", method = RequestMethod.POST)
    KbEntity<EventPositionModel> createPositionForDefault(
        @RequestParam Integer type,
        @RequestParam Integer state,
        @RequestParam Integer userId,
        @RequestParam String position,
        @RequestParam String description
    );

    @ApiOperation("根据id删除点位信息")
    @RequestMapping(path = "/tmuyun/position/deletePositionById", method = RequestMethod.POST)
    KbEntity<Void> deletePositionById(
            @RequestParam String positionId,
            @RequestParam Integer userId
    );

    @ApiOperation("根据点位名称删除点位信息")
    @RequestMapping(path = "/tmuyun/position/deletePositionByPosition", method = RequestMethod.POST)
    KbEntity<Void> deletePositionByPosition(
        @RequestParam String position,
        @RequestParam Integer userId
    );

    @ApiOperation("获取默认点位列表")
    @RequestMapping(path = "/tmuyun/position/getPositionListByDefault", method = RequestMethod.POST)
    KbEntity<List<EventPositionModel>> getPositionListByDefault();

    @ApiOperation("获取电台点位列表")
    @RequestMapping(path = "/tmuyun/position/getPositionList", method = RequestMethod.POST)
    KbEntity<List<EventPositionModel>> getPositionList(
            @RequestParam Integer siteId
    );

    @ApiOperation("获取点位信息")
    @RequestMapping(path = "/tmuyun/position/getPositionById", method = RequestMethod.POST)
    KbEntity<EventPositionModel> getPositionById(
            @RequestParam String positionId
    );

    @ApiOperation("获取电台点位信息")
    @RequestMapping(path = "/tmuyun/position/getPositionBySiteIdAndPosition", method = RequestMethod.POST)
    KbEntity<EventPositionModel> getPositionBySiteIdAndPosition(
            @RequestParam Integer siteId,
            @RequestParam String position,
            @RequestParam(required = false, defaultValue = "true") Boolean considerDefault
    );

    @ApiOperation("获取默认点位信息")
    @RequestMapping(path = "/tmuyun/position/getPositionByDefaultAndPosition", method = RequestMethod.POST)
    KbEntity<EventPositionModel> getPositionByDefaultAndPosition(
            @RequestParam String position
    );

    @ApiOperation("获取点位状态")
    @RequestMapping(path = "/tmuyun/position/getStateBySiteIdAndPosition", method = RequestMethod.POST)
    KbEntity<Integer> getStateBySiteIdAndPosition(
            @RequestParam Integer siteId,
            @RequestParam String position,
            @RequestParam(required = false, defaultValue = "true") Boolean considerDefault
    );

    @ApiOperation("修改点位状态")
    @RequestMapping(path = "/tmuyun/position/updatePositionStateById", method = RequestMethod.POST)
    KbEntity<EventPositionModel> updatePositionStateById(
        @RequestParam String positionId,
        @RequestParam Integer userId,
        @RequestParam Integer state
    );

    @ApiOperation("修改点位状态")
    @RequestMapping(path = "/tmuyun/position/updatePositionStateBySiteIdAndPosition", method = RequestMethod.POST)
    KbEntity<EventPositionModel> updatePositionStateBySiteIdAndPosition(
        @RequestParam Integer siteId,
        @RequestParam String position,
        @RequestParam Integer userId,
        @RequestParam Integer state
    );

    @ApiOperation("修改点位描述")
    @RequestMapping(path = "/tmuyun/position/updatePositionDescriptionById", method = RequestMethod.POST)
    KbEntity<EventPositionModel> updatePositionDescriptionById(
        @RequestParam String positionId,
        @RequestParam String description
    );

    @ApiOperation("添加稿件组")
    @RequestMapping(path = "/manage/tmuyun/article/addArticleGroupKey", method = RequestMethod.POST)
    KbEntity<ArticleGroupModel> addArticleGroupKey(
            @RequestParam String key,
            @RequestParam String pubId,
            @RequestParam String desc
    );

    @ApiOperation("新增通用稿件")
    @RequestMapping(path = "/manage/tmuyun/article/addArticleForCommon", method = RequestMethod.POST)
    KbEntity<ArticleApiModel> addArticleForCommon(
        @RequestParam @KbUserId Integer userId,
        @RequestParam String refId,
        @RequestParam Integer docType,
        @RequestParam Integer refType,
        @RequestParam Integer original,
        @RequestParam String docTitle,
        @RequestParam String content,
        @RequestParam(required = false) String subTitle,
        @RequestParam(required = false) String cover,
        @RequestParam(required = false) String mediaUrl,
        @RequestParam(required = false) String pubUrl,
        @RequestParam(required = false) String[] appendIxs
    );

    @ApiOperation("删除草稿")
    @RequestMapping(path = "/manage/tmuyun/article/deleteArticleDraftByPubId", method = RequestMethod.POST)
    KbEntity<Void> deleteArticleDraftByPubId(
        @RequestParam @KbUserId Integer userId,
        @RequestParam String pubId
    );

    @ApiOperation("编辑稿件组")
    @RequestMapping(path = "/manage/tmuyun/article/editArticleGroupKey", method = RequestMethod.POST)
    KbEntity<ArticleGroupModel> editArticleGroupKey(
            @RequestParam String id,
            @RequestParam String pubId,
            @RequestParam String desc
    );

    @ApiOperation("撤回稿件")
    @RequestMapping(path = "/manage/tmuyun/article/recallArticleByPubId", method = RequestMethod.POST)
    KbEntity<Void> recallArticleByPubId(
        @RequestParam @KbUserId Integer userId,
        @RequestParam String pubId
    );

    @ApiOperation("发送稿件")
    @RequestMapping(path = "/manage/tmuyun/article/sendArticleByPubId", method = RequestMethod.POST)
    KbEntity<Void> sendArticleByPubId(
        @RequestParam @KbUserId Integer userId,
        @RequestParam String pubId
    );

    @ApiOperation("更新稿件")
    @RequestMapping(path = "/manage/tmuyun/article/updateArticleByPubId", method = RequestMethod.POST)
    KbEntity<Void> updateArticleByPubId(
        @RequestParam @KbUserId Integer userId,
        @RequestParam String pubId,
        @RequestParam String docTitle,
        @RequestParam(required = false) Integer docType,
        @RequestParam(required = false) Integer original,
        @RequestParam(required = false) String content,
        @RequestParam(required = false) String subTitle,
        @RequestParam(required = false) String cover,
        @RequestParam(required = false) String mediaUrl,
        @RequestParam(required = false) String pubUrl,
        @RequestParam(required = false) String[] appendIxs
    );

    @ApiOperation("获取稿件组")
    @RequestMapping(path = "/manage/tmuyun/article/getArticleGroupList", method = RequestMethod.POST)
    KbEntity<List<ArticleGroupModel>> getArticleGroupList(
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @ApiOperation("获取稿件最近一次记录")
    @RequestMapping(path = "/manage/tmuyun/article/getLastRecordByPubId", method = RequestMethod.POST)
    KbEntity<ArticleRecordModel> getLastRecordByPubId(
        @RequestParam String pubId
    );

    @ApiOperation("获取稿件投递历史")
    @RequestMapping(path = "/manage/tmuyun/article/getRecordHistoryByPubId", method = RequestMethod.POST)
    KbEntity<List<ArticleRecordModel>> getRecordHistoryByPubId(
        @RequestParam String pubId
    );

    @ApiOperation("获取投递省宣稿件列表")
    @RequestMapping(path = "/manage/tmuyun/article/getRecordList", method = RequestMethod.POST)
    KbEntity<List<ArticleAggrModel>> getRecordList(
        @RequestParam(required = false) Integer[] status,
        @RequestParam(required = false) String title,
        @RequestParam(required = false) Long startTime,
        @RequestParam(required = false) Long endTime,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @RequestMapping(path = "/manage/tmuyun/article/getRecordCount", method = RequestMethod.POST)
    KbEntity<ArticleAggrModel> getRecordCount(
        @RequestParam Long startTime,
        @RequestParam Long endTime
    );

    @RequestMapping(path = "/manage/tmuyun/article/getRecordsByTimeBetween", method = RequestMethod.POST)
    KbEntity<List<ArticleApiModel>> getRecordsByTimeBetween(
        @RequestParam Long startTime,
        @RequestParam Long endTime,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );
}
