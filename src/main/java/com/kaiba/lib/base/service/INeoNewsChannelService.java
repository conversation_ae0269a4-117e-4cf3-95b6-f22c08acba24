package com.kaiba.lib.base.service;

import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.channel.*;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupModel;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * author: lyux
 * date: 2024-08-30
 */
@RequestMapping("/NeoNews/channel")
public interface INeoNewsChannelService {

    @ApiOperation("添加栏目")
    @PostMapping(path = "/usr/createProgramme")
    KbEntity<CProgrammeModel> createProgramme(
            @RequestBody() CProgrammeCreateModel model
    );

    @ApiOperation("修改栏目")
    @PostMapping(path = "/usr/updateProgramme")
    KbEntity<CProgrammeModel> updateProgramme(
            @RequestBody() CProgrammeUpdateModel model
    );

    @ApiOperation("修改栏目状态")
    @PostMapping(path = "/usr/updateProgrammeState")
    KbEntity<CProgrammeModel> updateProgrammeState(
            @RequestParam() String programmeId,
            @RequestParam() String state
    );

    @ApiOperation("修改栏目标识")
    @PostMapping(path = "/usr/updateProgrammeKey")
    KbEntity<CProgrammeModel> updateProgrammeKey(
            @RequestParam() String programmeId,
            @RequestParam() String key
    );

    @ApiOperation("修改栏目顶部布局")
    @PostMapping(path = "/usr/updateProgrammeLayoutId")
    KbEntity<CProgrammeModel> updateProgrammeLayoutId(
            @RequestParam() String programmeId,
            @RequestParam() String layoutId
    );

    @ApiOperation("修改栏目的单期和拆条分组的递补配置")
    @PostMapping(path = "/usr/updateProgrammeGroupAbsentDc")
    KbEntity<CProgrammeModel> updateProgrammeGroupAbsentDc(
            @RequestParam() String programmeId,
            @RequestParam(required = false) @ApiParam("为空表示清除该配置") String viewStyle,
            @RequestParam(required = false) @ApiParam("为空表示清除该配置") String reactStyle,
            @RequestParam(required = false) @ApiParam("为空表示清除该配置") String replyStyle,
            @RequestParam(required = false) @ApiParam("为空表示清除该配置") Boolean shareEnabled
    );

    @ApiOperation("修改栏目分享配置")
    @PostMapping(path = "/usr/updateProgrammeShare")
    KbEntity<CProgrammeModel> updateProgrammeShare(
            @RequestParam() String programmeId,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String content,
            @RequestParam(required = false) String image,
            @RequestParam(required = false) String url
    );

    @ApiOperation("修改栏目关联频率频道")
    @PostMapping(path = "/usr/updateProgrammeChannel")
    KbEntity<CProgrammeModel> updateProgrammeChannel(
            @RequestParam() String programmeId,
            @RequestParam(required = false) String channelKey
    );

    @ApiOperation("修改栏目排序")
    @PostMapping(path = "/usr/updateProgrammeIdx")
    KbEntity<CProgrammeModel> updateProgrammeIdx(
            @RequestParam() String programmeId,
            @RequestParam(required = false) Long idx
    );

    @ApiOperation("批量修改栏目排序")
    @PostMapping(path = "/usr/bulkUpdateProgrammeIdx")
    KbEntity<Void> bulkUpdateProgrammeIdx(
            @RequestBody() CPUpdateOrderModel model
    );

    @ApiOperation("修改栏目外部同步id")
    @PostMapping(path = "/usr/updateOuterId")
    KbEntity<CProgrammeModel> updateOuterId(
        @RequestParam() String programmeId,
        @RequestParam() Integer outerId
    );

    @PostMapping(path = "/obj/getProgrammeById")
    KbEntity<CProgrammeModel> getProgrammeById(
            @RequestParam() String programmeId
    );

    @PostMapping(path = "/obj/getProgrammeByKey")
    KbEntity<CProgrammeModel> getProgrammeByKey(
            @RequestParam() String programmeKey
    );

    @PostMapping(path = "/obj/getProgrammeByGroupKey")
    KbEntity<CProgrammeModel> getProgrammeByGroupKey(
            @RequestParam() String groupKey
    );

    @PostMapping(path = "/obj/getProgrammeShareById")
    KbEntity<ShareModel> getProgrammeShareById(
            @RequestParam() String programmeId
    );

    @PostMapping(path = "/obj/getProgrammeList")
    KbEntity<List<CProgrammeModel>> getProgrammeList(
            @RequestParam(required = false) String channelKey,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    // -----------------------------------------------------

    @ApiOperation("获取单期拆条文章分组, 不存在则创建")
    @PostMapping(path = "/usr/getOrCreateEpisodeClipGroup")
    KbEntity<GroupModel> getOrCreateEpisodeClipGroup(
            @RequestParam() @ApiParam("栏目ID") String programmeId,
            @RequestParam() @ApiParam("单期稿件ID") String articleId
    );

    @ApiOperation("获取单期拆条文章分组")
    @PostMapping(path = "/obj/getEpisodeClipGroup")
    KbEntity<GroupModel> getEpisodeClipGroup(
            @RequestParam() @ApiParam("单期稿件ID") String articleId
    );

    @ApiOperation("获取栏目单期文章分组")
    @PostMapping(path = "/obj/getProgrammeEpisodeGroup")
    KbEntity<GroupModel> getProgrammeEpisodeGroup(
            @RequestParam() @ApiParam("栏目ID") String programmeId
    );

    @PostMapping(path = "/obj/traverseAndFlushGroupConfig")
    KbEntity<GroupModel> traverseAndFlushGroupConfig(
            @RequestParam() String secret,
            @RequestParam() @ApiParam("栏目ID") String programmeId
    );

    // -----------------------------------------------------

    @ApiOperation("批量初始化频道配置")
    @PostMapping(path = "/usr/initAllChannels")
    KbEntity<Void> initAllChannels(
            @RequestParam() String secret
    );

    @ApiOperation("批量初始化频道文章分组")
    @PostMapping(path = "/usr/bulkCreateArticleGroup")
    KbEntity<Void> bulkCreateArticleGroup(
            @RequestParam() String secret,
            @RequestParam() @ApiParam("前缀+频道标识") String groupKeyPrefix,
            @RequestParam() @ApiParam("频道名称+后缀") String groupNameSuffix,
            @RequestParam(required = false) String channelType
    );

    @ApiOperation("添加频道")
    @PostMapping(path = "/usr/createChannel")
    KbEntity<ChannelConfigModel> createChannel(
            @RequestParam() @ApiParam("频道标识") String channelKey,
            @RequestParam(required = false) @ApiParam("频道名称") String channelName,
            @RequestParam(required = false) @ApiParam("频道简称") String channelAbbr
    );

    @ApiOperation("修改频道展示字段")
    @PostMapping(path = "/usr/updateChannelData")
    KbEntity<ChannelConfigModel> updateChannelData(
            @RequestBody() ChannelConfigUpdateModel model
    );

    @ApiOperation("修改频道展示状态")
    @PostMapping(path = "/usr/updateChannelState")
    KbEntity<ChannelConfigModel> updateChannelState(
            @RequestParam() String channelKey,
            @RequestParam() String state
    );

    @ApiOperation("修改频道所属文章的评论设置")
    @PostMapping(path = "/usr/updateChannelArticleReplyStyle")
    KbEntity<ChannelConfigModel> updateChannelArticleReplyStyle(
            @RequestParam() String channelKey,
            @RequestParam(required = false) @ApiParam("传空会置空") String replyStyle
    );

    @ApiOperation("修改频道排序")
    @PostMapping(path = "/usr/updateChannelIdx")
    KbEntity<ChannelConfigModel> updateChannelIdx(
            @RequestParam() String channelKey,
            @RequestParam(required = false) Long idx
    );

    @ApiOperation("批量修改频道排序")
    @PostMapping(path = "/usr/bulkUpdateChannelIdx")
    KbEntity<Void> bulkUpdateChannelIdx(
            @RequestBody() ChannelUpdateOrderModel model
    );

    @ApiOperation("获取频道")
    @PostMapping(path = "/obj/getChannelByKey")
    KbEntity<ChannelConfigModel> getChannelByKey(
            @RequestParam() String channelKey
    );

    @ApiOperation("获取频道列表")
    @PostMapping(path = "/obj/getChannelList")
    KbEntity<List<ChannelConfigModel>> getChannelList(
            @RequestParam(required = false) String channelType,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    );

    @ApiOperation("添加频道首页")
    @PostMapping(path = "/usr/createChannelMainPage")
    KbEntity<ChannelMainPageModel> createChannelMainPage(
            @RequestParam() @ApiParam("频道标识") String channelKey
    );

    @ApiOperation("修改频道首页")
    @PostMapping(path = "/usr/updateChannelMainPage")
    KbEntity<ChannelMainPageModel> updateChannelMainPage(
            @RequestBody() ChannelMainPageUpdateModel model
    );

    @ApiOperation("增加频道首页 tab")
    @PostMapping(path = "/usr/addTabToChannelMainPage")
    KbEntity<Void> addTabToChannelMainPage(
            @RequestParam() @ApiParam("频道标识") String channelKey,
            @RequestParam() String tabName,
            @RequestParam() String tabType,
            @RequestParam(required = false) @ApiParam("传 groupKey 或 threadId") String tabKey,
            @RequestParam(required = false, defaultValue = "true") Boolean init
    );

    @ApiOperation("更新 tab")
    @PostMapping(path = "/usr/updateChannelMainPageTab")
    KbEntity<Void> updateChannelMainPageTab(
            @RequestParam() @ApiParam("频道标识") String channelKey,
            @RequestParam() String tabType,
            @RequestParam() @ApiParam("传 groupKey 或 threadId") String tabKey,
            @RequestParam(required = false) @ApiParam("可更新字段: 标题") String name,
            @RequestParam(required = false) @ApiParam("可更新字段: 可见性状态") String state,
            @RequestParam(required = false) @ApiParam("可更新字段: 是否默认选中") Boolean selected
    );

    @ApiOperation("移除 tab")
    @PostMapping(path = "/usr/removeChannelMainPageTab")
    KbEntity<Void> removeChannelMainPageTab(
            @RequestParam() @ApiParam("频道标识") String channelKey,
            @RequestParam() String tabType,
            @RequestParam() @ApiParam("传 groupKey 或 threadId") String tabKey
    );

    @ApiOperation("获取频道首页")
    @PostMapping(path = "/obj/getChannelMainPageByChannel")
    KbEntity<ChannelMainPageModel> getChannelMainPageByChannel(
            @RequestParam() String channelKey
    );

    @PostMapping(path = "/obj/getChannelMainPageShareByChannel")
    KbEntity<ShareModel> getChannelMainPageShareByChannel(
            @RequestParam() String channelKey
    );

    // -----------------------------------------------------

}
