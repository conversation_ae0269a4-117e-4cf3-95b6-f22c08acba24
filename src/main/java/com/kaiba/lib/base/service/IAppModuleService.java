package com.kaiba.lib.base.service;

import com.kaiba.lib.base.domain.appmodule.DiscoveryHomeModel;
import com.kaiba.lib.base.domain.appmodule.DiscoveryMyModel;
import com.kaiba.lib.base.response.KbEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/21
 */
@RequestMapping("/appModule")
public interface IAppModuleService {
    @PostMapping(path = "/discoveryForPersonal")
    KbEntity<List<DiscoveryMyModel>> discoveryForPersonal(
            @RequestParam("siteId") Integer siteId
    );

    @PostMapping(path = "/discoveryForHome")
    KbEntity<List<DiscoveryHomeModel>> discoveryForHome(
            @RequestParam("siteId") Integer siteId
    );
}
