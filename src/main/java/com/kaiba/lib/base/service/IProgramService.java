package com.kaiba.lib.base.service;

import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.lib.base.domain.program.ProgramModel;
import com.kaiba.lib.base.domain.program.ReferenceThreadModel;
import com.kaiba.lib.base.response.KbEntity;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * author wangsj
 * date 2020-09-01
 */
public interface IProgramService {

    @ApiOperation("获取节目互动总板块信息")
    @RequestMapping(path = "/program/getMainProgramThread", method = RequestMethod.POST)
    KbEntity<NoteThreadModel> getMainProgramThread(
            @RequestParam(name = "siteId") Integer siteId
    );

    @ApiOperation("获取节目互动收藏板块块信息")
    @RequestMapping(path = "/program/getMainCollectionThread", method = RequestMethod.POST)
    KbEntity<NoteThreadModel> getMainCollectionThread(
            @RequestParam(name = "siteId") Integer siteId
    );

    @ApiOperation("获取节目互动路况板块块信息")
    @RequestMapping(path = "/program/getMainRoadThread", method = RequestMethod.POST)
    KbEntity<NoteThreadModel> getMainRoadThread(
            @RequestParam(name = "siteId") Integer siteId
    );

    @ApiOperation("节目详情")
    @RequestMapping(path = "/program/getDetailById", method = RequestMethod.POST)
    KbEntity<ProgramModel> getDetailById(
            @RequestParam(name = "id") String id
    );

    @ApiOperation("创建节目")
    @RequestMapping(path = "/program/create", method = RequestMethod.POST)
    KbEntity<Void> createProgram(
            @RequestParam(name = "name") String name,
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "introduce", required = false) String introduce,
            @RequestParam(name = "coverImg", required = false) String coverImg,
            @RequestParam(name = "reserved") Integer reserved,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "albumId", required = false) String albumId,
            @RequestParam(name = "threadLevel") Integer threadLevel
    );

    @ApiOperation("编辑节目")
    @RequestMapping(path = "/program/edit", method = RequestMethod.POST)
    KbEntity<Void> editProgram(
            @RequestParam(name = "id") String id,
            @RequestParam(name = "name") String name,
            @RequestParam(name = "introduce", required = false) String introduce,
            @RequestParam(name = "coverImg", required = false) String coverImg,
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "albumId", required = false) String albumId,
            @RequestParam(name = "threadLevel") Integer threadLevel
    );

    @ApiOperation("设置节目专辑")
    @RequestMapping(path = "/program/editAlbumId", method = RequestMethod.POST)
    KbEntity<Void> editProgramAlbumId(
            @RequestParam(name = "id") String id,
            @RequestParam(name = "albumId", required = false) String albumId
    );

    @ApiOperation("下线节目")
    @RequestMapping(path = "/program/offline", method = RequestMethod.POST)
    KbEntity<Void> offlineProgram(
            @RequestParam(name = "id") String id,
            @RequestParam(name = "userId") Integer userId
    );

    @ApiOperation("上线节目")
    @RequestMapping(path = "/program/online", method = RequestMethod.POST)
    KbEntity<Void> onlineProgram(
            @RequestParam(name = "id") String id,
            @RequestParam(name = "userId") Integer userId
    );

    @ApiOperation("获取电台现有节目")
    @RequestMapping(path = "/program/getOnlineProgramBySiteId", method = RequestMethod.POST)
    KbEntity<List<ProgramModel>> getOnlineProgramBySiteId(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "page") Integer page,
            @RequestParam(name = "pageSize") Integer pageSize
    );

    @ApiOperation("获取电台下线节目")
    @RequestMapping(path = "/program/getOfflineProgramBySiteId", method = RequestMethod.POST)
    KbEntity<List<ProgramModel>> getOfflineProgramBySiteId(
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "page") Integer page,
            @RequestParam(name = "pageSize") Integer pageSize
    );

    @ApiOperation("创建节目板块(创建话题)")
    @RequestMapping(path = "/program/createThreadForTopic", method = RequestMethod.POST)
    KbEntity<NoteThreadModel> createThreadForTopic(
            @RequestParam(name = "userId") Integer userId,
            @RequestParam(name = "siteId") Integer siteId,
            @RequestParam(name = "title") String title,
            @RequestParam(name = "description", required = false) String description,
            @RequestParam(name = "hotMax", required = false, defaultValue = "-1") Integer hotMax,
            @RequestParam(name = "topMax", required = false, defaultValue = "0") Integer topMax,
            @RequestParam(name = "condition", required = false) Integer condition,
            @RequestParam(name = "programThreadId") String programThreadId
    );

    @ApiOperation("管理后台帖子引用功能使用: 用以列出可供选择的板块列表.")
    @RequestMapping(path = "/program/getReferenceThreadListBySite", method = RequestMethod.POST)
    KbEntity<List<ReferenceThreadModel>> getReferenceThreadListBySite(
            @RequestParam(name = "siteId") Integer siteId
    );

    @ApiOperation("获取节目互动视频直播滚动帖子")
    @RequestMapping(path = "/program/getNoteBarrage", method = RequestMethod.POST)
    KbEntity<List<NoteModel>> getNoteBarrage(
            @RequestParam(name = "siteId", required = false) Integer siteId,
            @RequestParam(name = "userId", required = false) Integer userId,
            @RequestParam(name = "threadId") String threadId,
            @RequestParam(name = "lastNoteId", required = false) String lastNoteId
    );

}
