package com.kaiba.lib.base.util.appmodule.item;

import com.kaiba.lib.base.util.appmodule.constant.ItemType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * author: lyux
 * date: 19-3-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MainPageTabVideoLive extends AbsMainPageTab {

    public MainPageTabVideoLive() {
        super(ItemType.MAIN_PAGE_TAB_VIDEO_LIVE);
    }

    public static Builder on() {
        return new Builder();
    }

    public static class Builder extends AbsMainPageTab.Builder {

        public Builder() {
            super(new MainPageTabVideoLive());
        }

        @Override
        public Builder when(boolean condition) {
            whenFlag = condition;
            return this;
        }
    }

}
