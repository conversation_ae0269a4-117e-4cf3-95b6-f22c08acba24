package com.kaiba.lib.base.util.appmodule.item;

import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.util.appmodule.constant.ItemType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 19-3-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MainPageTabTripleBlock extends AbsMainPageTab {

    private String flag;

    private List<Image> blocks;

    public MainPageTabTripleBlock() {
        super(ItemType.MAIN_PAGE_TAB_TRIPLE_BLOCK);
    }

    public static Builder on() {
        return new Builder();
    }

    public static class Builder extends AbsMainPageTab.Builder {

        private MainPageTabTripleBlock module;

        public Builder() {
            super(new MainPageTabTripleBlock());
            module = (MainPageTabTripleBlock) getItem();
        }

        public Builder setFlag(String flag) {
            if (!checkWhen()) {
                return this;
            }
            module.setFlag(flag);
            return this;
        }

        public Builder setBlock(List<Image> images) {
            if (!checkWhen()) {
                return this;
            }
            module.setBlocks(images);
            return this;
        }

        public Builder addBlock(Image image) {
            if (!checkWhen()) {
                return this;
            }
            if (module.getBlocks() == null) {
                module.setBlocks(new LinkedList<>());
            }
            module.getBlocks().add(image);
            return this;
        }

        public Builder addBlock(String url, String action, Map<String, Object> actionParams) {
            Image image = new Image();
            image.setUrl(url);
            image.setAction(action);
            image.setActionParams(actionParams);
            return addBlock(image);
        }

        @Override
        public Builder when(boolean condition) {
            whenFlag = condition;
            return this;
        }
    }

}
