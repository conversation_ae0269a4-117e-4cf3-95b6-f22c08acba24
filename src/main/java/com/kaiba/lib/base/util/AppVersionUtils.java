package com.kaiba.lib.base.util;

import java.util.concurrent.ConcurrentHashMap;

/**
 * author: lyux
 * date: 19-3-13
 */
public class AppVersionUtils {

    private static final ConcurrentHashMap<String, AppVersion> versions = new ConcurrentHashMap<>();

    private AppVersionUtils() {}

    public static AppVersion getVersion(String version) {
        AppVersion v = versions.get(version);
        if (null == v) {
            v = new AppVersion(version);
            versions.put(version, v);
        }
        return v;
    }

    public static boolean gte(String version1, String version2) {
        return getVersion(version1).compareTo(getVersion(version2)) >= 0;
    }

    public static boolean gt(String version1, String version2) {
        return getVersion(version1).compareTo(getVersion(version2)) > 0;
    }

    public static boolean lte(String version1, String version2) {
        return getVersion(version1).compareTo(getVersion(version2)) <= 0;
    }

    public static boolean lt(String version1, String version2) {
        return getVersion(version1).compareTo(getVersion(version2)) < 0;
    }

    public static boolean eq(String version1, String version2) {
        return getVersion(version1).compareTo(getVersion(version2)) == 0;
    }

    public static String getVersionNameByCode(int versionCode) {
        return new AppVersion(versionCode).getVersionName();
    }

    public static int getVersionCodeByName(String versionName) {
        return new AppVersion(versionName).getVersionCode();
    }

}
