package com.kaiba.lib.base.util;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

/**
 * author: lyux
 * date: 2022-12-07
 */
public class DigitHashGenerator {

    private final int detSize; // 行列式尺寸
    private final int[] mapping; // 数位分别与此数组元素相加取余
    private final int[] shuffle; // 数位次序打乱规则
    private final int digit; // 数字位数
    private final int max; // 最大值
    private final int orphan; // 因为 mapping 机制而产生的需特殊处理的数字

    public DigitHashGenerator(int digit, int mappingNumber) {
        this(digit, null, disintegrateNumber(mappingNumber, digit));
    }

    public DigitHashGenerator(int digit, int shuffleNumber, int mappingNumber) {
        this(digit, disintegrateNumber(shuffleNumber, digit), disintegrateNumber(mappingNumber, digit));
    }

    /**
     * @param digit 期望映射的数字的最大位数, 仅支持偶数位
     * @param shuffle 数位次序打乱规则, 数组元素个数必须和 digit 相同, 元素取值范围均为 [1,9], 数组各元素不可重复.
     * @param mapping 数位相加取余参数, 数组元素个数必须和 digit 相同, 元素取值范围均为 [1,9], 数组各元素建议不要重复.
     */
    public DigitHashGenerator(int digit, int[] shuffle, int[] mapping) {
        if (digit < 4) {
            throw new IllegalArgumentException("digit should be last least 4");
        }
        if (digit % 2 != 0) {
            throw new IllegalArgumentException("digit should be a even number, or else mapping won't work");
        }
        if (shuffle == null) {
            shuffle = new int[digit];
            for (int i = 0; i < digit; i++) {
                shuffle[i] = i + 1;
            }
        } else {
            if (shuffle.length != digit) {
                throw new IllegalArgumentException("shuffle array should have " + digit + " element");
            }
            Set<Integer> set = new HashSet<>(digit);
            for (int s : shuffle) {
                if (s < 1 || s > digit) {
                    throw new IllegalArgumentException("shuffle array element should be [1," + digit + "]: " + s);
                }
                set.add(s);
            }
            if (set.size() != digit) {
                throw new IllegalArgumentException("shuffle array element should be unique");
            }
        }
        if (mapping.length != digit) {
            throw new IllegalArgumentException("mapping array should have " + digit + " element");
        }
        this.shuffle = shuffle;
        this.mapping = mapping;
        this.digit = digit;
        this.max = (int) Math.pow(10, digit) - 1;
        int[] orphanDigits = new int[digit];
        for (int i = 0; i < digit; i ++) {
            int p = shuffle[i];
            orphanDigits[p - 1] = mapping[i];
        }
        this.orphan = assembleNumber(orphanDigits);
        // 确定行列式参数
        this.detSize = (int) Math.pow(10, (float) (digit / 2));
    }

    /**
     * 通过数字 A 得到映射后的数字 B.
     */
    public int generate(int n) {
        if (n < 1 || n > max) {
            throw new IllegalArgumentException("generate, input number should be [1," + max + "]: " + n);
        }
        int detMapped = detMapping(n);
        int[] numbers = disintegrateNumber(detMapped, digit);
        int[] shuffled = reshuffle(numbers, shuffle, mapping);
        int result = assembleNumber(shuffled);
        if (result == 0) {
            return orphan;
        } else {
            return result;
        }
    }

    /**
     * 通过映射后的数字 B 得到数字 A.
     */
    public int reverse(int n) {
        if (n < 1 || n > max) {
            throw new IllegalArgumentException("reverse, input number should be [1," + max + "]: " + n);
        }
        if (n == orphan) {
            n = 0;
        }
        int[] numbers = disintegrateNumber(n, digit);
        int[] shuffled = reverseReshuffle(numbers, shuffle, mapping);
        int number = assembleNumber(shuffled);
        return reverseDetMapping(number);
    }

    // ---------------------------------------------------------------

    private static int[] disintegrateNumber(int n, int count) {
        int[] numbers = new int[count];
        numbers[count - 1] = n % 10;
        int x = 10;
        for (int i = 1; i < count; i ++) {
            if (n < x) {
                break;
            }
            numbers[count - i - 1] = n / x % 10;
            x = x * 10;
        }
        return numbers;
    }

    private static int assembleNumber(int[] numbers) {
        int result = 0;
        int x = 1;
        for (int i = numbers.length - 1; i >= 0; i --) {
            result += numbers[i] * x;
            x = x * 10;
        }
        return result;
    }

    // ---------------------------------------------------------------

    private int detMapping(int n) {
        for (int i = 1; i <= detSize * 2 - 1; i ++) {
            int sequence = getDetSequence(detSize, i);
            int sequencePrevious = i == 1 ? 0 : getDetSequence(detSize, i - 1);
            if (n <= sequence) {
                int rowStart, columnStart;
                if (i < detSize) {
                    rowStart = detSize - i;
                    columnStart = detSize - 1;
                } else if (i == detSize) {
                    rowStart = 0;
                    columnStart = detSize - 1;
                } else {
                    rowStart = 0;
                    columnStart = detSize - (i - detSize) - 1;
                }
                int offset = n - (sequencePrevious + 1);
                int row = rowStart + offset;
                int column = columnStart - offset;
                return row * detSize + column;
            }
        }
        throw new IllegalArgumentException("mapping, not supposed to reach here: " + n);
    }

    private int reverseDetMapping(int n) {
        int row = n / detSize;
        int column = n % detSize;
        int rowOffset = detSize - row - 1;
        int columnOffset = detSize - column - 1;
        int offset = rowOffset + columnOffset;
        if (offset == 0) {
            return getDetSequence(detSize, 1);
        } else if (offset < detSize) {
            int sequence = getDetSequence(detSize, offset);
            return sequence + columnOffset + 1;
        } else if (offset == detSize) {
            int sequence = getDetSequence(detSize, offset);
            return sequence + columnOffset;
        } else {
            int sequence = getDetSequence(detSize, offset);
            return sequence + (detSize - rowOffset);
        }
    }

    // ---------------------------------------------------------------

    private static int[] reshuffle(int[] number, int[] shuffle, int[] mapping) {
        int count = number.length;
        int[] result = new int[count];
        for (int i = 0; i < count; i ++) {
            int p = shuffle[i];
            int d = number[i];
            int m = mapping[i];
            result[p - 1] = (d + m) % 10;
        }
        return result;
    }

    private static int[] reverseReshuffle(int[] numbers, int[] shuffle, int[] mapping) {
        int count = numbers.length;
        int[] result = new int[count];
        for (int i = 0; i < count; i ++) {
            int p = shuffle[i];
            int d = numbers[p - 1];
            int m = mapping[i];
            result[i] = (d + 10 - (m % 10)) % 10;
        }
        return result;
    }

    private static int getDetSequence(int detSize, int n) {
        if (n > detSize) {
            int bn = detSize * (detSize + 1) / 2;
            int a1 = detSize - 1;
            int nn = n - detSize;
            int sn = nn * a1 - nn * (nn - 1) / 2;
            return bn + sn;
        } else {
            return n * (n + 1) / 2;
        }
    }


    // ---------------------------------------------------------------
    // 测试和验证

    public static void main(String[] args) {
        test(4, 3472);
        test(6, 294857);
    }

    private static void test(int digit, int mapping) {
        DigitHashGenerator generator = new DigitHashGenerator(digit, mapping);
        HashMap<Integer, Integer> map = new HashMap<>(generator.max);
        for (int i = 1; i <= generator.max; i ++) {
            int result = generator.generate(i);
            if (result < 1 || result > generator.max) {
                throw new IllegalArgumentException("number out of range: " + i + " -> " + result);
            }
            Integer repeated = map.put(result, i);
            if (repeated != null) {
                throw new IllegalArgumentException("number repeat: " + i + " -> " + result + ", by " + repeated);
            }
            int reversed = generator.reverse(result);
            System.out.println("orphan=" + generator.orphan + ", i=" + i + ", result=" + result + ", reversed=" + reversed + ", repeated=" + repeated);
            if (reversed != i) {
                throw new IllegalArgumentException("number reversed fail: " + i + " -> " + result + " -> " + reversed);
            }
        }
    }

}
