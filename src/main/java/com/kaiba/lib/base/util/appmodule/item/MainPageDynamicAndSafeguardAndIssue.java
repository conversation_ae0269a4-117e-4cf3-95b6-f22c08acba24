package com.kaiba.lib.base.util.appmodule.item;

import com.kaiba.lib.base.util.appmodule.constant.ItemType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * author: lyux
 * date: 19-3-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MainPageDynamicAndSafeguardAndIssue extends AbsItem {

    private Long flag;

    public MainPageDynamicAndSafeguardAndIssue() {
        super(ItemType.MAIN_PAGE_DYNAMIC_AND_SAFEGUARD_AND_ISSUE);
    }

    public static Builder on() {
        return new Builder();
    }

    public static class Builder extends AbsItem.Builder {

        private MainPageDynamicAndSafeguardAndIssue module;

        public Builder() {
            super(new MainPageDynamicAndSafeguardAndIssue());
            module = (MainPageDynamicAndSafeguardAndIssue) getItem();
        }

        public Builder setFlag(long flag) {
            if (!checkWhen()) return this;
            module.flag = flag;
            return this;
        }

        @Override
        public Builder when(boolean condition) {
            whenFlag = condition;
            return this;
        }
    }

}
