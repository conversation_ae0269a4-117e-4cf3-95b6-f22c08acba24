package com.kaiba.lib.base.util.appmodule.constant;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/5/24
 */
public enum LayoutIdentifier {

    HOME("MainPageTabHome", "首页"),
    CIRCLE("MainPageTabCircle", "圈子"),
    SERVICE("MainPageTabService", "服务"),
    ME("MainPageTabMe", "我的"),;

    private String name;
    private String description;

    LayoutIdentifier(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public String getName() {
        return name;
    }

    public static Optional<LayoutIdentifier> of(String name) {
        if (null == name) return Optional.empty();
        for (LayoutIdentifier v : values()) {
            if (v.getName().equals(name)) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }
}
