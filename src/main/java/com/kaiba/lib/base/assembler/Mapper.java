package com.kaiba.lib.base.assembler;

import org.springframework.lang.NonNull;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 18-7-27
 */
public class Mapper {

    /***
     * 对一个对象进行深拷贝:
     * 先根据目标类构造目标对象, 再将源对象中同名称同类型的字段赋值给目标对象.
     *
     * @param source 源对象
     * @param targetClass 目标类
     * @return 新构造的深拷贝对象
     */
    @NonNull
    public static <T> T map(Object source, Class<T> targetClass) {
        return map(source, targetClass, true);
    }

    /***
     * 对一个对象进行深拷贝:
     * 先根据目标类构造目标对象, 再将源对象中同名称同类型的字段赋值给目标对象.
     *
     * @param source 源对象
     * @param targetClass 目标类
     * @param ignoreNullField 是否忽略源数据中 value 为 null 的字段.
     * @return 新构造的深拷贝对象
     */
    @NonNull
    public static <T> T map(Object source, Class<T> targetClass, boolean ignoreNullField) {
        Object target;
        try {
            target = targetClass.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new MappingException("Mapper cannot create instance, check bean constructor");
        }
        return map(source, target, ignoreNullField);
    }

    /***
     * 将源对象中非空且同类型同名称的字段赋值给目标对象.
     * 注意: 在此过程中, 作为入参的目标对象的数据会发生变更.
     *
     * @param source 源对象
     * @param target 目标对象
     * @return 目标对象本身
     */
    @NonNull
    @SuppressWarnings("unchecked")
    public static <T> T map(Object source, Object target) {
        return map(source, target, true);
    }

    /***
     * 将源对象中非空且同类型同名称的字段赋值给目标对象.
     * 注意: 在此过程中, 作为入参的目标对象的数据会发生变更.
     *
     * @param source 源对象
     * @param target 目标对象
     * @param ignoreNullField 是否忽略源数据中 value 为 null 的字段.
     * @return 目标对象本身
     */
    @NonNull
    @SuppressWarnings("unchecked")
    public static <T> T map(Object source, Object target, boolean ignoreNullField) {
        if (null == target) {
            throw new MappingException("Mapper target should not be null");
        }
        if (source instanceof Map<?, ?>) {
            ReflectionUtils.mappingMap((Map<String, Object>) source, target, ignoreNullField);
        } else {
            ReflectionUtils.mapping(source, target, ignoreNullField);
        }
        return (T) target;
    }

    /***
     * 逐一将源对象字段拷贝至由目标类构造的目标对象.
     * 先根据目标类构造目标对象, 再将源对象中同名称同类型的字段赋值给目标对象.
     *
     * @param sources 源对象列表
     * @param targetClass 目标类
     * @return 新构造的深拷贝对象
     */
    @NonNull
    public static <T> T mapTo(List<Object> sources, Class<T> targetClass) {
        Object target;
        try {
            target = targetClass.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new MappingException("Mapper cannot create instance, check bean constructor");
        }
        return mapTo(sources, target);
    }

    /***
     * 逐一将列表中的源对象中非空且同类型同名称的字段赋值给目标对象.
     * 注意: 在此过程中, 作为入参的目标对象的数据会发生变更.
     *
     * @param sources 源对象列表
     * @param target 目标对象
     * @return 目标对象本身
     */
    @NonNull
    @SuppressWarnings("unchecked")
    public static <T> T mapTo(List<Object> sources, Object target) {
        if (sources != null && sources.size() != 0) {
            for (Object source : sources) {
                map(source, target);
            }
        }
        return (T) target;
    }

    /***
     * 将一个 Map 对象按"深拷贝"概念赋值给目标类构造的目标对象:
     * 先根据目标类构造目标对象, 再将源 Map 中 key 相同且类型相同的 value 赋值给目标对象对应的字段.
     *
     * @param sourceMap 源 Map
     * @param targetClass 目标类
     * @return 新构造的深拷贝对象
     */
    public static <T> T fromMap(Map<String, Object> sourceMap, Class<T> targetClass) {
        Object target;
        try {
            target = targetClass.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new MappingException("Mapper cannot create instance, check bean constructor");
        }
        return fromMap(sourceMap, target);
    }

    /***
     * 将一个 Map 对象按"深拷贝"概念赋值给目标对象:
     * 将源 Map 中 key 相同且类型相同的 value 赋值给目标对象对应的字段.
     * 注意: 在此过程中, 作为入参的目标对象的数据会发生变更.
     *
     * @param sourceMap 源 Map
     * @param target 目标对象
     * @return 目标对象本身
     */
    @NonNull
    @SuppressWarnings("unchecked")
    public static <T> T fromMap(Map<String, Object> sourceMap, Object target) {
        return fromMap(sourceMap, target, true);
    }

    /***
     * 将一个 Map 对象按"深拷贝"概念赋值给目标对象:
     * 将源 Map 中 key 相同且类型相同的 value 赋值给目标对象对应的字段.
     * 注意: 在此过程中, 作为入参的目标对象的数据会发生变更.
     *
     * @param sourceMap 源 Map
     * @param target 目标对象
     * @param ignoreNullField 是否忽略源数据中 value 为 null 的字段.
     * @return 目标对象本身
     */
    @NonNull
    @SuppressWarnings("unchecked")
    public static <T> T fromMap(Map<String, Object> sourceMap, Object target, boolean ignoreNullField) {
        if (null == target) {
            throw new MappingException("Mapper target should not be null");
        }
        ReflectionUtils.mappingMap(sourceMap, target, ignoreNullField);
        return (T) target;
    }

    /***
     * 将一个 List 按照 {@link #map(Object, Object)} 的规则, 转化为另一个 List.
     *
     * @param sourceList 源列表
     * @param targetClass 目标类
     */
    @NonNull
    public static <T> List<T> mapList(List<?> sourceList, Class<T> targetClass) {
        return mapList(sourceList, targetClass, true);
    }

    /***
     * 将一个 List 按照 {@link #map(Object, Object)} 的规则, 转化为另一个 List.
     *
     * @param sourceList 源列表
     * @param targetClass 目标类
     * @param ignoreNullField 是否忽略源数据中 value 为 null 的字段.
     */
    @NonNull
    public static <T> List<T> mapList(List<?> sourceList, Class<T> targetClass, boolean ignoreNullField) {
        return sourceList.stream().map(s -> {
            T target;
            try {
                target = targetClass.newInstance();
            } catch (InstantiationException | IllegalAccessException e) {
                throw new MappingException("Mapper cannot create instance, check bean constructor");
            }
            ReflectionUtils.mapping(s, target, ignoreNullField);
            return target;
        }).collect(Collectors.toList());
    }

    /***
     * 将一个对象转化为 Map
     * @param source 源对象
     */
    @NonNull
    public static Map<String, Object> asMap(Object source) {
        Map<String, Object> sourceValueMap = ReflectionUtils.fillMapWithBean(null, null, source);
        if (null == sourceValueMap) {
            return Collections.emptyMap();
        } else {
            return sourceValueMap;
        }
    }

    /***
     * 将一个对象转化为 Map:
     * 对于指定的字段名, 对象的字段名会作为该 Map 的 key 值, 字段值会作为该 Map 的 value.
     *
     * @param source 源对象
     * @param keys 指定的字段名
     */
    @NonNull
    public static Map<String, Object> asMap(Object source, String... keys) {
        return asMap(source, NameMapper.DEFAULT, keys);
    }

    /***
     * 将一个对象转化为 Map:
     * 对于指定的字段名, 对象的字段名会作为该 Map 的 key 值, 字段值会作为该 Map 的 value.
     *
     * @param source 源对象
     * @param fieldNameMapper 对象字段名映射策略
     * @param keys 指定的字段名
     */
    @NonNull
    public static Map<String, Object> asMap(Object source, FieldNameMapper fieldNameMapper, String... keys) {
        if (null == keys || keys.length == 0) {
            return Collections.emptyMap();
        } else {
            return asMapByKeys(source, fieldNameMapper, Arrays.asList(keys));
        }
    }

    /***
     * 将一个对象转化为 Map:
     * 对于指定的字段名, 对象的字段名会作为该 Map 的 key 值, 字段值会作为该 Map 的 value.
     *
     * @param source 源对象
     * @param keys 指定的字段名
     */
    @NonNull
    public static Map<String, Object> asMapByKeys(Object source, Collection<String> keys) {
        return asMapByKeys(source, NameMapper.DEFAULT, keys);
    }

    /***
     * 将一个对象转化为 Map:
     * 对于指定的字段名, 对象的字段名会作为该 Map 的 key 值, 字段值会作为该 Map 的 value.
     *
     * @param source 源对象
     * @param fieldNameMapper 对象字段名映射策略
     * @param keys 指定的字段名
     */
    @NonNull
    public static Map<String, Object> asMapByKeys(Object source, FieldNameMapper fieldNameMapper, Collection<String> keys) {
        if (null == keys || keys.size() == 0) {
            return Collections.emptyMap();
        } else {
            Map<String, Object> sourceValueMap = ReflectionUtils.fillMapWithBean(null, null, source);
            if (null == sourceValueMap) {
                return Collections.emptyMap();
            }

            Map<String, Object> result = new HashMap<>(keys.size());
            for (String key : keys) {
                Object sourceValue = sourceValueMap.get(key);
                String targetKey = fieldNameMapper.getKey(sourceValue, key);
                result.put(targetKey, sourceValue);
            }
            return result;
        }
    }

}
