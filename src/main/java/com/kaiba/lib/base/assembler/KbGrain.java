package com.kaiba.lib.base.assembler;

import com.kaiba.lib.base.constant.Values;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * author: lyux
 * date: 18-7-27
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface KbGrain {

    int UNSPECIFIED = Values.GRAIN_UNSPECIFIED;
    int BASIC = Values.GRAIN_BASIC;
    int SUMMARY = Values.GRAIN_SUMMARY;
    int DETAIL = Values.GRAIN_DETAIL;
    int ALL = Values.GRAIN_ALL;

    @AliasFor("grain")
    int value() default UNSPECIFIED;

    @AliasFor("value")
    int grain() default UNSPECIFIED;

    boolean subEnabled() default false;

    int subGrain() default UNSPECIFIED;

}
