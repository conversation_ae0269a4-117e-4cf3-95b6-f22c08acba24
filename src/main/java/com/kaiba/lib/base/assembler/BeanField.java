package com.kaiba.lib.base.assembler;

import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

/**
 * author: lyux
 * date: 18-7-27
 */
class BeanField {

    private static final boolean DEBUG = false;

    private static final int INIT_GETTER = 0x01 << 1;
    private static final int INIT_SETTER = 0x01 << 2;
    private static final int INIT_KB_MAPPING = 0x01 << 3;
    private static final int INIT_KB_GRAIN = 0x01 << 4;
    private static final int INIT_IS_READABLE = 0x01 << 5;
    private static final int INIT_IS_WRITABLE = 0x01 << 6;
    private static final int INIT_IS_PUBLIC = 0x01 << 7;

    private int initFlag = 0x00;

    private final Class<?> clazz;
    private final Field field;
    private Method setter;
    private Method getter;
    private String mappingFieldName;

    private KbMapping kbMapping;
    private KbGrain kbGrain;

    private boolean isPublic;
    private boolean isReadable;
    private boolean isWritable;

    BeanField(Class<?> beanClass, Field field) {
        this.clazz = beanClass;
        this.field = field;
    }

    String name() {
        return field.getName();
    }

    boolean isReadable() {
        if ((initFlag & INIT_IS_READABLE) == 0) {
            isReadable = isPublic() || (getGetter() != null);
            initFlag = initFlag | INIT_IS_READABLE;
        }
        return isReadable;
    }

    boolean isWritable() {
        if ((initFlag & INIT_IS_WRITABLE) == 0) {
            isWritable = isPublic() || (getSetter() != null);
            initFlag = initFlag | INIT_IS_WRITABLE;
        }
        return isWritable;
    }

    boolean isPublic() {
        if ((initFlag & INIT_IS_PUBLIC) == 0) {
            isPublic = Modifier.isPublic(field.getModifiers());
            initFlag = initFlag | INIT_IS_PUBLIC;
        }
        return isPublic;
    }

    Method getGetter() {
        if ((initFlag & INIT_GETTER) == 0) {
            try {
                Method getter = getBeanGetter(clazz, field);
                if (Modifier.isPublic(getter.getModifiers())) {
                    // no point if the getter is not accessible
                    this.getter = getter;
                }
            } catch (NoSuchMethodException nme) {
                LoggerFactory.getLogger(BeanField.class).info("get getter, no such method for " + field);
            } catch (Exception e) {
                if (DEBUG) {
                    LoggerFactory.getLogger(BeanField.class).debug("get getter failed", e);
                }
            }
            initFlag = initFlag | INIT_GETTER;
        }
        return getter;
    }

    Method getSetter() {
        if ((initFlag & INIT_SETTER) == 0) {
            try {
                Method setter = getBeanSetter(clazz, field);
                if (Modifier.isPublic(setter.getModifiers())) {
                    // no point if the setter is not accessible
                    this.setter = setter;
                }
            } catch (NoSuchMethodException nme) {
                LoggerFactory.getLogger(BeanField.class).info("get setter, no such method for " + field);
            } catch (Exception e) {
                if (DEBUG) {
                    LoggerFactory.getLogger(BeanField.class).debug("get setter failed", e);
                }
            }
            initFlag = initFlag | INIT_SETTER;
        }
        return setter;
    }

    KbMapping getKbMapping() {
        if ((initFlag & INIT_KB_MAPPING) == 0) {
            if (null == kbMapping) {
                kbMapping = field.getAnnotation(KbMapping.class);
            }
            if (null == kbMapping || "".equals(kbMapping.value())) {
                mappingFieldName = field.getName();
            } else {
                mappingFieldName = kbMapping.value();
            }
            initFlag = initFlag | INIT_KB_MAPPING;
        }
        return kbMapping;
    }

    KbGrain getKbGrain() {
        if ((initFlag & INIT_KB_GRAIN) == 0) {
            if (null == kbGrain) {
                kbGrain = field.getAnnotation(KbGrain.class);
            }
            initFlag = initFlag | INIT_KB_GRAIN;
        }
        return kbGrain;
    }

    String getMappingName() {
        if (null == mappingFieldName) {
            getKbMapping();
        }
        return mappingFieldName;
    }

    Object get(Object bean) {
        if (isReadable()) {
            try {
                return isPublic ? field.get(bean) : getter.invoke(bean);
            } catch (Exception e) {
                if (DEBUG) {
                    LoggerFactory.getLogger(BeanField.class).debug("get failed", e);
                }
                return null;
            }
        } else {
            return null;
        }
    }

    void set(Object bean, Object fieldValue) {
        if (isWritable()) {
            try {
                if (isPublic) {
                    field.set(bean, fieldValue);
                } else {
                    setter.invoke(bean, fieldValue);
                }
            } catch (Exception e) {
                if (DEBUG) {
                    LoggerFactory.getLogger(BeanField.class).debug("set failed", e);
                }
            }
        }
    }

    private static Method getBeanGetter(Class<?> beanClass, Field field) throws Exception {
        String fieldName = field.getName();
        String camelMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
        return beanClass.getMethod(camelMethodName);
    }

    private static Method getBeanSetter(Class<?> beanClass, Field field) throws Exception {
        String fieldName = field.getName();
        String camelMethodName = "set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
        return beanClass.getMethod(camelMethodName, field.getType());
    }
}
