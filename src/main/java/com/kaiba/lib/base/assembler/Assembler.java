package com.kaiba.lib.base.assembler;

import com.kaiba.lib.base.constant.Values;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.kaiba.lib.base.assembler.NameMapper.DEFAULT;

public class Assembler {

    private boolean whenFlag = true;
    private Map<String, Object> result;

    public static Assembler on() {
        return new Assembler();
    }

    public static Assembler on(int capacity) {
        return new Assembler(capacity);
    }

    private Assembler() {
    }

    private Assembler(int capacity) {
        lazyMap(capacity);
    }

    public Assembler when(boolean condition) {
        whenFlag = condition;
        return this;
    }

    public Assembler put(String key, int value) {
        checkWhen();
        lazyMap().put(key, value);
        return this;
    }

    public Assembler put(String key, long value) {
        checkWhen();
        lazyMap().put(key, value);
        return this;
    }

    public Assembler put(String key, float value) {
        checkWhen();
        lazyMap().put(key, value);
        return this;
    }

    public Assembler put(String key, double value) {
        checkWhen();
        lazyMap().put(key, value);
        return this;
    }
    
    public Assembler put(String key, boolean value) {
        checkWhen();
        return put(key, value ? Values.TRUE : Values.FALSE);
    }

    public Assembler put(String key, Object value) {
        checkWhen();
        if (ReflectionUtils.notEmpty(value)) {
            lazyMap().put(key, value);
        }
        return this;
    }

    public Assembler put(String key, Supplier<Object> supplier) {
        if (!checkWhen()) {
            return this;
        }
        return put(key, supplier.get());
    }

    public Assembler putBean(String key, Object value, int grain) {
        checkWhen();
        if (grain <= KbGrain.UNSPECIFIED) {
            return put(key, value);
        }
        if (ReflectionUtils.notEmpty(value)) {
            if (value instanceof List<?>) {
                List<?> list = ((List<?>) value).stream()
                        .map(obj -> ReflectionUtils.fillMapWithBean(
                                null, obj, DEFAULT, grain))
                        .collect(Collectors.toList());
                lazyMap().put(key, list);
            } else {
                lazyMap().put(key, ReflectionUtils.fillMapWithBean(
                        null, value, DEFAULT, grain));
            }
        }
        return this;
    }

    public Assembler withMap(Map<String, Object> map) {
        checkWhen();
        if (map != null && map.size() != 0) {
            lazyMap(map);
        }
        return this;
    }

    public Assembler withMap(Map<String, Object> map, int grain) {
        checkWhen();
        return withMap(map, DEFAULT, grain);
    }

    public Assembler withMap(Map<String, Object> map, FieldNameMapper fieldNameMapper, int grain) {
        checkWhen();
        if (grain <= KbGrain.UNSPECIFIED) {
            return withMap(map);
        }
        if (map != null && map.size() != 0) {
            result = map.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> ReflectionUtils.fillMapWithBean(null, entry.getValue(), fieldNameMapper, grain)
                    ));
        }
        return this;
    }

    public Assembler withBean(Object bean, FieldNameMapper fieldNameMapper, int grain) {
        checkWhen();
        if (bean != null) {
            ReflectionUtils.fillMapWithBean(lazyMap(), bean, fieldNameMapper, grain);
        }
        return this;
    }

    public Assembler withBean(Object bean, int grain) {
        checkWhen();
        return withBean(bean, DEFAULT, grain);
    }

    public Assembler withBean(Object bean, FieldNameMapper fieldNameMapper) {
        checkWhen();
        if (bean != null) {
            ReflectionUtils.fillMapWithBean(lazyMap(), fieldNameMapper, bean);
        }
        return this;
    }

    public Assembler withBean(Object bean) {
        checkWhen();
        return withBean(bean, DEFAULT);
    }

    public List<?> asList(List<?> value, int grain) {
        if (null != result) {
            throw new MappingException("Assembler.asList() is a standalone method !!!");
        }
        if (null == value || grain <= KbGrain.UNSPECIFIED) {
            return value;
        }
        return ((List<?>) value).stream()
                .map(obj -> ReflectionUtils.fillMapWithBean(null, obj, null, grain))
                .collect(Collectors.toList());
    }

    public Map<String, Object> result() {
        return result;
    }

    // --------------------------------------------

    private Map<String, Object> lazyMap(int size) {
        if (null == result) {
            if (size > 0) {
                result = new HashMap<>(size);
            } else {
                result = new HashMap<>();
            }
        }
        return result;
    }

    private Map<String, Object> lazyMap(Map<String, Object> map) {
        if (null == result) {
            result = map;
        } else {
            result.putAll(map);
        }
        return result;
    }

    private Map<String, Object> lazyMap() {
        return lazyMap(0);
    }

    private boolean checkWhen() {
        boolean flag = whenFlag;
        whenFlag = true;
        return flag;
    }

}
