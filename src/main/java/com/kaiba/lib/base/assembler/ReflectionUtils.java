package com.kaiba.lib.base.assembler;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.*;

import static com.kaiba.lib.base.assembler.NameMapper.DEFAULT;

/**
 * author: lyux
 * date: 18-7-27
 */
@Slf4j
final class ReflectionUtils {

    private static final int NO_GRAIN = Integer.MIN_VALUE;

    private static final LoadingCache<Class<?>, BeanInfo> reflectionCache = Caffeine.newBuilder()
            .expireAfterAccess(Duration.ofMinutes(3))
            .build(BeanInfo::new);

    static Map<String, Object> fillMapWithBean(Map<String, Object> map, FieldNameMapper fieldNameMapper, Object bean) {
        return fillMapWithBean(map, bean, fieldNameMapper, NO_GRAIN);
    }

    static Map<String, Object> fillMapWithBean(Map<String, Object> map, Object bean, FieldNameMapper fieldNameMapper, int grain) {
        fieldNameMapper = (null == fieldNameMapper) ? DEFAULT : fieldNameMapper;
        Class<?> clazz = bean.getClass();

        BeanInfo info = reflectionCache.get(clazz);
        if (info == null) {
            info = new BeanInfo(clazz);
            reflectionCache.put(clazz, info);
        }

        List<BeanField> beanFields = info.getReadableFields();
        for (BeanField bf : beanFields) {
            if (!bf.isReadable()) {
                continue;
            }

            KbGrain kbGrain = (grain < 0) ? null : bf.getKbGrain();
            if (null != kbGrain && kbGrain.value() > grain) {
                continue;
            }

            Object fieldValue = bf.get(bean);
            if (null == fieldValue) {
                continue;
            }

            if (null != kbGrain && kbGrain.subEnabled()) {
                // treat value as KbGrain annotated object
                final int subGrain;
                if (KbGrain.UNSPECIFIED != kbGrain.subGrain()) {
                    subGrain = kbGrain.subGrain();
                } else {
                    // object grain unspecified, use grain passed by the method
                    subGrain = grain;
                }

                Object fieldValueObj = null;
                if (fieldValue instanceof List) {
                    // treat list elements as KbGrain annotated object
                    List<?> list = (List<?>) fieldValue;
                    if (list.size() != 0) {
                        List<Object> objList = new ArrayList<>(list.size());
                        for (Object obj : list) {
                            objList.add(fillMapWithBean(null, obj, null, subGrain));
                        }
                        fieldValueObj = objList;
                    }
                } else {
                    Map<String, Object> fieldMap = fillMapWithBean(null, fieldValue, null, subGrain);
                    if (fieldMap != null && fieldMap.size() != 0) {
                        fieldValueObj = fieldMap;
                    }
                }

                if (null != fieldValueObj) {
                    map = lazyMap(map, beanFields.size());
                    map.put(fieldNameMapper.getKey(bean, bf.name()), fieldValueObj);
                }
            } else {
                map = lazyMap(map, beanFields.size());
                map.put(fieldNameMapper.getKey(bean, bf.name()), fieldValue);
            }
        }
        return map;
    }

    // ---------------------------------------------------------

    static void mapping(Object source, Object target, boolean ignoreNullField) {
        Map<String, Object> sourceValueMap = fillMapWithBean(null, null, source);
        mappingMap(sourceValueMap, target, ignoreNullField);
    }

    static void mappingMap(Map<String, Object> sourceValueMap, Object target, boolean ignoreNullField) {
        if (null == sourceValueMap || sourceValueMap.isEmpty()) {
            // nothing to do
            return;
        }

        Class<?> targetClass = target.getClass();
        BeanInfo info = reflectionCache.get(targetClass);
        if (info == null) {
            info = new BeanInfo(targetClass);
            reflectionCache.put(targetClass, info);
        }
        List<BeanField> list = info.getWritableFields();
        if (list.size() == 0) {
            return;
        }

        for (BeanField bf : list) {
            String sourceFieldName = bf.getMappingName();
            Object sourceFieldValue = sourceValueMap.get(sourceFieldName);
            if (ignoreNullField && sourceFieldValue == null) {
                continue;
            }
            try {
                bf.set(target, sourceFieldValue);
            } catch (Exception ignore) {
            }
        }
    }

    static void mappingField(Map<String, Object> sourceValueMap, Object target, boolean ignoreNullField) {
        if (null == sourceValueMap || sourceValueMap.isEmpty()) {
            // nothing to do
            return;
        }

        Class<?> targetClass = target.getClass();
        BeanInfo info = reflectionCache.get(targetClass);
        if (info == null) {
            info = new BeanInfo(targetClass);
            reflectionCache.put(targetClass, info);
        }
        List<BeanField> list = info.getWritableFields();
        if (list.size() == 0) {
            return;
        }

        for (BeanField bf : list) {
            String sourceFieldName = bf.getMappingName();
            Object sourceFieldValue = sourceValueMap.get(sourceFieldName);
            if (ignoreNullField && sourceFieldValue == null) {
                continue;
            }
            try {
                bf.set(target, sourceFieldValue);
            } catch (Exception ignore) {
            }
        }
    }

    // ---------------------------------------------------------

    private static Map<String, Object> lazyMap(Map<String, Object> map, int size) {
        if (null == map) {
            map = new HashMap<>(size);
        }
        return map;
    }

    static boolean notEmpty(Object object) {
        if (null == object) {
            return false;
        }
        if (object instanceof String) {
            return !"".equals(object);
        } else if (object instanceof Collection<?>) {
            return !((Collection<?>) object).isEmpty();
        }
        return true;
    }

}
