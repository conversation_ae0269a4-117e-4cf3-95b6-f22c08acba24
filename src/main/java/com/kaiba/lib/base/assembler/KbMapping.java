package com.kaiba.lib.base.assembler;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * author: lyux
 * date: 18-7-27
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface KbMapping {

    @AliasFor("mapping")
    String value() default "";

    @AliasFor("value")
    String mapping() default "";

}
