package com.kaiba.lib.base.assembler;

/**
 * author: lyux
 * date: 18-7-27
 */
public final class NameMapper {

    private NameMapper() {}

    public static final FieldNameMapper DEFAULT = (bean, fieldName) -> fieldName;


    public static class Prefix implements FieldNameMapper {

        private String prefix;
        private boolean camel;

        public Prefix(String prefix) {
            this.prefix = prefix;
        }

        public Prefix(String prefix, boolean camel) {
            this.prefix = prefix;
            this.camel = camel;
        }

        @Override
        public String getKey(Object bean, String fieldName) {
            if (camel) {
                return prefix + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            } else {
                return prefix + fieldName;
            }
        }
    }

    public static class PrefixCamel extends Prefix {

        public PrefixCamel(String prefix) {
            super(prefix, true);
        }
    }

}
