package com.kaiba.lib.base.assembler;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * author: lyux
 * date: 18-7-27
 */
@Slf4j
class BeanInfo {

    private final Class<?> clazz;
    private List<BeanField> readableFields;
    private List<BeanField> writableFields;

    BeanInfo(Class<?> clazz) {
        this.clazz = clazz;
    }

    List<BeanField> getReadableFields() {
        if (null == readableFields) {
            Field[] fields = clazz.getDeclaredFields();
            List<BeanField> list = new ArrayList<>(fields.length);
            for (Field field : fields) {
                BeanField bf = new BeanField(clazz, field);
                if (bf.isReadable()) {
                    list.add(bf);
                }
            }
            if (list.size() == 0) {
                readableFields = Collections.emptyList();
                log.warn(clazz.getName() + " readable field list empty");
            } else {
                readableFields = Collections.unmodifiableList(list);
            }
        }
        return readableFields;
    }

    List<BeanField> getWritableFields() {
        if (null == writableFields) {
            Field[] fields = clazz.getDeclaredFields();
            List<BeanField> list = new ArrayList<>(fields.length);
            for (Field field : fields) {
                BeanField bf = new BeanField(clazz, field);
                if (bf.isWritable()) {
                    list.add(bf);
                }
            }
            if (list.size() == 0) {
                writableFields = Collections.emptyList();
                log.warn(clazz.getName() + " writable field list empty");
            } else {
                writableFields = Collections.unmodifiableList(list);
            }
        }
        return writableFields;
    }

}
