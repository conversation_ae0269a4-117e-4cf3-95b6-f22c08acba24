package com.kaiba.lib.base.cache;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;

import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

/**
 * author: lyux
 * date: 19-5-8
 *
 * 单实例懒加载缓存, 根据时间间隔刷新实例.
 */
public class LazyExpireCache<T> {

    private final long expireTime; // 过期时间, 单位毫秒. 时长, 非时间戳.
    private final Supplier<T> supplier;
    private final DataValidator<T> validator;
    private final Executor executor;
    private final ReentrantLock lock = new ReentrantLock();

    private volatile T data;
    private volatile long refreshTime;
    private final AtomicBoolean refreshing;

    public LazyExpireCache(long expireTime, Supplier<T> supplier, DataValidator<T> validator, Executor executor) {
        if (expireTime <= 0) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "expire time should be positive");
        }
        if (null == supplier) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "supplier should not be null");
        }
        this.expireTime = expireTime;
        this.supplier = supplier;
        this.validator = validator != null ? validator : Objects::nonNull;
        this.executor = executor;
        this.refreshing = new AtomicBoolean(false);
    }

    public T getData() {
        return getData(false);
    }

    public T getData(boolean asyncRefresh) {
        if (!validator.validate(this.data)) {
            lock.lock();
            try {
                if (!validator.validate(this.data)) {
                    this.data = supplier.get();
                    this.refreshTime = System.currentTimeMillis();
                }
            } finally {
                lock.unlock();
            }
        } else {
            if (System.currentTimeMillis() - refreshTime > expireTime) {
                // need refresh
                if (asyncRefresh && !refreshing.get()) {
                    if (executor == null) {
                        throw new IllegalStateException("no executor found to run async refresh");
                    }
                    executor.execute(() -> {
                        if (refreshing.compareAndSet(false, true)) {
                            refresh();
                            refreshing.set(false);
                        }
                    });
                } else {
                    if (lock.tryLock()) {
                        try {
                            this.data = supplier.get();
                            this.refreshTime = System.currentTimeMillis();
                        } finally {
                            lock.unlock();
                        }
                    }
                }
            }
        }

        return this.data;
    }

    public void setData(T data) {
        if (validator.validate(data)) {
            lock.lock();
            this.data = data;
            this.refreshTime = System.currentTimeMillis();
            lock.unlock();
        } else {
            throw new IllegalArgumentException("provided data invalid");
        }
    }

    public T refresh() {
        lock.lock();
        try {
            this.data = supplier.get();
            this.refreshTime = System.currentTimeMillis();
        } finally {
            lock.unlock();
        }
        return this.data;
    }

    public interface DataValidator<T> {
        boolean validate(T data);
    }

    public static class Builder<T> {
        private long expireTime;
        private T initData;
        private Supplier<T> supplier;
        private DataValidator<T> validator;
        private Executor executor;

        public LazyExpireCache<T> create() {
            if (validator == null) {
                validator = data -> true;
            }
            LazyExpireCache<T> cache = new LazyExpireCache<>(expireTime, supplier, validator, executor);
            if (initData != null) {
                cache.setData(initData);
            }
            return cache;
        }

        public Builder<T> setInitData(T data) {
            this.initData = data;
            return this;
        }

        public Builder<T> setSupplier(Supplier<T> supplier) {
            this.supplier = supplier;
            return this;
        }

        public Builder<T> setExpireTime(long expireTime) {
            this.expireTime = expireTime;
            return this;
        }

        public Builder<T> setDataValidator(DataValidator<T> validator) {
            this.validator = validator;
            return this;
        }

        public Builder<T> setDataValidatorNonNull() {
            this.validator = Objects::nonNull;
            return this;
        }

        public Builder<T> setExecutor(Executor executor) {
            this.executor = executor;
            return this;
        }
    }

}
