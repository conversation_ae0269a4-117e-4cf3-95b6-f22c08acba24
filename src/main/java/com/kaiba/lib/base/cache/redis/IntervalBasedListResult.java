package com.kaiba.lib.base.cache.redis;

import lombok.Getter;

import java.util.List;

/**
 * author: lyux
 * date: 2023-08-01
 */
@Getter
public class IntervalBasedListResult<T> {

    private final List<T> list;
    private String lastMark;
    private String currentMark;

    public IntervalBasedListResult(List<T> list, String lastMark, String currentMark) {
        this.list = list;
        this.lastMark = lastMark;
        this.currentMark = currentMark;
    }

    public IntervalBasedListResult(List<T> list) {
        this.list = list;
    }

}
