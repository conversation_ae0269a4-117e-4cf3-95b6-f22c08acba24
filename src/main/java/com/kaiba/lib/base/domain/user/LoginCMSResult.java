package com.kaiba.lib.base.domain.user;

import com.kaiba.lib.base.constant.user.LoginCMSResultCode;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * author: lyux
 * date: 2024-08-13
 */
@Data
@ToString
@NoArgsConstructor
public class LoginCMSResult {

    /** 登录结果. {@link LoginCMSResultCode} */
    @NotNull
    private Integer code;

    /** 提示信息 */
    @NotNull
    private String message;

    /** 鞥路数据 */
    private LoginUserModel login;

    /** 阿里防水墙返回码. 在使用带防水墙的投票接口时会对该值赋值. */
    private Integer aliACSCode;

    public LoginCMSResult(LoginUserModel login) {
        this.code = LoginCMSResultCode.SUCCESS.getValue();
        this.message = LoginCMSResultCode.SUCCESS.getDescription();
        this.login = login;
    }

    public LoginCMSResult(LoginCMSResultCode code, String message) {
        this.code = code.getValue();
        this.message = message == null ? code.getDescription() : message;
    }

    public LoginCMSResult(LoginCMSResultCode code) {
        this(code, code.getDescription());
    }

    public boolean isOK() {
        return code == LoginCMSResultCode.SUCCESS.getValue();
    }

}
