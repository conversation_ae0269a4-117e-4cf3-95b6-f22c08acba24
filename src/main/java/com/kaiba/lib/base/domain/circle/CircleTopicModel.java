package com.kaiba.lib.base.domain.circle;

import com.kaiba.lib.base.constant.circle.CircleGetuiType;
import com.kaiba.lib.base.constant.circle.CircleState;
import com.kaiba.lib.base.constant.circle.CircleType;
import com.kaiba.lib.base.constant.note.NoteListStyle;
import com.kaiba.lib.base.constant.note.NoteThreadCondition;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.lib.base.domain.note.NoteThreadPostRule;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 2023-03-06
 */
@Data
@ToString
@NoArgsConstructor
public class CircleTopicModel {

    /** 话题 id */
    private String circleId;

    /** 板块 id. {@link NoteThreadModel#getId()} */
    private String threadId;

    /** 电台 id */
    private Integer siteId;

    /** 板块状态. {@link CircleState} */
    private Integer state;

    /** 帖子列表首选样式. 默认为 {@link NoteListStyle#POST_LIST} */
    private String style;

    /** 热议帖子列表标题, 可用于 tab */
    private String hotTitle;

    /** 是否首选展示热议贴 */
    private Boolean hotPrefer;

    /** 热议帖子上限, 负数表示无限制. */
    private Integer hotMax;

    /** 置顶帖子上限, 负数或零表示不可置顶. */
    private Integer topMax;

    /** 名称 */
    private String title;

    /** 封面 */
    private String cover1;

    /** 封面 */
    private String cover2;

    /** 方形封面, 主要用于个人专版头像 */
    private String avatar;

    /** 背景渐变色, 格式 "#808080" */
    private String color;

    /** 板块状态. {@link NoteThreadCondition} */
    private Integer condition;

    /** 发帖规则. 首发于该板块下的帖子都要遵守该规则. */
    private NoteThreadPostRule postRule;

    /** 感兴趣的用户头像, 取最后 3 个. */
    private List<String> interestedAvatars;

    /** 感兴趣的用户数 */
    private Long interestedCount;

    /** 用于老版圈子排序. 升序排列. */
    private Integer order;

    /** 以帖子形式给出的话题内容, 帖子 id */
    private String noteId;

    /** 以帖子形式给出的话题内容. 对应 {@link #noteId} */
    private NoteModel content;

    /** 分享 */
    private ShareModel share;

    /** 圈子类型 {@link CircleType}*/
    private Integer type;

    /** 个推埋点类型 {@link CircleGetuiType}*/
    private String getuiType;

    public boolean isShow() {
        return circleId != null && threadId != null && (state == null || state == CircleState.SHOW.getValue());
    }

}
