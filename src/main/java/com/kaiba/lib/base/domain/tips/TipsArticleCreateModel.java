package com.kaiba.lib.base.domain.tips;

import com.kaiba.lib.base.constant.tips.TipsArticleState;
import com.kaiba.lib.base.constant.tips.TipsArticleStyle;
import com.kaiba.lib.base.constant.tips.TipsArticleType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 2023-02-10
 */
@Data
@ToString
@NoArgsConstructor
public class TipsArticleCreateModel {

    /** {@link TipsInstanceModel} */
    private String instanceId;

    /** 和 {@link #instanceId} 二选一 */
    private String instanceKey;

    /** 由运营手动设置的索引编号, 可作为排序依据. */
    private Integer idx;

    /** 标签, 用以方便进行分类. 可为空. */
    private List<String> tags;

    /** 自定义文章标识, 在实例内唯一, 默认为 UUID. */
    private String refKey;

    /** 状态. {@link TipsArticleState} */
    private Integer state;

    /** 类型. {@link TipsArticleType} */
    private Integer type;

    /** 内容格式. {@link TipsArticleStyle} */
    private Integer style;

    /** 标题. 可为空. */
    private String title;

    /** 副标题 */
    private String subtitle;

    /** 封面. 可为空. */
    private String cover;

    /** 内容, HTML 格式 */
    private String content;

    /** 如果开启了评论区, 此为评论区帖子列表板块 ID */
    private String viewThreadId;

    /** 如果开启了评论功能, 此为发帖的目标板块 ID */
    private String postThreadId;

    /** 跳转页面. */
    private String action;

    /** 跳转页面参数. */
    private Map<String, Object> actionParams;

    /** 以键值定义的额外属性值. */
    private Map<String, String> attr;

    /** 作者, 可为空. */
    private Integer userId;

    /** 稿件链接 */
    private String asTmuLink;

    /** 预定的自动投递时间. 单位毫秒. */
    private Long asTmuSendTime;

    // ---------------------------------------

    /** 当文章类型为选择题时, 此为可用选项. */
    private List<Option> options;

    @Data
    @ToString
    @NoArgsConstructor
    public static class Option {

        /** 选项名称 */
        private String name;

        /** 选项内容 */
        private String text;

        /** 是否正确答案 */
        private Boolean correct;
    }

    // --------------------------------------------------

    public boolean shouldCreateOptions() {
        if (options == null || options.isEmpty()) {
            return false;
        }
        if (type == null) {
            return false;
        }
        return type == TipsArticleType.SINGLE_CHOICE.getValue() || type == TipsArticleType.MULTI_CHOICE.getValue();
    }

}
