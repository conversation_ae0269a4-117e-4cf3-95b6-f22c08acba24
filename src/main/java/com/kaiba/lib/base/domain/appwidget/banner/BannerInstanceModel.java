package com.kaiba.lib.base.domain.appwidget.banner;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 2023-05-31
 */
@Data
@ToString
@NoArgsConstructor
public class BannerInstanceModel {

    /** 主键 */
    private String id;

    /** 逻辑主键. */
    private String key;

    /** 电台标识 */
    private Integer siteId;

    /** 图片的相对高度要求, 单位不限. 用于约束该实例下图片的宽高比. 对于已有 {@link BannerImageModel} 的实例该字段不允许修改. */
    private Integer width;

    /** 图片的相对宽度要求, 单位不限. 用于约束该实例下图片的宽高比. 对于已有 {@link BannerImageModel} 的实例该字段不允许修改. */
    private Integer height;

    /** 自动翻页的间隔时间, 单位毫秒. 非正值表示不会自动翻页. */
    private Integer autoplay;

    /**
     * 仅用于后台管理筛选的类型
     * 类型声明 1:默认 2:临时活动
     */
    private Integer backendFilterType;

    /** 简短名称, 只显示在管理后台 */
    private String name;

    /** 展示标题, 在客户端组件上展示 */
    private String title;

    /** 介绍, 只显示在管理后台. 非必填. */
    private String description;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    // -----------------------------------------------------------

    private List<BannerImageModel> imageList;

}
