package com.kaiba.lib.base.domain.wx;

import lombok.Data;

/**
 * <AUTHOR>
 * @version WxOpenAppBlockUserModel, v0.1 2024/5/31 09:20 daopei Exp $
 **/
@Data
public class WxOpenAppBlockUserModel {

    private String id;

    /** 微信用户ID:公众号唯一 */
    private String openId;
    /** 电台ID */
    private Integer siteId;
    /** 屏蔽原因 */
    private String reason;
    /** 屏蔽来源消息ID */
    private String messageId;
    /** 屏蔽开始时间 */
    private Long startTime;
    /** 屏蔽结束时间 */
    private Long endTime;
    /** 屏蔽状态 0:无效,1:生效*/
    private Integer state;
    /** 操作人 */
    private Integer operatorUserId;
    /** 创建时间 */
    private Long createTime;
    /** 修改时间 */
    private Long updateTime;
}
