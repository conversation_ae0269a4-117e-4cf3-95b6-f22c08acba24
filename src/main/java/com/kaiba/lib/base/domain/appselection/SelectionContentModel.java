package com.kaiba.lib.base.domain.appselection;


import com.kaiba.lib.base.constant.apphome.AppHomeSelectionOrigin;
import com.kaiba.lib.base.constant.apphome.AppHomeSelectionStyle;
import com.kaiba.lib.base.constant.apphome.AppHomeSelectionTarget;
import com.kaiba.lib.base.domain.IActionGetterSetter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Map;
import java.util.Set;

/**
 * author: lyux
 * date: 2023-07-18
 *
 * 推荐备选内容池.
 */
@Data
@ToString
@NoArgsConstructor
public class SelectionContentModel implements IActionGetterSetter {

    private String id;

    /** 电台 id. */
    private Integer siteId;

    /** 内容来源 {@link AppHomeSelectionOrigin} */
    private String origin;

    /** 冗余字段: 已被推荐到的推荐位. 为空或长度为 0 表示尚未上架到任何推荐位. {@link AppHomeSelectionTarget} */
    private Set<String> targets;

    /** 内容引用 ID. 和 {@link #origin} 一起定位具体内容. */
    private String ref1;

    /** 内容引用 ID. 和 {@link #origin} 一起定位具体内容. */
    private String ref2;

    /** 内容引用 ID. 和 {@link #origin} 一起定位具体内容. */
    private String ref3;

    /** 标题 */
    private String title;

    /** 副标题, 主要用于推送副标题 */
    private String subTitle;

    /** 内容提示, 用于前端展示. 默认为空, 为空时前端展示 时间+来源, 如 "11月12日 20:56 资讯". */
    private String hint;

    /** 大图/一图/二图 模式封面. 宽高比固定为 3:2 */
    private SelectionCover image1;

    /** 二图 模式封面. 宽高比固定为 3:2 */
    private SelectionCover image2;

    /** 腰封模式封面. {@link AppHomeSelectionStyle#BELT}. 宽高比固定为 6.98:1 */
    private SelectionCover imageBelt;

    /** 推荐者 */
    private Integer userId;

    /** 跳转页面. 对于少数 {@link #origin} 类型, 该参数才有效. */
    private String action;

    /** 跳转页面参数. */
    private Map<String, Object> actionParams;

    /** 该条内容被执行推送动作的次数 */
    private Integer pushCount;

    /** 最后一次推送时间, 单位毫秒 */
    private Long pushTime;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
