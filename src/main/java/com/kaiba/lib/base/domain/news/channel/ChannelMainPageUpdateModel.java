package com.kaiba.lib.base.domain.news.channel;

import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.news.article.ChannelArticleListStyle;
import com.kaiba.lib.base.constant.news.article.ChannelProgrammeListStyle;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupTopicTab;
import com.kaiba.lib.base.domain.news.pool.topic.TopicLayoutModel;
import lombok.Data;

import java.util.List;

/**
 * author: lyux
 * date: 2024-09-09
 */
@Data
public class ChannelMainPageUpdateModel {

    /** 频道标识. {@link NewsChannel} */
    private String channelKey;

    /** 实例标题 */
    private String title;

    /** 栏目列表样式类型. {@link ChannelProgrammeListStyle} */
    private String programmeListStyle;

    /** 全部文章列表样式类型. {@link ChannelArticleListStyle} */
    private String articleListStyle;

    /** 顶部布局. {@link TopicLayoutModel#getId()} */
    private String layoutId;

    /** tab 分组标识属性. */
    private List<GroupTopicTab> tabs;

    /** 分享配置 */
    private ShareModel share;

}
