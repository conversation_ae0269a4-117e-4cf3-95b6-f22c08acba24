package com.kaiba.lib.base.domain.common;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 2023-02-06
 *
 * 多张 {@link Image}.
 */
@Data
@ToString
@NoArgsConstructor
public class Gallery {

    /** {@link com.kaiba.lib.base.constant.common.GalleryDisplayType} */
    private Integer displayType;

    /** 画廊模式下的图片自动切换间隔, 单位毫秒. 为空表示不进行自动切换. */
    private Integer switchInterval;

    /** 图片列表 */
    private List<Image> images;

}
