package com.kaiba.lib.base.domain.prize;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.util.Map;

/**
 * author: lyux
 * date: 2021-02-07
 */
@Data
@ToString
@NoArgsConstructor
public class PrizeUpdateAttrsModel {

    /** 帖子 id */
    private String prizeId;

    /** 操作者 id */
    private Integer operatorId;

    /** 以键值定义的额外属性值. */
    private Map<String, String> attrs;

    /** 是否整体替换 note 中的 extra 字段. 如果为 false 则只会更新 {@link #extra} 中声明的键值. */
    private Boolean replaceExtra;

}
