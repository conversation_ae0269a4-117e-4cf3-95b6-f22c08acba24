package com.kaiba.lib.base.domain.news.pool.bygroup;

import com.kaiba.lib.base.domain.news.article.ArticleModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2023-07-27
 *
 * [文章 id 聚合策略] 简单分组: 使用一个分类层级对文章进行分组.
 * 本类是分组文章的关系表.
 */
@Data
@ToString
@NoArgsConstructor
public class GroupArticleModel {

    private String id;

    /** {@link GroupModel#getKey()} */
    private String group;

    /** {@link ArticleModel#getId()} */
    private String articleId;

    /** 排序规则-置顶排序: 查询时以此字段降序排列. */
    private Long seq;

    /**
     * 排序规则-置顶排序: 人工指定的排序依据, 降序, 最大值 999.
     * 最终会根据算法生成 {@link #seq}. 因此本字段仅做记录, 实际排序应使用 {@link #seq} 字段
     */
    private Long idx;

    /** 排序规则-批次排序: 批次 */
    private Long batch;

    /** 排序规则-批次排序: 批次内的序号 */
    private Long batchIdx;

    /** 排序规则-批次排序: 查询时以此字段降序排列. */
    private Long batchSeq;

    /** 创建时间, 单位毫秒. 用作排序, 表示文章被赋予该标签的时间. */
    private Long createTime;

}
