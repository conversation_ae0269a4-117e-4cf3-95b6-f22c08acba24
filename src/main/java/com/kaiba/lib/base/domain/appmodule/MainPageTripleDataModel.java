package com.kaiba.lib.base.domain.appmodule;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Data
@ToString
@NoArgsConstructor
public class MainPageTripleDataModel {

    private String title;

    private String action;

    private Map<String, Object> actionParams;

    private List<MainPageTripleModuleModel> moduleList;

}
