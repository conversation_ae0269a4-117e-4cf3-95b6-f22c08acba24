package com.kaiba.lib.base.domain.counter;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 21-05-25
 */
@Data
@ToString
@NoArgsConstructor
public class KbCounterModel {

    private String id;

    /**
     * 全局唯一的字符串标识, 用以快速检索到某个特定应用场景对应的计数器实例. 可为空, 非空时全局唯一. 创建后不可修改.
     * 用以解决比如测试库和正式库如果用 {@link #id} 作为标识, 会出现两个环境不一致的情况.
     * 建议采用 function-functionInstanceId 的格式以避免冲突. 例:
     * 建党百年用户接龙计数器的 key 值可以设置为:
     * ccp_100_anniversary_relay_60764dd610eeae6c0182a2bf
     */
    private String key;

    /** 名称. */
    private String name;

    /** 描述. */
    private String description;

    /** 真实计数. */
    private Long count;

    /** 展示计数. */
    private Long displayCount;

    /**
     * 作用于 {@link #count}, 表示单用户的最大计数. 默认为 0, 表示不限制.
     * 为 0 适用于浏览量等场景;
     * 为 1 适用于一人一票场景;
     * 为 >1 适用于一人多票场景;
     */
    private Long uidLimit;

    /** {@link #uidLimit} 缓存重置的相对时间表达式. 用以实现 "每天每用户计数上限为10" 这类需求. 默认为空, 表示不可重置. */
    private String uidLimitResetRTE;

    /**
     * 虚拟计数规则 {@link KbCounterVirtualModel}. 用以实现部分需要伪造计数的场景 (技术是无罪的!).
     * 可为空, 为空表示不需要开启虚拟计数功能.
     * 将虚拟计数单独列出是为了让我们始终可以掌握真实计数.
     */
    private String virtualId;

    /**
     * 是否要将每个具体用户的计数持久化到数据库. 默认为 false.
     * 开启此选项会消耗大量存储及性能, 因此如无必要不要开启.
     */
    private Boolean recordUser;

    /** 结束时间. 单位毫秒, 时间戳. 默认为 3 个月. 结束时间仅用于清理缓存. */
    private Long endTime;

    /** 创建时间. 单位毫秒. */
    private Long createTime;

    /** 更新时间. 单位毫秒. */
    private Long updateTime;

    // --------------------------------------------------------

    /** 虚拟计数规则. 此为冗余字段, 见 {@link KbCounterVirtualModel#getVirtualCount()}. */
    private Long virtualCount;

    /** 虚拟计数规则. 此为冗余字段. 见 {@link KbCounterVirtualModel#getExpression()} ()}. */
    private String virtualExpression;

    /** 虚拟计数规则. {@link #virtualId} 对应的虚拟计数规则实例. */
    private KbCounterVirtualModel counterVirtual;

}
