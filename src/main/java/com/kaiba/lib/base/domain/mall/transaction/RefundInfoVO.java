package com.kaiba.lib.base.domain.mall.transaction;

import lombok.Data;

/**
 * Description: 退款信息VO
 * Author: ZM227
 * Date: 2024/8/29 10:40
 */
@Data
public class RefundInfoVO {

    /**
     * 退款单号
     */
    private String refundCode;

    /**
     * 退款金额
     */
    private Long refundFee;

    /**
     * 退款说明
     */
    private String refundDesc;

    /**
     * 申请退款时间
     */
    private Long refundTime;

    /**
     * 退款通过时间
     */
    private Long refundApproveTime;

    /**
     * 申请退款原因
     */
    private String refundReason;

    /**
     * 通过退款原因
     */
    private String approveReason;

    /**
     * 拒绝退款原因
     */
    private String rejectReason;

}
