package com.kaiba.lib.base.domain.da.sensors;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.domain.da.sensors.channel.SensorsChannelModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.LinkedList;
import java.util.List;

/**
 * author: lyux
 * date: 2023-11-17
 *
 * 解析 action 的结果
 */
@Data
@ToString
@NoArgsConstructor
public class APSubject {

    /** 内容 id */
    private String id;

    /** 内容标题 */
    private String title;

    /** 内容所属模块. {@link KbModule} */
    private Integer module;

    /** 内容所属模块名. */
    private String moduleName;

    /** 如果对应一个网页, 此为网址. */
    private String url;

    /** 内容发布时间, 即面向 C 端用户可见的时间点. */
    private Long publishTime;

    /** 内容创建时间. */
    private Long createTime;

    /** 内容内部分类或标签. */
    private String tag1;

    /** 内容内部分类或标签. */
    private String tag2;

    /** 内容内部分类或标签. */
    private String tag3;

    /** 如果内容存在作者概念, 此为内容作者. */
    private List<Author> authors;

    /** 如果是节目相关内容, 此为节目数据 */
    private APProgram program;

    /** 如果是爆料台相关内容, 此为爆料台栏目或话题信息 */
    private APReveal reveal;

    /** 如果是活动页, 此为活动信息 */
    private APActivity activity;

    /** 内容主体归属频率频道 */
    private SensorsChannelModel channel;

    // ---------------------------------------------------------

    /** 业务类型. {@link KbModule} */
    private String biz;

    /** 功能单元 */
    private String unit;

    /** 对象标识 1 */
    private String ref1;

    /** 对象标识 2 */
    private String ref2;

    /** 对象标识 3 */
    private String ref3;

    // ---------------------------------------------------------

    public static Builder on() {
        return new Builder();
    }

    // ---------------------------------------------------------

    @Data
    @ToString
    @NoArgsConstructor
    public static class Author {
        private Integer userId;
        private String name;
        private String type;
        private String group;

        public Author(Integer userId, String name, String type, String group) {
            this.userId = userId;
            this.name = name;
            this.type = type;
            this.group = group;
        }

        public Author(Integer userId, String name) {
            this.userId = userId;
            this.name = name;
        }
    }

    // ---------------------------------------------------------

    public static class Builder {

        private final APSubject subject;

        public Builder() {
            this.subject = new APSubject();
        }

        public Builder module(KbModule module) {
            subject.module = module.getValue();
            subject.moduleName = module.getDescription();
            subject.biz = module.name();
            return this;
        }

        public Builder subject(String id, String title) {
            subject.id = id;
            subject.title = title;
            return this;
        }

        public Builder time(Long publishTime, Long createTime) {
            subject.publishTime = publishTime;
            subject.createTime = createTime;
            return this;
        }

        public Builder tag(String tag1) {
            return tag(tag1, null, null);
        }

        public Builder tag(String tag1, String tag2, String tag3) {
            subject.tag1 = tag1;
            subject.tag2 = tag2;
            subject.tag3 = tag3;
            return this;
        }

        public Builder author(Integer userId, String name) {
            Author author = new Author();
            author.userId = userId;
            author.name = name;
            return author(author);
        }

        public Builder author(Author author) {
            if (author == null) {
                return this;
            }
            if (subject.authors == null) {
                subject.authors = new LinkedList<>();
            }
            subject.authors.add(author);
            return this;
        }

        public Builder authors(List<Author> authors) {
            subject.authors = authors;
            return this;
        }

        public Builder program(APProgram program) {
            subject.program = program;
            return this;
        }

        public Builder reveal(APReveal reveal) {
            subject.reveal = reveal;
            return this;
        }

        public Builder activity(APActivity activity) {
            subject.activity = activity;
            return this;
        }

        public Builder channel(SensorsChannelModel channel) {
            subject.channel = channel;
            return this;
        }

        public Builder unit(String unit) {
            subject.unit = unit;
            return this;
        }

        public Builder ref(String ref1) {
            subject.ref1 = ref1;
            return this;
        }

        public Builder ref(String ref1, String ref2, String ref3) {
            subject.ref1 = ref1;
            subject.ref2 = ref2;
            subject.ref3 = ref3;
            return this;
        }

        public APSubject create() {
            return subject;
        }

    }

}
