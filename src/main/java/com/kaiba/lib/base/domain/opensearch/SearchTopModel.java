package com.kaiba.lib.base.domain.opensearch;

import com.kaiba.lib.base.constant.opensearch.SearchTopState;
import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/08/24 16:51
 */
@Data
@ToString
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SearchTopModel extends SearchModuleModel {

    private String id;

    /** 电台id */
    private Integer siteId;

    /** 操作用户id */
    private Integer userId;

    /** 标题 */
    private String title;

    /** 置顶数据状态 {@link SearchTopState } */
    private Integer state;

    /** 搜索关键词 */
    private List<String> keywords;

    /** 搜索框显示文案 */
    private String copywriting;

    /** 图片 */
    private Image image;

    /** 图片 */
    private List<Image> pic;

    /** 生效时间 ms 可为空 */
    private Long startTime;

    /** 失效时间 ms 可为空 */
    private Long endTime;

    /** 指定消息点击时的跳转页面. */
    private String action;

    /** 指定消息点击时的跳转页面, 参数. */
    private Map<String, Object> actionParams;

    /** 创建时间 ms */
    private Long createTime;

}
