package com.kaiba.lib.base.domain.appwidget.textticker;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 2023-05-31
 */
@Data
@ToString
@NoArgsConstructor
public class TextTickerInstanceModel {

    /** 主键 */
    private String id;

    /** 逻辑主键. */
    private String key;

    /** 电台标识 */
    private Integer siteId;

    /** 自动翻页的间隔时间, 单位毫秒. 非正值表示不会自动翻页. */
    private Integer autoplay;

    /** 最大显示行数 */
    private Integer maxLine;

    /** 简短名称, 只显示在管理后台 */
    private String name;

    /** 介绍, 只显示在管理后台. 非必填. */
    private String description;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    // --------------------------------------------------------

    private List<TextTickerDataModel> dataList;

}
