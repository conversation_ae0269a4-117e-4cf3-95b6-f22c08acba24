package com.kaiba.lib.base.domain.user;

import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.user.ornament.CredModel;
import com.kaiba.lib.base.domain.user.ornament.CrestModel;
import com.kaiba.lib.base.domain.user.ornament.PendantModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * author: lyux
 * date: 18-8-28
 */
@Data
@NoArgsConstructor
public class UserModel {

    // ------------------------------------------
    // 基础信息

    /** 用户 ID. 同 {@link #userId}. 为兼容过往而保留. */
    private Integer id;

    /** 用户 ID. */
    private Integer userId;

    /** 用户名 */
    private String userName;

    /** 登录所用账号 */
    private String loginName;

    /** 用户手机号 */
    private String mobile;

    /** 登录密码, 明文密码 md5 后的值. 敏感信息. */
    private String password;

    /** 展示在用户信息栏的用户认证. */
    private String displayCred;

    /** 展示在用户信息栏的用户头冠. */
    private String displayCrest;

    /** 展示在用户信息栏的用户徽章. */
    private List<String> displayPendants;

    /** 用户所获取的徽章数量. 冗余字段. */
    private Integer pendantCount;

    /** 用户角色 id - 老角色系统 */
    @Deprecated
    private Integer role;

    /** 是否发帖时公开地址 */
    private Integer isPublic;

    // ------------------------------------------
    // 展示信息

    /** 用户头像 */
    private String avatar;

    /** 用户相册 */
    private List<Image> pic;

    /** 用户自定义的背景图像, 可能用在用户详情等页面 */
    private List<Image> background;

    /** 用户性别 */
    private Integer sex;

    /** 用户签名 */
    private String signature;

    /** 用户生日 */
    private String birthday;

    /** 用户车型 id */
    private String series;

    /** 用户车牌 */
    private String carNo;

    // ------------------------------------------
    // 数据

    /** 用户当前电台 id */
    private Integer siteId;

    /** 用户当前地市 id */
    private Integer source;

    /** 用户地市 code */
    private Integer cityCode;

    /** 客户端类型 */
    private Integer origin;

    /** php版老积分: 用户累计积分 - 只增不减 */
    private Integer score;

    /** php版老积分: 用户可用积分 - 可以消费 */
    private Integer scoreAvail;

    // ------------------------------------------
    // 时间信息

    /** 登录时间, 单位秒 */
    private Long loginTime;

    /** 更新时间, 单位秒 */
    private Long updateTime;

    /** 创建时间, 单位秒 */
    private Long createTime;

    // ------------------------------------------

    /** 用户认证 */
    private CredModel cred;

    /** 头冠, 必须为中心透明的方形 png */
    private CrestModel crest;

    /** 徽章 */
    private List<PendantModel> pendants;

    /** 通过用户积分总值换算出的用户等级. */
    private String level;

    // ------------------------------------------

    public UserModel(Integer id) {
        this.id = id;
        this.userId = id;
    }

    public static UserModel asAnonymousUser(Integer id) {
        UserModel user = new UserModel(id);
        user.setUserName("匿名用户");
        return user;
    }

}
