package com.kaiba.lib.base.domain.videolive;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/12/20 14:09
 **/
@Data
@ToString
public class PortalEditParams {

    @NotBlank(message = "直播间id不能为空")
    private String portalId;

    @NotBlank(message = "直播间标题不能为空")
    @Size(max = 20, message = "直播间标题不能超过20个字符")
    private String title;

    @NotBlank(message = "图片不能为空")
    private String image;

    private Long duration;

    @NotNull(message = "点击事件不能为空")
    private String action;

    private String actionParams;

    @NotNull(message = "操作用户不能为空")
    private Integer userId;

}
