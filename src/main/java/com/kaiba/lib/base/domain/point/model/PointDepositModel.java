package com.kaiba.lib.base.domain.point.model;

import com.kaiba.lib.base.constant.point.PointDepositExpirePolicy;
import com.kaiba.lib.base.constant.point.PointDepositState;
import com.kaiba.lib.base.domain.IUserIdGetter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2022-04-13
 *
 * 用户积分订单表. 积分明细的辅助表. 用于积分先预扣再执行的场景.
 * 该表数据只可增加和归档, 不可变更.
 */
@Data
@ToString
@NoArgsConstructor
public class PointDepositModel implements IUserIdGetter {

    private Long id;

    /** 积分实例 ID. {@link PointInstanceModel} */
    private Long iid;

    /** 扣减渠道 ID. {@link PointChannelReduceModel}. */
    private Long cid;

    /** 用户 ID. */
    private Integer uid;

    /** 状态. {@link PointDepositState}. */
    private Integer state;

    /** 关联的外部模块信息. 用于幂等性支持. */
    private String ref;

    /** 积分分值. 正数代表积分获取, 负数代表积分消费或扣罚. */
    private Integer point;

    /** 订单备注. 可为空. */
    private String remark;

    /** 如果订单流转至取消状态, 此为原因描述. */
    private String closeReason;

    /** 如果设置了截止时间, 此为处理策略. {@link PointDepositExpirePolicy} */
    private Integer expirePolicy;

    /** 截止时间, 单位毫秒, 可为空. */
    private Long expireTime;

    /** 修改时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

    @Override
    public Integer getUserId() {
        return getUid();
    }
}
