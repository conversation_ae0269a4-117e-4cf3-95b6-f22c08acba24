package com.kaiba.lib.base.domain.route;

import com.kaiba.lib.base.constant.route.KbDownstreamType;
import com.kaiba.lib.base.constant.route.KbRouteState;
import com.kaiba.lib.base.domain.auth.AuthPermissionModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 2021-03-24
 */
@Data
@ToString
@NoArgsConstructor
public class RouteQueryModel {

    /** 规则状态. {@link KbRouteState}. */
    private List<Integer> state;

    /** 下游的架构类型. {@link KbDownstreamType} */
    private List<Integer> downstreamType;

    /** 匹配路径. */
    private List<String> paths;

    /** 匹配条件 id. {@link MatchRuleModel}. */
    private String matchRuleId;

    /** 下发规则 id. {@link DispatchRuleModel}. */
    private String filterRuleId;

    /** 权限检测: 所需权限. {@link AuthPermissionModel}. */
    private List<String> permission;

    /** 鉴权规则 id. {@link AuthRuleModel}. */
    private String authRuleId;

    /** 排序规则, 字段名 */
    private String orderBy;

    /** 排序规则, 是否升序 */
    private Boolean orderAsc;

    /** 分页参数 */
    private Integer page;

    /** 分页参数 */
    private Integer pageSize;

}
