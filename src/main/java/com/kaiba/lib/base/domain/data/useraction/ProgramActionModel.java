package com.kaiba.lib.base.domain.data.useraction;

import com.kaiba.lib.base.domain.data.IKbDataV;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 19-12-11
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class ProgramActionModel implements IKbDataV {

    /** 节目 id */
    private String programId;

    /** 节目排班实例 id */
    private String instanceId;

    /** 节目话题 id */
    private String topicId;

    /** 该当节目的主持人列表, 用户 id */
    private List<Integer> es;

    /** 投票计数. 可为 1 或 空. */
    private Integer vote;

    /** 点赞计数. 可为 点赞数量 或 空. */
    private Integer praise;

    /** 打赏金额, 单位分. 可为 打赏金额 或 空. */
    private Integer reward;

    /** 打赏目标主持人. */
    private List<Integer> rewardEmcee;

    /** 发帖计数. 不管是否进入审核都计数. 可为 1 或 空. */
    private Integer post;

    /** 发帖进入审核计数. 可为 1 或 空. */
    private Integer postReview;

    /** 发帖文字数量. 可为 n 或 空. */
    private Integer postText;

    /** 发帖文字 md5 值, 用以对内容进行去重统计. */
    private String postTextMD5;

    /** 发帖图片数量. 可为 n 或 空. */
    private Integer postImage;

    /** 发帖音频数量. 可为 n 或 空. */
    private Integer postAudio;

    /** 发帖视频数量. 可为 n 或 空. */
    private Integer postVideo;

    /** 帖子回复计数. 不管是否进入审核都计数. 可为 1 或 空. */
    private Integer postReply;

    /** 帖子回复进入审核计数. 可为 1 或 空. */
    private Integer postReplyReview;

    /** 帖子回复文字数量. 可为 n 或 空. */
    private Integer postReplyText;

    /** 帖子点赞计数. 可为 1 或 空. */
    private Integer postPraise;

}
