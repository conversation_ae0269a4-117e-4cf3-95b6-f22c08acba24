package com.kaiba.lib.base.domain.applego.widget.home;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.lib.base.constant.common.DummyFantasy;
import com.kaiba.lib.base.domain.applego.IWidget;
import com.kaiba.lib.base.domain.applego.lego.LegoBlockModel;
import com.kaiba.lib.base.domain.applego.lego.LegoPageModel;
import com.kaiba.lib.base.domain.appwidget.banner.BannerInstanceModel;
import com.kaiba.lib.base.domain.appwidget.textticker.TextTickerInstanceModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2023-05-24
 */
@Data
@ToString
@NoArgsConstructor
public class WidgetHomeRecommendList implements IWidget {

    /** 主键 */
    private String id;

    /** 广告条的插入位置: 插在列表第几个元素之后. */
    private Integer adPosition;

    /** {@link BannerInstanceModel#getKey()} */
    private String adInstanceKey;

    /** {@link LegoBlockModel#getId()} */
    private String blockId;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    @Override
    public WidgetType getType() {
        return WidgetType.HOME_RECOMMEND_LIST;
    }

}
