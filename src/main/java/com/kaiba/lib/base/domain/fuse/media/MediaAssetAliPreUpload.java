package com.kaiba.lib.base.domain.fuse.media;

import lombok.Data;

/**
 * 阿里云预上传凭证
 * <AUTHOR>
 * @version MediaAssetAliPreUpload, v0.1 2024/10/28 15:29 daopei Exp $
 **/
@Data
public class MediaAssetAliPreUpload {

    /** 上传地址 */
    public String uploadAddress;

    /** 上传授权凭证 */
    public String uploadAuth;


    public static MediaAssetAliPreUpload as(String uploadAddress, String uploadAuth) {
        MediaAssetAliPreUpload result = new MediaAssetAliPreUpload();
        result.uploadAddress = uploadAddress;
        result.uploadAuth = uploadAuth;
        return result;
    }

}
