package com.kaiba.lib.base.domain.da.eventtrack.manual;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.workorder.WOBusiness;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * author: lyux
 * date: 2025-03-07
 */
public class DocManual {

    public static final List<DocDefine> DOC_LIST = Collections.unmodifiableList(Arrays.asList(

            // ------------------------------------------------------------------------------
            // 首页

            doc("首页-首屏TAB")
                    .desc("当首页底部导航切换至 AppHomeFrameTab.tabKey == home 时打点.")
                    .biz(KbModule.APP_MAIN_PAGE)
                    .ref1("home")
                    .ref2("{LegoLayoutTab.tabKey}")
                    .siteId("{LegoLayoutTab.siteId}")
                    .title("首页-首屏-{LegoLayoutTab.sTitle}"),

            doc("首页-圈子")
                    .desc("当首页底部导航切换至 AppHomeFrameTab.tabKey == circle 时打点. 需要同时打一个 <圈子话题> 访问点.")
                    .biz(KbModule.APP_MAIN_PAGE)
                    .siteId("访问电台ID")
                    .ref1("circle"),

            doc("首页-服务")
                    .desc("当首页底部导航切换至 AppHomeFrameTab.tabKey == service 时打点.")
                    .biz(KbModule.APP_MAIN_PAGE)
                    .siteId("访问电台ID")
                    .ref1("service"),

            doc("首页-活动")
                    .desc("当首页底部导航切换至 AppHomeFrameTab.tabKey == act_mix 时打点. 需要同时打一个 <活动瀑布流TAB> 访问点.")
                    .biz(KbModule.APP_MAIN_PAGE)
                    .siteId("访问电台ID")
                    .ref1("act_mix"),

            doc("首页-我的")
                    .desc("当首页底部导航切换至 AppHomeFrameTab.tabKey == personal 时打点.")
                    .biz(KbModule.APP_MAIN_PAGE)
                    .siteId("访问电台ID")
                    .ref1("personal"),

            // ------------------------------------------------------------------------------
            // 文章系统

            doc("文章详情")
                    .desc("文章系统所有渲染器详情页均适用该定义")
                    .biz(KbModule.NEWS_NEO)
                    .unit("ARTICLE")
                    .ref1("{ArticleModel.id}")
                    .siteId("{ArticleModel.siteId}")
                    .title("文章详情-{ArticleModel.title}"),

            doc("资讯专题")
                    .desc("普通版和增强版资讯专题均适用该定义")
                    .biz(KbModule.NEWS_NEO)
                    .unit("GROUP_TOPIC")
                    .ref1("{GroupTopicModel.id}")
                    .siteId("{GroupTopicModel.siteId}")
                    .title("资讯专题-{GroupTopicModel.title}"),

            doc("栏目首页")
                    .biz(KbModule.NEWS_NEO)
                    .unit("PROGRAMME")
                    .ref1("{CProgrammeModel.id}")
                    .siteId("{CProgrammeModel.siteId}")
                    .title("栏目首页-{CProgrammeModel.name}"),

            doc("频道首页")
                    .biz(KbModule.NEWS_NEO)
                    .unit("CHANNEL")
                    .ref1("{ChannelMainPageModel.id}")
                    .title("频道首页-{ChannelMainPageModel.channelName}"),

            // ------------------------------------------------------------------------------
            // 老资讯

            doc("老资讯详情页")
                    .biz(KbModule.NEWS)
                    .ref1("{NewsBaseModel.id}")
                    .title("老资讯-{NewsBaseModel.title}"),

            doc("热门专题")
                    .desc("热门专题 VO 定义在 activity 节点")
                    .biz(KbModule.HOT_TOPIC)
                    .ref1("{HotTopicModel.id}")
                    .siteId("{HotTopicModel.siteId}")
                    .title("热门专题-{HotTopicModel.title}"),


            // ------------------------------------------------------------------------------
            // 节目互动

            doc("节目互动")
                    .biz(KbModule.PROGRAM)
                    .unit("SCHEDULE")
                    .ref1("{TopicModel.programId}")
                    .ref2("{TopicModel.scheduleId}")
                    .siteId("{TopicModel.siteId}")
                    .title("节目互动-{TopicModel.scheduleName}"),

            doc("节目列表")
                    .biz(KbModule.PROGRAM)
                    .unit("PAGE")
                    .siteId("电台ID")
                    .ref1("program_list_page"),

            doc("当日节目排行")
                    .biz(KbModule.PROGRAM)
                    .unit("PAGE")
                    .siteId("电台ID")
                    .ref1("program_today_rank_page"),

            doc("节目用户排行")
                    .biz(KbModule.PROGRAM)
                    .unit("PAGE")
                    .siteId("电台ID")
                    .ref1("program_user_rank_page"),

            // ------------------------------------------------------------------------------
            // 节目音频回播

            doc("音频回播专辑列表")
                    .biz(KbModule.PLAYBACK)
                    .unit("PAGE")
                    .siteId("电台ID")
                    .ref1("playback_list_page"),

            doc("音频回播专辑页")
                    .biz(KbModule.PLAYBACK)
                    .unit("ALBUM")
                    .ref1("{PlaybackAlbumModel.id}")
                    .siteId("{PlaybackAlbumModel.siteId}")
                    .title("音频回播专辑-{PlaybackAlbumModel.title}"),

            doc("音频回播详情页")
                    .biz(KbModule.PLAYBACK)
                    .unit("ITEM")
                    .ref1("{PlaybackModel.albumId}")
                    .ref2("{PlaybackModel.id}")
                    .siteId("{PlaybackModel.siteId}")
                    .title("音频回播详情-{PlaybackModel.title}"),

            // ------------------------------------------------------------------------------
            // 爆料台

            doc("爆料台主页")
                    .desc("客户端首页<杭帮侠>TAB访问时, 也应该同时记录该页面的访问点.")
                    .biz(KbModule.REVEAL)
                    .unit("PAGE")
                    .siteId("电台ID")
                    .ref1("reveal_main_page"),

            doc("爆料台栏目主页")
                    .desc("爆料台 VO 定义在 activity 节点. 访问栏目主页时也应该对话题埋点.")
                    .biz(KbModule.REVEAL)
                    .unit("PROGRAM")
                    .ref1("{RevealProgramModel.id}")
                    .siteId("{RevealProgramModel.siteId}")
                    .title("爆料台栏目-{RevealProgramModel.title}"),

            doc("爆料台话题页")
                    .desc("爆料台 VO 定义在 activity 节点.")
                    .biz(KbModule.REVEAL)
                    .unit("SCHEDULE")
                    .ref1("{RevealScheduleModel.revealProgramId}")
                    .ref2("{RevealScheduleModel.id}")
                    .siteId("{RevealScheduleModel.siteId}")
                    .title("爆料台话题-{RevealProgramModel.title}-{RevealProgramModel.periodical}"),


            // ------------------------------------------------------------------------------
            // 剧好看/短剧

            doc("剧好看主页")
                    .desc("主要用于剧好看单页. 客户端首页<剧好看>TAB访问已有埋点.")
                    .biz(KbModule.MINI_THEATRE)
                    .unit("page")
                    .ref1("mini_theatre_main_page"),

            doc("剧好看-剧场页")
                    .desc("剧好看 VO 定义在 activity 节点.")
                    .biz(KbModule.MINI_THEATRE)
                    .unit("theatre")
                    .ref1("{MTTheatreModel.id}")
                    .title("剧好看剧场-{MTTheatreModel.title}"),

            doc("剧好看-剧集页")
                    .desc("剧好看 VO 定义在 activity 节点.")
                    .biz(KbModule.MINI_THEATRE)
                    .unit("episode")
                    .ref1("{MTEpisodeModel.theatreId}")
                    .ref2("{MTEpisodeModel.id}")
                    .title("剧好看剧集-{MTEpisodeModel.theatreTitle}-{MTEpisodeModel.title}"),

            // ------------------------------------------------------------------------------
            // 视频直播

            doc("视频直播页")
                    .desc("视频直播标题中, 开始时间请格式化为 YYYY-MM-dd")
                    .biz(KbModule.VIDEO_LIVE)
                    .ref1("{VideoLiveModel.id}")
                    .siteId("{VideoLiveModel.siteId}")
                    .title("视频直播-{VideoLiveModel.title}-{VideoLiveModel.startTime}"),

            // ------------------------------------------------------------------------------
            // 集团广播频率播放

            doc("听见杭州主页")
                    .biz(KbModule.AUDIO_LIVE)
                    .unit("PAGE")
                    .ref1("hangzhou_fm_plaza"),

            doc("听见杭州播放页")
                    .desc("听见杭州 VO 定义在 core 节点")
                    .biz(KbModule.AUDIO_LIVE)
                    .unit("GROUP")
                    .ref1("{AudioLiveInstanceModel.key}")
                    .title("听见杭州播放页-{AudioLiveInstanceModel.title}"),

            // ------------------------------------------------------------------------------
            // 圈子

            doc("圈子话题")
                    .desc("首页圈子相关话题/话题详情页, 均需打点. 注意 ref1 是帖子系统的板块ID, 不是圈子ID")
                    .biz(KbModule.CIRCLE)
                    .unit("TOPIC")
                    .ref1("{CircleThreadModel.id}", "圈子的板块ID")
                    .siteId("{CircleThreadModel.siteId}")
                    .title("圈子话题-{CircleThreadModel.title}"),

            // ------------------------------------------------------------------------------
            // 活动瀑布流

            doc("活动瀑布流TAB")
                    .title("活动瀑布流TAB-{ActMixTabModel.name}")
                    .biz(KbModule.ACT_MIX)
                    .siteId("{ActMixTabModel.siteId}")
                    .ref1("{ActMixTabModel.id}"),

            // ------------------------------------------------------------------------------
            // 工单系统

            doc("汽车维权主页")
                    .biz(KbModule.WORK_ORDER)
                    .unit("wo_page")
                    .ref1(WOBusiness.CAR_SAFEGUARD.name())
                    .ref2("wo_safeguard_main_page"),

            doc("汽车维权-投诉排行页")
                    .biz(KbModule.WORK_ORDER)
                    .unit("wo_page")
                    .ref1(WOBusiness.CAR_SAFEGUARD.name())
                    .ref2("wo_safeguard_rank_page"),

            doc("汽车维权-我的维权列表页")
                    .biz(KbModule.WORK_ORDER)
                    .unit("wo_page")
                    .ref1(WOBusiness.CAR_SAFEGUARD.name())
                    .ref2("wo_safeguard_my_case_list"),

            doc("汽车维权-我关注的维权列表页")
                    .biz(KbModule.WORK_ORDER)
                    .unit("wo_page")
                    .ref1(WOBusiness.CAR_SAFEGUARD.name())
                    .ref2("wo_safeguard_followed_case_list"),

            doc("汽车维权-案件详情页")
                    .biz(KbModule.WORK_ORDER)
                    .unit("wo_case")
                    .ref1(WOBusiness.CAR_SAFEGUARD.name())
                    .ref2("{WOCaseModel.id}")
                    .title("汽车维权案件详情-{WOCaseModel.id}"),

            doc("我要问局长主页")
                    .biz(KbModule.WORK_ORDER)
                    .unit("wo_page")
                    .ref1(WOBusiness.HZ_ASK_GOVERNOR.name())
                    .ref2("wo_hz_ask_governor_main_page"),

            doc("我要问局长-我的投诉列表页")
                    .biz(KbModule.WORK_ORDER)
                    .unit("wo_page")
                    .ref1(WOBusiness.HZ_ASK_GOVERNOR.name())
                    .ref2("wo_hz_ask_governor_my_case_list"),

            doc("我要问局长-投诉详情页")
                    .biz(KbModule.WORK_ORDER)
                    .unit("wo_case")
                    .ref1(WOBusiness.HZ_ASK_GOVERNOR.name())
                    .ref2("{WOCaseModel.id}")
                    .title("我要问局长案件详情页-{WOCaseModel.id}"),

            // ------------------------------------------------------------------------------

            doc("开吧教育主页")
                    .biz(KbModule.EDUCATION)
                    .unit("hz_education_page")
                    .ref1("hz_education_main_page"),

            doc("918研学主页")
                    .biz(KbModule.EDUCATION)
                    .unit("hz_education_918_page")
                    .ref1("hz_education_918_study_tour"),

            doc("918星少年小主播")
                    .biz(KbModule.EDUCATION)
                    .unit("hz_education_918_page")
                    .ref1("hz_education_918_i_am_host"),

            doc("918星少年小主播-童心妙笔")
                    .biz(KbModule.EDUCATION)
                    .unit("hz_education_918_page")
                    .ref1("hz_education_918_i_am_host_txmb"),

            doc("918星少年小主播-晚安电台")
                    .biz(KbModule.EDUCATION)
                    .unit("hz_education_918_page")
                    .ref1("hz_education_918_i_am_host_wadt"),

            doc("918星少年小主播-童观世界")
                    .biz(KbModule.EDUCATION)
                    .unit("hz_education_918_page")
                    .ref1("hz_education_918_i_am_host_tgsj"),

            doc("918星少年小主播-小主播列表")
                    .biz(KbModule.EDUCATION)
                    .unit("hz_education_918_page")
                    .ref1("hz_education_918_i_am_host_kid_list"),

            doc("918星少年小主播-小主播详情")
                    .biz(KbModule.EDUCATION)
                    .unit("hz_education_918_i_am_host_kid")
                    .ref1("{kidModel.id}")
                    .title("918星少年小主播-{kidModel.realName}"),

            doc("89朗诵团主页")
                    .biz(KbModule.EDUCATION)
                    .unit("hz_education_89_recitation_page")
                    .ref1("hz_education_89_recitation_main_page"),

            doc("89朗诵团-成员列表页")
                    .biz(KbModule.EDUCATION)
                    .unit("hz_education_89_recitation_page")
                    .ref1("hz_education_89_recitation_member_list_page"),

            doc("89朗诵团-成员详情页")
                    .desc("朗诵团成员 VO 定义在 core 节点")
                    .biz(KbModule.EDUCATION)
                    .unit("hz_education_89_recitation_member")
                    .ref1("{MemberModel.memberCode}")
                    .title("89朗诵团成员-{MemberModel.name}"),

            doc("89朗诵团-专家详情页")
                    .desc("朗诵团专家 VO 定义在 core 节点")
                    .biz(KbModule.EDUCATION)
                    .unit("hz_education_89_recitation_member")
                    .ref1("{ExpertModel.expertCode}")
                    .title("89朗诵团专家-{ExpertModel.name}"),

            // ------------------------------------------------------------------------------
            // 活动

            doc("活动页面")
                    .desc("活动页面中, biz 和 ref1 按照固定规则设置, 其余字段均可按实际情况赋值.")
                    .biz(KbModule.ACTIVITY_HUB)
                    .ref1("{KbActivityModel.key}")
                    .ref2("{pageKey}")
                    .title("活动页-{KbActivityModel.name}-{pageName}"),

            // ------------------------------------------------------------------------------
            // 组件-商品详情页

            doc("商品详情页")
                    .desc("商品详情 VO 定义在 mall 节点")
                    .biz(KbModule.MALL)
                    .unit("commodity")
                    .ref1("{CommodityVO.commodityCode}")
                    .title("商品详情-{CommodityVO.title}"),

            doc("剧院售票详情页")
                    .desc("剧院商品 VO 定义在 mall 节点")
                    .biz(KbModule.MALL)
                    .unit("ticket")
                    .ref1("{CommodityVO.commodityCode}")
                    .title("剧院售票详情-{CommodityVO.title}"),

            // ------------------------------------------------------------------------------
            // 组件-小贴士

            doc("小贴士-文章详情页")
                    .biz(KbModule.TIPS)
                    .unit("tips_article")
                    .ref1("{TipsArticleModel.id}")
                    .title("小贴士文章详情-{TipsArticleModel.title}"),

            // ------------------------------------------------------------------------------
            // 零散单页

            doc("开吧下载页")
                    .biz(KbModule.SINGLE_PAGE)
                    .ref1("kaiba_download")
                    .ref2("{渠道标识}"), // 渠道标识不存在则固定填 none .

            doc("限行地图")
                    .biz(KbModule.SINGLE_PAGE)
                    .ref1("traffic_restriction_map"),

            doc("找车位")
                    .biz(KbModule.SINGLE_PAGE)
                    .ref1("parking_map"),

            doc("in杭州主页")
                    .biz(KbModule.SINGLE_PAGE)
                    .ref1("in_hangzhou"),

            doc("看见杭州主页")
                    .biz(KbModule.SINGLE_PAGE)
                    .ref1("hangzhou_tv_plaza"),

            doc("路况")
                    .biz(KbModule.SINGLE_PAGE)
                    .ref1("road_condition")

            // ------------------------------------------------------------------------------

    ));

    private static DocDefine doc(String name) {
        return new DocDefine().name(name).title(name);
    }

    public static void main(String[] args){
        System.out.println(toMarkdown());
    }

    /**
     * 将 DOC_LIST 转换为 markdown 格式的字符串
     * @return markdown 格式的文档字符串
     */
    public static String toMarkdown() {
        StringBuilder sb = new StringBuilder();

        // 添加标题
        sb.append("# 埋点文档定义\n\n");
        sb.append("本文档包含所有埋点的定义信息。\n\n");

        // 添加目录
        sb.append("## 目录\n\n");
        String currentCategory = "";
        for (DocDefine doc : DOC_LIST) {
            String category = extractCategoryFromName(doc.getName());
            if (!category.equals(currentCategory)) {
                currentCategory = category;
                sb.append("- [").append(category).append("](#").append(category.toLowerCase().replace(" ", "-")).append(")\n");
            }
        }
        sb.append("\n");

        // 添加详细内容
        currentCategory = "";
        for (DocDefine doc : DOC_LIST) {
            String category = extractCategoryFromName(doc.getName());
            if (!category.equals(currentCategory)) {
                currentCategory = category;
                sb.append("## ").append(category).append("\n\n");
            }

            // 添加每个文档定义
            sb.append("### ").append(doc.getName()).append("\n\n");

            // 基本信息表格 - 三列格式：字段、值、描述
            sb.append("| 字段 | 值 | 描述 |\n");
            sb.append("|------|----|---------|\n");

            // 描述字段（可选显示）
            if (doc.getDesc() != null && !doc.getDesc().isEmpty()) {
                sb.append("| desc | ").append(escapeMarkdown(doc.getDesc())).append(" | - |\n");
            }

            // 固定显示的字段（即使为空也显示）
            sb.append("| biz | ");
            if (doc.getBiz() != null && !doc.getBiz().isEmpty()) {
                sb.append("`").append(doc.getBiz()).append("`");
            } else {
                sb.append("-");
            }
            sb.append(" | ");
            if (doc.getBizDesc() != null && !doc.getBizDesc().isEmpty()) {
                sb.append(escapeMarkdown(doc.getBizDesc()));
            } else {
                sb.append("-");
            }
            sb.append(" |\n");

            sb.append("| unit | ");
            if (doc.getUnit() != null && !doc.getUnit().isEmpty()) {
                sb.append("`").append(doc.getUnit()).append("`");
            } else {
                sb.append("-");
            }
            sb.append(" | ");
            if (doc.getUnitDesc() != null && !doc.getUnitDesc().isEmpty()) {
                sb.append(escapeMarkdown(doc.getUnitDesc()));
            } else {
                sb.append("-");
            }
            sb.append(" |\n");

            sb.append("| ref1 | ");
            if (doc.getRef1() != null && !doc.getRef1().isEmpty()) {
                sb.append("`").append(escapeMarkdown(doc.getRef1())).append("`");
            } else {
                sb.append("-");
            }
            sb.append(" | ");
            if (doc.getRef1Desc() != null && !doc.getRef1Desc().isEmpty()) {
                sb.append(escapeMarkdown(doc.getRef1Desc()));
            } else {
                sb.append("-");
            }
            sb.append(" |\n");

            sb.append("| ref2 | ");
            if (doc.getRef2() != null && !doc.getRef2().isEmpty()) {
                sb.append("`").append(escapeMarkdown(doc.getRef2())).append("`");
            } else {
                sb.append("-");
            }
            sb.append(" | ");
            if (doc.getRef2Desc() != null && !doc.getRef2Desc().isEmpty()) {
                sb.append(escapeMarkdown(doc.getRef2Desc()));
            } else {
                sb.append("-");
            }
            sb.append(" |\n");

            sb.append("| ref3 | ");
            if (doc.getRef3() != null && !doc.getRef3().isEmpty()) {
                sb.append("`").append(escapeMarkdown(doc.getRef3())).append("`");
            } else {
                sb.append("-");
            }
            sb.append(" | ");
            if (doc.getRef3Desc() != null && !doc.getRef3Desc().isEmpty()) {
                sb.append(escapeMarkdown(doc.getRef3Desc()));
            } else {
                sb.append("-");
            }
            sb.append(" |\n");

            sb.append("| siteId | ");
            if (doc.getSiteId() != null && !doc.getSiteId().isEmpty()) {
                sb.append("`").append(escapeMarkdown(doc.getSiteId())).append("`");
            } else {
                sb.append("-");
            }
            sb.append(" | ");
            if (doc.getSiteIdDesc() != null && !doc.getSiteIdDesc().isEmpty()) {
                sb.append(escapeMarkdown(doc.getSiteIdDesc()));
            } else {
                sb.append("-");
            }
            sb.append(" |\n");

            // 可选字段
            if (doc.getTitle() != null && !doc.getTitle().isEmpty()) {
                sb.append("| title | `").append(escapeMarkdown(doc.getTitle())).append("` | ");
                if (doc.getTitleDesc() != null && !doc.getTitleDesc().isEmpty()) {
                    sb.append(escapeMarkdown(doc.getTitleDesc()));
                } else {
                    sb.append("-");
                }
                sb.append(" |\n");
            }

            if (doc.getCategory() != null && !doc.getCategory().isEmpty()) {
                sb.append("| category | ").append(String.join(", ", doc.getCategory())).append(" | - |\n");
            }

            sb.append("\n");
        }

        return sb.toString();
    }

    /**
     * 从名称中提取分类信息
     */
    private static String extractCategoryFromName(String name) {
        if (name == null || name.isEmpty()) {
            return "其他";
        }

        // 根据名称前缀提取分类
        if (name.startsWith("首页")) {
            return "首页";
        } else if (name.startsWith("文章") || name.startsWith("资讯") || name.startsWith("栏目") || name.startsWith("频道")) {
            return "文章系统";
        } else if (name.startsWith("老资讯") || name.startsWith("热门专题")) {
            return "老资讯";
        } else if (name.startsWith("节目")) {
            return "节目互动";
        } else if (name.startsWith("音频回播")) {
            return "节目音频回播";
        } else if (name.startsWith("爆料台")) {
            return "爆料台";
        } else if (name.startsWith("剧好看")) {
            return "剧好看/短剧";
        } else if (name.startsWith("视频直播")) {
            return "视频直播";
        } else if (name.startsWith("听见杭州")) {
            return "集团广播频率播放";
        } else if (name.startsWith("圈子")) {
            return "圈子";
        } else if (name.startsWith("活动瀑布流")) {
            return "活动瀑布流";
        } else if (name.startsWith("汽车维权") || name.startsWith("我要问局长")) {
            return "工单系统";
        } else if (name.startsWith("开吧教育") || name.startsWith("918") || name.startsWith("89朗诵团")) {
            return "教育";
        } else if (name.startsWith("活动页面")) {
            return "活动";
        } else if (name.startsWith("商品") || name.startsWith("剧院售票")) {
            return "组件-商品详情页";
        } else if (name.startsWith("小贴士")) {
            return "组件-小贴士";
        } else {
            return "零散单页";
        }
    }

    /**
     * 转义 markdown 特殊字符
     */
    private static String escapeMarkdown(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("|", "\\|")
                  .replace("\n", "<br>")
                  .replace("\r", "");
    }

}
