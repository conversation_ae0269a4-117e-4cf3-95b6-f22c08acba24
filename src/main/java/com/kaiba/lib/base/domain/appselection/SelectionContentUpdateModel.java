package com.kaiba.lib.base.domain.appselection;


import com.kaiba.lib.base.constant.apphome.AppHomeSelectionStyle;
import com.kaiba.lib.base.domain.IActionGetterSetter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Map;

/**
 * author: lyux
 * date: 2023-07-18
 *
 * 推荐备选内容池.
 */
@Data
@ToString
@NoArgsConstructor
public class SelectionContentUpdateModel implements IActionGetterSetter {

    private String id;

    /** 标题 */
    private String title;

    /** 副标题, 主要用于推送副标题 */
    private String subTitle;

    /** 内容提示, 用于前端展示. 如 "11月12日 20:56 资讯"; "火热进行中". */
    private String hint;

    /** 大图/一图/二图 模式封面. 宽高比固定为 3:2 */
    private SelectionCover image1;

    /** 二图 模式封面. 宽高比固定为 3:2 */
    private SelectionCover image2;

    /** 腰封模式封面. {@link AppHomeSelectionStyle#BELT}. 宽高比固定为 6.98:1 */
    private SelectionCover imageBelt;

    /** 跳转页面. */
    private String action;

    /** 跳转页面参数. */
    private Map<String, Object> actionParams;

}
