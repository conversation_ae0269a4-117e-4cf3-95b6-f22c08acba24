package com.kaiba.lib.base.domain.mall.commodity;

import lombok.Data;

/**
 * Description: 聚合Sku修改VO
 * Author: ZM227
 * Date: 2024/10/16 10:06
 */
@Data
public class AggregateSkuModifyVO {

    /**
     * 商品code
     */
    private String commodityCode;

    /**
     * 售卖开始时间
     */
    private Long saleStartTime;

    /**
     * 售卖结束时间
     */
    private Long saleEndTime;

    /**
     * 场次开始时间
     */
    private Long startTime;

    /**
     * 场次结束时间
     */
    private Long endTime;

    /**
     * 剧院名称
     */
    private String theaterName;

    /**
     * 状态
     * (0, "无效")
     * (1, "有效")
     */
    private Integer status;

    /**
     * 聚合Id
     */
    private String aggregationCode;

}
