package com.kaiba.lib.base.domain.reveal;

import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.ShareModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version RevealScheduleModel, v0.1 2025/4/10 10:37 daopei Exp $
 **/
@Data
public class RevealScheduleModel {
    /**
     * 主键ID
     **/
    private String id;

    /**
     * 冗余字段-归属电台ID
     **/
    private Integer siteId;
    /**
     * 数据状态 1:上线 0:下线
     */
    private Integer status;

    /**
     * 业务状态
     */
    private Integer buStatus;

    /**
     * 关联爆料台节目ID
     **/
    private String revealProgramId;

    /**
     * 关联帖子系统中的板块ID-noteThreadId
     * 用户发帖使用板块
     **/
    private String threadId;

    /**
     * 浏览量
     **/
    private Long viewCount;

    /**
     * 转发量
     **/
    private Long shareCount;

    /**
     * 标题
     **/
    private String title;

    /**
     * 封面图
     */
    private Image cover;

    /**
     * 简要描述
     **/
    private String description;

    /**
     * 期数，yyyyMMdd
     **/
    private String periodical;

    /**
     * 修改时间戳 毫秒
     **/
    private Long updateTime;

    /**
     * 创建时间戳 毫秒
     **/
    private Long createTime;

    /**
     * 创建人ID
     **/
    private Integer creatorId;

    /**
     * 修改人ID
     **/
    private Integer modifierId;
    /**
     * 友台频道编码 -冗余
     **/
    private String friendChannelCode;
    /**
     * 投票ID
     **/
    private String voteId;

    /** 分享设置,允许为空,有默认规则 */
    private ShareModel shareModel;
}
