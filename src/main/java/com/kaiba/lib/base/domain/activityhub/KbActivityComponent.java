package com.kaiba.lib.base.domain.activityhub;

import com.kaiba.lib.base.constant.KbComponent;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2023-11-22
 *
 * 活动登记表, 活动组件信息. {@link KbComponent}
 */
@Data
@ToString
@NoArgsConstructor
public class KbActivityComponent {

    /** 组件类型. {@link KbComponent} */
    private String component;

    /** 组件标识 1. 可以是逻辑主键 (key), 也可以是物理主键 (id). */
    private String ref1;

    /** 组件标识 2. 可以是逻辑主键 (key), 也可以是物理主键 (id). */
    private String ref2;

    /** 组件标识 3. 可以是逻辑主键 (key), 也可以是物理主键 (id). */
    private String ref3;

    /** 组件用途说明. 会用在管理后台活动详情页的组件列表展示. */
    private String name;

}
