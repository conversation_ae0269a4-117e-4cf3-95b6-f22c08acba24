package com.kaiba.lib.base.domain.election;

import com.kaiba.lib.base.constant.election.VoteResultCode;
import com.kaiba.lib.base.rte.RelativeTimeExpression;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * author: lyux
 * date: 2022-09-21
 *
 * 将选票规则独立出来, 以便将多个投票实例组成一个组, 来实现大于 1000 人的选票活动.
 */
@Data
@NoArgsConstructor
public class TicketRuleModel {

    private String id;

    /** 用户初始可用选票数. 必填. */
    private Integer ticketInitial;

    /** 重置周期内用户向单一候选人可投的最大票数. 默认为 0, 代表无限制. */
    private Integer ticketMaxEach;

    /** 用户可用选票数重置时间规则, 相对时间表达式. {@link com.kaiba.lib.base.rte.RelativeTimeExpression} */
    private String ticketResetRTE;

    /** 用户周期内额外获票机会 (常用语分享加票等场景): 周期内可加多少次. 默认为 0. */
    private Integer incrTicketChance;

    /** 用户周期内额外获票机会 (常用语分享加票等场景): 周期内每次可加多少票. */
    private Integer incrTicketPerChance;

    /** 用户周期内额外获票机会 (常用语分享加票等场景): 周期重置规则. 相对时间表达式, {@link RelativeTimeExpression}. */
    private String incrTicketChanceRTE;

    /** 投票提示语, 对应 {@link VoteResultCode#SUCCESS} */
    private String msgSuccess;

    /** 投票提示语, 对应 {@link VoteResultCode#FAIL} */
    private String msgFail;

    /** 投票提示语, 对应 {@link VoteResultCode#ACS_SUSPECT} */
    private String msgACSSuspect;

    /** 投票提示语, 对应 {@link VoteResultCode#ACS_BLOCKED} */
    private String msgACSBlocked;

    /** 投票提示语, 对应 {@link VoteResultCode#NO_TICKET} */
    private String msgNoTicket;

    /** 投票提示语, 对应 {@link VoteResultCode#INVALID_TICKET} */
    private String msgInvalidTicket;

    /** 投票提示语, 对应 {@link VoteResultCode#EXCEED_CANDIDATE_LIMIT} */
    private String msgExceedCandidateLimit;

    /** 投票提示语, 对应 {@link VoteResultCode#CANDIDATE_NOT_FOUND}. 也可能是因为候选人状态为隐藏 */
    private String msgCandidateNotFound;

    /** 投票提示语, 对应 {@link VoteResultCode#CANDIDATE_CANNOT_VOTE} */
    private String msgCandidateCannotVote;

    /** 投票提示语, 对应 {@link VoteResultCode#ELECTION_INIT} */
    private String msgElectionInit;

    /** 投票提示语, 对应 {@link VoteResultCode#ELECTION_PREVIEW} */
    private String msgElectionPreview;

    /** 投票提示语, 对应 {@link VoteResultCode#ELECTION_SEALED} */
    private String msgElectionSealed;

    /** 投票提示语, 对应 {@link VoteResultCode#ELECTION_ARCHIVED} */
    private String msgElectionArchived;

    /** 描述. */
    private String description;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
