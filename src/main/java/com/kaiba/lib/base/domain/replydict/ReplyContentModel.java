package com.kaiba.lib.base.domain.replydict;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ReplyContentModel {
    private String id;

    private String groupId;

    private Integer siteId;

    private Integer createUserId;

    /** 标题 */
    private String title;

    /** 内容 */
    private String content;

    /** url */
    private String link;

    /** 封面 */
    private String cover;

    private Long createTime;

    private Long updateTime;

}
