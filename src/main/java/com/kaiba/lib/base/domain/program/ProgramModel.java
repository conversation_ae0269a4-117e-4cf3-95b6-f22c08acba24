package com.kaiba.lib.base.domain.program;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * author wangsj
 * date 2020-09-01
 *
 * 节目:
 * 代表一档节目, 比如 [我的汽车有话说].
 * 具体谋期的节目由排班实例表示: {@link ScheduleInstanceModel}.
 */
@Data
@NoArgsConstructor
public class ProgramModel {

    private String id;

    /** 电台 id */
    private Integer siteId;

    private Integer source;

    /** 节目名称 */
    private String name;

    /** 节目介绍. 新版中未使用该字段. */
    private String introduce;

    /** 节目封面 */
    private String coverImg;

    /** 是否为保留节目 */
    private Integer reserved;

    /** 关联的节目回播模块的专辑 id. {@link com.kaiba.lib.base.domain.playback.PlaybackAlbumModel} */
    private String albumId;

    /** 该节目总的帖子板块 id. 所有该节目下的排班实例发布的帖子都会同时发布到该板块. */
    private String threadId;

    /** 帖子显示策略. {@link com.kaiba.lib.base.constant.program.ProgramThreadType} */
    private Integer threadLevel;

    /** 创建者 */
    private Integer creator;

    /** 创建时间 */
    private Long createTime;

    /** 修改时间 */
    private Long modifyTime;

    /** 修改者 */
    private Integer modifyUserId;

}
