package com.kaiba.lib.base.domain.tecent;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 此处并没有解析所有的返回数据
 * 具体参照 https://wiki.open.qq.com/wiki/%E3%80%90QQ%E7%99%BB%E5%BD%95%E3%80%91get_user_info
 * **/

@Data
@NoArgsConstructor
@ToString
public class QQUserInfo {
    private Integer ret;

    private String msg;

    private String nickname;

    /** 40*40 **/
    private String figureurl_qq_1;

    private String gender;
}
