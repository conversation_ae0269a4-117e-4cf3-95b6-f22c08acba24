package com.kaiba.lib.base.domain.mall.commodity;

import java.util.List;
import lombok.Data;

/**
 * Description: 大剧院场次VO
 * Author: ZM227
 * Date: 2024/6/6 15:46
 */
@Data
public class TheaterPerformVO {

    /**
     * 场次id
     */
    private String performId;

    /**
     * 场次名称
     */
    private String performName;

    /**
     * 演出id
     */
    private String projectId;

    /**
     * 剧院名称
     */
    private String theaterName;

    /**
     * 状态
     * (0, "无效")
     * (1, "有效")
     */
    private Integer status;

    /**
     * 场次开始时间
     */
    private Long startTime;

    /**
     * 场次结束时间
     */
    private Long endTime;

    /**
     * 售卖开始时间
     */
    private Long saleStartTime;

    /**
     * 售卖结束时间
     */
    private Long saleEndTime;

    /**
     * 场次介绍
     */
    private String describe;

    /**
     * 是否可以选座
     * (0, "不可选座")
     * (1, "可以选座")
     * (2, "无座位")
     */
    private Integer chooseSeatFlag;

    /**
     * 实名制规则
     * (0, "非实名制")
     * (1, "一单一证")
     * (2, "一票一证")
     */
    private Integer ruleType;

    /**
     * 支持证件类型:ruleType!=0时必填
     * (1, "身份证")
     * (2, "护照")
     * (3, "港澳通行证")
     * (4, "台胞证")
     * (5, "军人证")
     * (6, "港澳台居民居住证")
     * (7, "外国人永久居留身份证")
     */
    private List<Integer> certType;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 修改时间
     */
    private Long gmtModified;

}
