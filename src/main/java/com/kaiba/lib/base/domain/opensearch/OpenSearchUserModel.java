package com.kaiba.lib.base.domain.opensearch;

import java.util.regex.Pattern;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 开放搜索引擎实体表-用户关联表
 * <AUTHOR>
 * @date 2022/06/16 18:43
 **/
@Data
@ToString
@NoArgsConstructor
public class OpenSearchUserModel implements ISearchModel {

    private static final Pattern PATTERN = Pattern.compile("[0-9]*");

    /** 用户id */
    private Integer id;
    /** 用户角色 */
    private Integer role;
    /** 用户名称 */
    private String user_name;
    /** 用户头像 */
    private String avatar;
    /** 用户签名 */
    private String signature;

    @Override
    public void setId(String id) {
        if(PATTERN.matcher(id).matches()){
            this.id = Integer.parseInt(id);
        }
    }
}
