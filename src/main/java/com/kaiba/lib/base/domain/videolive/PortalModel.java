package com.kaiba.lib.base.domain.videolive;

import com.kaiba.lib.base.domain.user.UserModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Map;

/**
 * author: lyux
 * date: 2020-05-19
 */
@Data
@ToString
@NoArgsConstructor
public class PortalModel {

    private String id;

    private String title;

    /** 所关联的视频直播 id */
    private String liveId;

    /** 图标 url */
    private String image;

    private Integer state;

    /** 指定消息点击时的跳转页面 */
    private String action;

    /** 指定消息点击时的跳转页面, 参数 */
    private Map<String, Object> actionParams;

    /** 预约开始时间. 单位秒. */
    private Long scheduledStartTime;

    /** 预约结束时间. 单位秒. */
    private Long scheduledEndTime;

    /** 持续时长. 单位秒. */
    private Long duration;

    private UserModel creator;

    /** 消息创建时间 */
    private Long createTime;

    /** 链接点击次数 */
    private Long clickCount;

}
