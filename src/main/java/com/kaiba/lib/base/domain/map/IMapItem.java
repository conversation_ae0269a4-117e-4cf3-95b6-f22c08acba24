package com.kaiba.lib.base.domain.map;

/**
 * author: lyux
 * date: 2022-03-21
 */
public interface IMapItem {

    default MapMarker getMapMarker() {
        throw new IllegalStateException("marker data not supported");
    }

    default MapLine getMapLine() {
        throw new IllegalStateException("line data not supported");
    }

    default MapArea getMapArea() {
        throw new IllegalStateException("area data not supported");
    }

}
