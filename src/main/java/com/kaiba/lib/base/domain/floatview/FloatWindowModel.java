package com.kaiba.lib.base.domain.floatview;

import com.kaiba.lib.base.constant.floatview.FloatViewState;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 19-12-25
 */
@Data
@ToString
@NoArgsConstructor
public class FloatWindowModel {

    public static final String DEFAULT_BACKGROUND_COLOR = "40000000";
    public static final int DEFAULT_WIDTH_SCREEN_RATIO = 75;
    public static final int DEFAULT_WIDTH_HEIGHT_RATIO = 150;


    private String id;

    private Integer siteId;

    /** 页面标识 */
    private String mark;

    /** 状态. {@link FloatViewState} */
    private Integer state;

    /** 创建者的用户 id */
    private Integer creatorId;

    /** 背景颜色, 格式为16进制颜色, aRGB. 例: 809f9f9f  */
    private String backgroundColor;

    /** 浮窗宽度与屏幕宽度的百分比值. 范围 [0, 100]. */
    private Integer widthScreenRatio;

    /** 浮窗自身的宽高比. 范围为正整数. */
    private Integer widthHeightRatio;

    /** 用户未登录时是否显示. 默认为 false. */
    private Boolean onlyForLogin;

    /** 是否使用默认参数作为 query string 传递给 link. 默认为 true. */
    private Boolean decorateLink;

    /** 用以渲染浮窗的 h5 的地址 */
    private String link;

    /** 描述 */
    private String description;

    /** 预约的浮标开始显示的时间, 单位秒 */
    private Long scheduledStartTime;

    /** 预约的浮标消失的时间, 单位秒 */
    private Long scheduledEndTime;

    /** 开始时间 */
    private Long startTime;

    /** 结束时间 */
    private Long endTime;

    /** 状态更新时间 */
    private Long updateTime;

    /** 创建时间 */
    private Long createTime;

}
