package com.kaiba.lib.base.domain.program;

import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.playback.PlaybackAlbumModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;

/**
 * author wangsj
 * date 2020-09-01
 *
 * 排班实例:
 * 具体的某天的某个时间段的节目排班. 比如 2020-01-01 中午 12:00-1:00 的 [我的汽车有话说].
 */
@Data
@NoArgsConstructor
@ToString
public class ScheduleInstanceModel {

    private String id;

    /** 电台 id */
    private Integer siteId;

    /** 节目. {@link ProgramModel} */
    private String programId;

    /** 节目名称 */
    private String programName;

    /** 节目封面 */
    private String programCover;

    /** 客户端发布新贴时使用的板块 id */
    private String threadId;

    /** 客户端展示帖子列表的板块 id */
    private String contentThreadId;

    /** 直播管理后台用, 待播帖子板块 id */
    private String boardThreadId;

    /** 直播管理后台用, 已播帖子板块 id */
    private String broadcastThreadId;

    /** 直播管理后台用, 打赏帖子板块 id */
    private String rewardThreadId;

    /** 关联的节目回播模块的专辑 id. {@link PlaybackAlbumModel} */
    private String albumId;

    /** 开始时间, 时间戳. 单位秒. */
    private Integer startTime;

    /** 结束时间, 时间戳. 单位秒. */
    private Integer endTime;

    /** 更新时间, 时间戳. 单位秒. */
    private Long updateTime;

    /** 是否是重播 */
    private Boolean rebroadcast;

    /** 是否为空白档 */
    private Boolean isEmpty;

    //在节目直播中目前和 schedule_instance_id 相同
    //个人目前没有感觉到此字段存在的具体意义
    private String streamId;

    /** 主持人列表 */
    private List<EmceeModel> emcee;

    /** 当前话题 */
    private TopicModel topic;

    /** 分享设置 */
    private ShareModel shareModel;

    /** 是否直播的标记 */
    private Integer isLive;

    /** 客户端展示用, 在 APP 首页展示动态用户评论数据. */
    private List<SimpleNoteModel> comments;

    /** 客户端展示用, 在 APP 首页展示的 "直播", "已播" 等标签. */
    private String label;

    // ---------------------------------------------------

    private Integer praise;
    private Integer view;

    // -----------------------------------------------------

    private static final DateTimeFormatter TITLE_TIME_FORMATTER = DateTimeFormatter
            .ofPattern("yyyy-MM-dd")
            .withLocale(Locale.CHINA)
            .withZone(ZoneId.systemDefault());

    public static String generateScheduleName(Integer startTime, String programName) {
        if (startTime == null || programName == null) {
            return null;
        } else {
            return TITLE_TIME_FORMATTER.format(Instant.ofEpochSecond(startTime)) + " " + programName;
        }
    }

}
