package com.kaiba.lib.base.domain.applego.widget.view;

import com.kaiba.lib.base.constant.common.KbAlign;
import com.kaiba.lib.base.constant.common.KbTextStyle;
import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.lib.base.domain.applego.IWidget;
import com.kaiba.lib.base.domain.applego.lego.LegoBlockModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2023-05-24
 */
@Data
@ToString
@NoArgsConstructor
public class WidgetText implements IWidget {

    /** 主键 */
    private String id;

    /** 文字内容 */
    private String text;

    /** 文字颜色. 格式举例: #fff0f0f0 . */
    private String textColor;

    /** {@link KbTextStyle} */
    private String textStyle;

    /** 文字大小, 单位 pt */
    private Integer textSize;

    /** 文字位置 {@link KbAlign} */
    private Integer textAlign;

    /** 最大行数 */
    private Integer textMaxLine;

    /** {@link LegoBlockModel#getId()} */
    private String blockId;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    @Override
    public WidgetType getType() {
        return WidgetType.TEXT;
    }
}
