package com.kaiba.lib.base.domain.note;

import com.kaiba.lib.base.domain.user.UserModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

@Data
@NoArgsConstructor
public class NotePraiseModel {

    /** 主键 */
    @Id
    private String id;

    /** 用户 id */
    private Integer userId;

    /** 帖子 id */
    private String noteId;

    /** 创建时间, 单位毫秒 */
    private Long createTimeMS;

    // -------------------------------------------------

    /** 点赞者. */
    private UserModel user;

}
