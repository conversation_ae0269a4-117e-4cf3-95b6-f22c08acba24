package com.kaiba.lib.base.domain.floatview;

import com.kaiba.lib.base.constant.floatview.FloatViewState;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Map;

/**
 * author: lyux
 * date: 19-12-25
 */
@Data
@ToString
@NoArgsConstructor
public class FloatIconModel {

    private String id;

    private Integer siteId;

    /** 页面标识 */
    private String mark;

    /** 状态. {@link FloatViewState} */
    private Integer state;

    /** 创建者的用户 id */
    private Integer creatorId;

    /** 图标 url */
    private String icon;

    /** 浮标初始显示位置. {@link com.kaiba.lib.base.constant.floatview.FloatIconPosition} */
    private String position;

    /** 浮标大小与屏幕宽度的百分比值. 可取范围 [1, 100] */
    private Integer sizeRatio;

    /** 是否支持拖动 */
    private Boolean enableDrag;

    /** 用户未登录时是否显示. 默认为 false. */
    private Boolean onlyForLogin;

    /** action */
    private String action;

    /** actionParams */
    private Map<String, Object> actionParams;

    /** 描述 */
    private String description;

    /** 预约的浮标开始显示时间, 单位秒 */
    private Long scheduledStartTime;

    /** 预约的浮标消失时间, 单位秒 */
    private Long scheduledEndTime;

    /** 开始时间 */
    private Long startTime;

    /** 结束时间 */
    private Long endTime;

    /** 状态更新时间 */
    private Long updateTime;

    /** 创建时间 */
    private Long createTime;

}
