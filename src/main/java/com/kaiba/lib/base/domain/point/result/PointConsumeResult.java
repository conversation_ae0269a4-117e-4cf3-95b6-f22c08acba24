package com.kaiba.lib.base.domain.point.result;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2022-12-28
 *
 * 用户获取积分动作的返回数据.
 */
@Data
@ToString
@NoArgsConstructor
public class PointConsumeResult {

    public static final int CODE_OK = 1;
    public static final int CODE_FAIL = 2;
    public static final int CODE_BATCH_ON_GOING = 3;
    public static final int CODE_ALREADY_PAYED = 11;
    public static final int CODE_POINT_INSUFFICIENT = 12;
    public static final int CODE_POINT_EXCEED_DIRECT_PAY_LIMIT = 13;
    public static final int CODE_DEPOSIT_NOT_EXISTS = 21;
    public static final int CODE_DEPOSIT_SEALED = 22;

    /** 结果编码 */
    private Integer code;

    /** 提示信息 */
    private String msg;

    /** 积分实例 */
    private Long instanceId;

    /** 积分渠道 */
    private Long channelId;

    /** 直接扣减积分, 此为积分明细 id */
    private Long entryId;

    /** 若暂扣至积分订单表, 此为订单 id */
    private Long depositId;

    /** 用户 */
    private Integer userId;

    /** 消费的积分值 */
    private Integer point;

    public PointConsumeResult(Long instanceId, Long channelId, Integer userId) {
        this.instanceId = instanceId;
        this.channelId = channelId;
        this.userId = userId;
    }

    public boolean isOK() {
        return code == CODE_OK;
    }

    public PointConsumeResult setCodeWithMsg(int code) {
        this.code = code;
        this.msg = code2msg(code);
        return this;
    }

    public static String code2msg(int code) {
        switch (code) {
            case CODE_OK:
                return "积分消费成功";
            case CODE_BATCH_ON_GOING:
                return "核算进行中, 请稍后再试";
            case CODE_ALREADY_PAYED:
                return "积分订单已生成";
            case CODE_POINT_INSUFFICIENT:
                return "积分不足抵扣";
            case CODE_POINT_EXCEED_DIRECT_PAY_LIMIT:
                return "扣减额度超过阈值, 请使用积分订单完成扣减";
            case CODE_DEPOSIT_NOT_EXISTS:
                return "积分订单不存在";
            case CODE_DEPOSIT_SEALED:
                return "积分订单已关闭";
            case CODE_FAIL:
            default:
                return "积分消费失败";
        }
    }

}
