package com.kaiba.lib.base.domain.dynamic;

import com.kaiba.lib.base.constant.dynamic.DynamicChannelType;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2019/3/14
 */
@Data
@NoArgsConstructor
@ToString
public class DynamicDropEventModel {
    private String referenceId;

    /**
     * {@link DynamicChannelType}
     */
    private Integer channelType;

    private Integer siteId;

    private Integer source;

    private transient DynamicChannelType dynamicChannelType;

    public DynamicChannelType getDynamicChannelType() {
        if (dynamicChannelType == null) {
            dynamicChannelType = DynamicChannelType
                    .valueOf(channelType)
                    .orElseThrow(KbException.supplier(KbCode.ILLEGAL_ARGUMENT));
        }
        return dynamicChannelType;
    }
}
