package com.kaiba.lib.base.domain.prize;

import com.kaiba.lib.base.domain.user.UserModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2020-06-11
 */
@Data
@ToString
@NoArgsConstructor
public class PrizeWriteOffLogModel {

    private String id;

    /** 奖品 id */
    private String prizeId;

    /** 核销者 id */
    private Integer operatorId;

    /** 核销渠道 */
    private String channel;

    /** 创建时间 */
    private Long createTime;

    // --------------------------------------

    private PrizeModel prize;

    private UserModel operator;

}
