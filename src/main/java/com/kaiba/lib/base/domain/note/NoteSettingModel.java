package com.kaiba.lib.base.domain.note;

import com.kaiba.lib.base.constant.note.NoteThreadCondition;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2022-12-02
 *
 * 帖子系统全局设置.
 */
@Data
@ToString
@NoArgsConstructor
public class NoteSettingModel {

    private String id;

    private Integer siteId;

    /**
     * 全局板块状态. 参考 {@link NoteThreadModel#getCondition()}, {@link NoteThreadCondition}.
     * 此开关控制整个平台的板块评论状态开关. 会和各板块自身设置进行比较, 以从严原则决定最终评论状态.
     * 因此建议默认设置为 {@link NoteThreadCondition#FREE} 即可, 突发事件时设置为 {@link NoteThreadCondition#REVIEW} 等.
     */
    private Integer condition;

    /** 更新时间, 单位秒 */
    private Long updateTime;

}
