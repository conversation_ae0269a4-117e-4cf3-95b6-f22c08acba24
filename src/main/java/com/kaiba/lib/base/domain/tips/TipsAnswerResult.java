package com.kaiba.lib.base.domain.tips;

import com.kaiba.lib.base.constant.tips.TipsAnswerResultCode;
import com.kaiba.lib.base.domain.IAttrGetterSetter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.beans.Transient;
import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 2023-02-10
 */
@Data
@ToString
@NoArgsConstructor
public class TipsAnswerResult implements IAttrGetterSetter {

    /** 结果代码. {@link TipsAnswerResultCode} */
    private Integer code;

    /** 结果消息 */
    private String msg;

    /** {@link TipsAnswerModel} */
    private String answerId;

    /** {@link TipsArticleModel} */
    private String articleId;

    /** {@link TipsOptionModel} */
    private String optionId;

    /** 用户 */
    private Integer userId;

    /** 是否正确 */
    private Boolean correct;

    /** 时间, 单位毫秒. */
    private Long time;

    /** 答案的统计数据 */
    private List<TipsOptionStat> stats;

    /** 额外信息 */
    private Map<String, String> extra;

    // -----------------------------------------

    public TipsAnswerResult(TipsAnswerResultCode resultCode) {
        this.code = resultCode.getValue();
        this.msg = resultCode.getDescription();
    }

    @Transient
    @Override
    public Map<String, String> getAttr() {
        return extra;
    }

    @Transient
    @Override
    public void setAttr(Map<String, String> attr) {
        this.extra = attr;
    }
}
