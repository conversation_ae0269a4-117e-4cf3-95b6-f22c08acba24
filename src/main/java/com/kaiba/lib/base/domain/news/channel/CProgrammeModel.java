package com.kaiba.lib.base.domain.news.channel;

import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.news.article.NDisplayState;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.DisplayConfig;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupModel;
import com.kaiba.lib.base.domain.news.pool.topic.TopicLayoutModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2024-08-28
 *
 * 频率频道的一档栏目. 比如 "阿六头说新闻", "小区大事".
 */
@Data
@ToString
@NoArgsConstructor
public class CProgrammeModel {

    private String id;

    /** 逻辑主键 */
    private String key;

    /** 频道标识. {@link NewsChannel} */
    private String channelKey;

    /** 状态. {@link NDisplayState} */
    private String state;

    /** 栏目关联的单期分组. {@link GroupModel#getKey()} */
    private String group;

    /** 栏目名称 */
    private String name;

    /** 栏目简称 */
    private String abbr;

    /** 栏目封面 */
    private Image cover;

    /** 顶部布局. {@link TopicLayoutModel#getId()} */
    private String layoutId;

    /** 分享配置 */
    private ShareModel share;

    /** 查询时以此字段降序排列. */
    private Long seq;

    /**
     * 人工指定的排序依据, 降序, 最大值 999.
     * 最终会根据算法生成 {@link #seq}. 因此本字段仅做记录, 实际排序应使用 {@link #seq} 字段
     */
    private Long idx;

    /** 默认的每日播出开始时间, 单位毫秒, 取值范围 [0, 86_400_000]. 用来在添加单期视频时预填节目在当日的播出时间. */
    private Long startTimeInDay;

    /** 默认的每日播出结束时间, 单位毫秒, 取值范围 [0, 86_400_000]. 用来在添加单期视频时预填节目在当日的播出时间. */
    private Long endTimeInDay;

    /** 默认的单期和拆条分组的递补配置. {@link GroupModel#getDcOnAbsent()}. */
    private DisplayConfig groupAbsentDc;

    /**
     * 葫芦网cid
     * 目前用户葫芦网资讯同步
     */
    private Integer outerId;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

    // ------------------------------------------

    /** 顶部布局数据 */
    private TopicLayoutModel layout;

}
