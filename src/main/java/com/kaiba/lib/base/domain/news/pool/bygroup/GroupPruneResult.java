package com.kaiba.lib.base.domain.news.pool.bygroup;

import lombok.Data;
import lombok.ToString;
import org.springframework.data.annotation.Transient;

/**
 * author: lyux
 * date: 2024-10-12
 */
@Data
@ToString
public class GroupPruneResult {

    @Transient
    public transient static final int CODE_NOT_FOUND = 1;

    @Transient
    public transient static final int CODE_OFFLINE = 2;

    @Transient
    public transient static final int CODE_MISSING_ID = 3;


    /** 关联文章 id 缺失的数量 */
    private Integer missingIdCount;

    /** 所关联的文章不存在的数量. */
    private Integer notFoundCount;

    /** 所关联的文章非上线状态的数量. */
    private Integer offlineCount;


    public GroupPruneResult() {
        this.missingIdCount = 0;
        this.notFoundCount = 0;
        this.offlineCount = 0;
    }

}
