package com.kaiba.lib.base.domain.publicservice;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/1/12 10:34
 **/
@Data
@ToString
@NoArgsConstructor
public class PublicServiceGroupItemModel {

    private String id;

    /** 分组id */
    private String groupId;

    /** 服务id */
    private String itemId;

    /** 服务在分组内的排序 默认9999 手动后台调整 */
    private Long order;

    /** 创建时间 ms */
    private Long createTime;

    /** 更新时间 ms */
    private Long updateTime;
}
