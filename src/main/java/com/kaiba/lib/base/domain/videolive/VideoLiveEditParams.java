package com.kaiba.lib.base.domain.videolive;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/12/20 14:09
 **/
@Data
@ToString
public class VideoLiveEditParams {

    @NotBlank(message = "直播间id不能为空")
    private String videoliveId;

    @NotNull(message = "userId 不能为空")
    private Integer userId;

    @NotNull(message = "直播间主持人不能为空")
    @Size(min = 1, message = "直播间主持人不能为空")
    private Integer[] emcees;

    @NotBlank(message = "直播间标题不能为空")
    @Size(max = 50, message = "直播间标题不能超过50个字符")
    private String title;

    /** 预热封面 */
    private String warmUpCover;

    /** 直播封面 */
    @NotBlank(message = "直播封面不能为空")
    private String startedCover;

    /** 直播间背景图 */
    private String background;

    /** 预热视频 */
    private String warmUpVideo;

    /** 视频直播流地址,返回给前端直接使用,同时为管理平台设置拉流地址的字段 */
    private String videoUrl;

    @Size(max = 140, message = "直播间描述不能超过140个字符")
    private String description;

    /** 直播间直播时浮动弹窗描述 */
    private String floatText;

    /** 预设开始时间 */
    private Long presetStartTime;

    private Long startTime;

    /** 0竖屏 1横屏 默认竖屏 */
    @Min(value = 0, message = "横竖屏设值不正确")
    @Max(value = 1, message = "横竖屏设值不正确")
    private Integer liveStreamingOrientation = 0;

    /** 是否横屏播放 默认0 */
    @Min(value = 0, message = "是否横屏播放设值不正确")
    @Max(value = 1, message = "是否横屏播放设值不正确")
    private Integer landscape = 0;

    /** 是否隐藏弹幕区 0否1是 默认0 不隐藏 */
    @Min(value = 0, message = "隐藏弹幕区设值不正确")
    @Max(value = 1, message = "隐藏弹幕区设值不正确")
    private Integer hideComment = 0;

    /** 是否隐藏点赞按钮 0否1是 默认0 不隐藏 */
    @Min(value = 0, message = "隐藏点赞按钮设值不正确")
    @Max(value = 1, message = "隐藏点赞按钮设值不正确")
    private Integer hidePraise = 0;

    /** 是否前端展示 默认是 */
    private Boolean isDisplay = true;
    
}
