package com.kaiba.lib.base.domain.news.article;

import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.news.article.*;
import com.kaiba.lib.base.domain.IActionGetterSetter;
import com.kaiba.lib.base.domain.IAttrGetterSetter;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * author: lyux
 * date: 2023-07-26
 *
 * 新资讯文章池
 */
@Data
@ToString
@NoArgsConstructor
public class ArticleCreateModel implements IAttrGetterSetter, IActionGetterSetter {

    /** 电台 id */
    private Integer siteId;

    /** 内容所属频率频道. {@link NewsChannel} */
    private String channelKey;

    /** 频率频道的下属部门标识, 目前仅用于考核数据统计. */
    private String departKey;

    /** 所属分组. {@link GroupModel#getKey()} */
    private String group;

    /** 文章初始状态 {@link NewsState} */
    private String state;

    /** 所属模块列表. {@link NewsModuleConfigModel#getModule()}. */
    private Set<String> moduleIndex;

    /** 详情页以何种形式渲染. {@link NewsRenderer} */
    private String renderer;

    /** 浏览样式设置, 默认为 {@link DisplayViewStyle#EYE}. */
    private String viewStyle;

    /** 互动按钮设置, 默认为 {@link DisplayReactStyle#LIKE}. */
    private String reactStyle;

    /** 评论功能配置, 默认为 {@link DisplayReplyStyle#FREE} */
    private String replyStyle;

    /** 分享配置 */
    private ShareModel share;

    /** 指定为已存在板块作为评论板块. */
    private String threadId;

    // ---------------------------------------------------------
    // 内容字段

    /** 标题 */
    private String title;

    /** 副标题 */
    private String subtitle;

    /** 封面 */
    private List<Image> covers;

    /** 正文内容 */
    private String content;

    /** 内容字段类型, {@link NewsContentType}, 用来指明 {@link #content} 字段的格式. */
    private String contentType;

    /** 正文视频 */
    private ArticleVideo video;

    /** 正文音频 */
    private ArticleAudio audio;

    /** 投票实例 id */
    private String voteId;

    /** 如果 {@link #renderer} 为 {@link NewsRenderer#NONE}, 此为跳转页面. */
    private String action;

    /** 如果 {@link #renderer} 为 {@link NewsRenderer#NONE}, 此为跳转页面参数. */
    private Map<String, Object> actionParams;

    /** 以 key-value 存储的属性值 */
    private Map<String, String> attr;

    // ---------------------------------------------------------
    // 辅助字段

    /** 是否原创 */
    private Boolean isOriginal;

    /** 来源信息 */
    private String origin;

    /** 省宣: 稿件链接 */
    private String asTmuLink;

    /** 省宣: 预定的自动投递时间. 单位毫秒. */
    private Long asTmuSendTime;

    /** 如果文章代表一个电视或广播节目, 此为播出开始时间, 单位毫秒. */
    private Long releaseTime;

    /** 如果文章代表一个电视或广播节目, 此为播出结束时间, 单位毫秒. */
    private Long releaseEndTime;

}
