package com.kaiba.lib.base.domain.news.pool.knowledge;

import lombok.Data;

/**
 * Description: 知识库分类
 * Author: ZM227
 * Date: 2024/12/30 11:25
 */
@Data
public class KnowledgeCategoryModel {

    /**
     * 分类id
     */
    private String categoryId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类类型
     * 1-通用分类；2-专用分类
     */
    private Integer categoryType;

    /**
     * 有效知识点数量
     */
    private Integer knowledgeCount;

    /**
     * 父分类id
     */
    private String parentId;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 状态:0-无效,1-有效
     */
    private Integer status;

}
