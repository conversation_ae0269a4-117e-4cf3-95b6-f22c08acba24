package com.kaiba.lib.base.domain.publicservice;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/02/22 15:35
 */
@Data
@ToString
@NoArgsConstructor
public class PublicServiceInstanceModel {

    private String id;

    /** 电台id */
    private Integer siteId;

    /** 服务实例key */
    private String instanceKey;

    /** 服务实例下对应的banner key */
    private String bannerKey;

    /** 实例描述 */
    private String desc;

    /** 创建用户id */
    private Integer userId;

    /** 创建时间 ms */
    private Long createTime;

}
