package com.kaiba.lib.base.domain.da.eventtrack.event;

import com.kaiba.lib.base.constant.KbModule;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Set;

/**
 * author: lyux
 * date: 2025-03-07
 *
 * 埋点事件定义: 标准稿件用户行为埋点.
 * 使用场景建议: 建议用于满足以下条件的场景:
 * 1. 该动作由用户主动触发. 反例: 视频播放页面5秒无动作播放控制栏自动消失.
 * 2. 该动作会使业务系统发生数据变更. 反例: 滚动事件不会引发业务数据变更.
 * 3. 该动作没有触发当前页面压栈.
 *
 * 其中, 第3条建议出发点如下:
 * 1. 标准稿件浏览点中的 referer 字段已经可以实现大部分页面跳转事件的统计需求.
 * 2. 在组件化等泛用化页面中, 要在页面跳转埋点上附加稿件信息, 会导致专用业务数据污染通用数据结构, 破坏了模块分层单向依赖原则.
 */
@Data
@ToString
@NoArgsConstructor
public class DocActionEvent {

    /**
     * 动作标识. 以下为保留关键字, 语义相同的动作请复用如下定义:
     * 点赞: like
     * 分享: share
     * 评论: reply
     * 评论的评论: reply_r
     * 评论的点赞: reply_l
     * 投票: vote
     * 签到: sign
     * 购买: buy
     * 打赏: reward
     *
     * 动作语义不符合如上定义时, 请自行定义动作标识.
     */
    private String act;

    /** 业务类型. {@link KbModule}. 非空. */
    private String biz;

    /** 业务单元. 用以保持稿件定义的同构性: 同一个业务单元下的 ref 含义和层级结构总是一致的. 可为空. */
    private String unit;

    /** 对象标识1. 非空. */
    private String ref1;

    /** 对象标识2. 可为空. */
    private String ref2;

    /** 对象标识3. 可为空. */
    private String ref3;

    /** 站点 ID. 可为空. */
    private Integer siteId;

    /** 稿件标识, 拼接非空字段得到: biz-unit-ref1-ref2-ref3-siteId */
    private String docId;

    /**
     * 稿件标题. 非空.
     * 此为冗余字段, 以便在分析平台查询. 考核统计场景下应使用维表.
     */
    private String title;

    /**
     * 所属单位. 可为空.
     * 此为冗余字段, 以便在分析平台查询. 考核统计场景下应使用维表.
     */
    private String channel;

    /**
     * 所属部门. 部门是单位的下属组织. 可为空.
     * 此为冗余字段, 以便在分析平台查询. 考核统计场景下应使用维表.
     */
    private String depart;

    /**
     * 所属统计模块. 统计模块规定了稿件的各统计计数的汇总单元. 可为空.
     * 此为冗余字段, 以便在分析平台查询. 考核统计场景下应使用维表.
     */
    private Set<String> modules;

}
