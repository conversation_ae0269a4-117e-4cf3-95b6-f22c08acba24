package com.kaiba.lib.base.domain.appwidget.icongrid;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version IconGridIdxUpdateModel, v0.1 2023/7/19 09:59 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
public class IconGridIdxUpdateModel {

    /**
     * {@link IconGridInstanceModel#getId()}
     **/
    private String instanceId;

    /**
     *
     **/
    private List<IconGridIdx> idxList;



    @Data
    @ToString
    @NoArgsConstructor
    public static class IconGridIdx {

        /**
         * {@link IconGridDataModel#getId()}
         **/
        private String dataId;

        /**
         * {@link IconGridDataModel#getIdx()}
         **/
        private Integer idx;

    }
}
