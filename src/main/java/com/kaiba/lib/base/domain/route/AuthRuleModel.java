package com.kaiba.lib.base.domain.route;

import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.constant.auth.AuthType;
import com.kaiba.lib.base.constant.route.KbRouteRuleScope;
import com.kaiba.lib.base.constant.route.KbRouteRuleState;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 2021-03-24
 *
 * 路由鉴权规则.
 */
@Data
@ToString
@NoArgsConstructor
public class AuthRuleModel {

    private String id;

    /** 规则的适用范围. {@link KbRouteRuleScope}. 管理后台的相关功能会根据这个类型分别进行管理. */
    private Integer scope;

    /** 状态. {@link KbRouteRuleState}. */
    private Integer state;

    /** 鉴权规则: 鉴权方式. {@link AuthType}. 为空则不做鉴权. 适用 "and" 逻辑, 即必须通过全部鉴权方可访问. */
    private Integer method;

    /**
     * 鉴权规则: 当鉴权方式为 {@link AuthType#TOKEN} 时, 此为限定的端类型. 参考 {@link KbEndpoint}.
     * 为空则表示对任意端类型都支持.
     */
    private List<Integer> endpoint;

    /**
     * 鉴权规则: 当鉴权方式为 {@link AuthType#TOKEN} 时, 此为限定的时间区间, 单位毫秒.
     * 为空则使用 token 默认的过期规则进行时间限定.
     * 此配置是为了解决部分敏感功能需要用户在 token 过期前就主动更新的问题. 如 "签发确认" 操作, 需要用账号密码重新对用户进行确认.
     */
    private Long loginTimeLimit;

    /** 名称 */
    private String name;

    /** 描述 */
    private String description;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    /** 创建时间, 单位毫秒 */
    private Long updateTime;

}
