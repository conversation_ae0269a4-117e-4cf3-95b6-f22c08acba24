package com.kaiba.lib.base.domain.map;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 2022-03-21
 *
 * 折线.
 * https://lbs.amap.com/api/javascript-api/reference/overlay#polyline
 */
@Data
@ToString
@NoArgsConstructor
public class MapLine {

    /** 标识一个具体的数据对象, 方便在调用地图编辑工具等场景下追踪定位元素. 可为空. */
    private String id;

    /** 叠加顺序. */
    private Integer zIndex;

    /** 线条颜色, 使用16进制颜色代码赋值. 默认值为 #006600. */
    private String strokeColor;

    /** 线透明度, 取值范围 [0,1], 0表示完全透明, 1表示不透明 */
    private Float strokeOpacity;

    /** 线样式, 实线: solid, 虚线: dashed  */
    private String strokeStyle;

    /** 线宽度. */
    private Integer strokeWeight;

    /** 折线的节点坐标数组.  */
    private List<LngLat> path;

}
