package com.kaiba.lib.base.domain.election;

import com.kaiba.lib.base.domain.IAttrGetterSetter;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * author: lyux
 * date: 2022-09-01
 */
@Data
@NoArgsConstructor
public class CandidateApiModel implements IAttrGetterSetter {

    private String id;

    /** 投票实例 id. {@link ElectionModel} */
    private String electionId;

    /** 候选人编号, 用于前端显示. */
    private String idx;

    /** {@link com.kaiba.lib.base.constant.election.CandidateState} */
    private Integer state;

    /** 以 key-value 存储的属性值 */
    private Map<String, String> attr;

    /** 当前票数排名 */
    private Integer ticketRank;

    /** 候选人当前得票总数, 会受到 virtualId 加权影响. */
    private Integer ticketCount;

    /** 用户对该候选人的投票数 */
    private Integer ticketBySelf;

    /** 重置周期内, 用户对该候选人允许的最大投票数 */
    private Integer ticketBySelfMax;

    /** 按请求批次的票数排名 */
    private Integer lastMarkTicketRank;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    // ---------------------------------------------------------

    /**
     * 用户当前可用票数. 仅在取候选人详情接口时会被赋值.
     * 列表接口的用户可用票数信息应使用 {@link ElectionApiModel#getUserTicket()} 字段.
     */
    private Integer userTicket;

}
