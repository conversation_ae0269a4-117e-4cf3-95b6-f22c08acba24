package com.kaiba.lib.base.domain.workorder;

import com.kaiba.lib.base.constant.workorder.WOIdentity;
import com.kaiba.lib.base.domain.user.UserModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 2023-11-07
 *
 * 工单系统, 案件关注者. {@link WOIdentity#FOLLOWER}
 */
@Data
@ToString
@NoArgsConstructor
public class WOFollowerModel {

    private String id;

    /** 案件 id. {@link WOCaseModel#getId()} */
    private String caseId;

    /** 用户 id. */
    private Integer userId;

    /** 关注者信息 */
    private List<WOACLStringData> info;

    /** 关注者留言, 非必填 */
    private String message;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

    // -----------------------------------------------------

    /** 用户 */
    private UserModel user;

    /** 经过 ACL 检测后的 {@link #info}, 以 {@link WOACLStringData#getKey()} 作为 map 的 key . */
    private Map<String, WOACLStringData> userInfo;

}
