package com.kaiba.lib.base.domain.activityhub;

import com.kaiba.lib.base.domain.actconf.conf.ActConfInstanceModel;
import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 2023-11-22
 *
 * 活动登记表.
 */
@Data
@ToString
@NoArgsConstructor
public class KbActivityUpdateModel {

    private String id;

    /** 活动名称. 需要填写活动的官方全称. */
    private String name;

    /** 活动简称. 尽量简短 */
    private String abbr;

    /** 活动描述. 非必填 */
    private String desc;

    /** 项目成员及职责表 */
    private List<KbActivityMember> members;

    /** 组件列表, 可用于管理后台跳转相应组件的管理页面 */
    private List<KbActivityComponent> components;

    /** 页面截图, 用作刊例. 第一张建议放主页截图. */
    private List<Image> screenshots;

    /** 子活动. 该字段决定活动管理后台的菜单折叠方式. */
    private List<String> subActivities;

    /** 标签, 用于管理后台筛选. */
    private List<String> tags;

    /** 关联的活动配置 key. {@link ActConfInstanceModel#getKey()}. */
    private String confKey;

    /** 是否展示在后台活动管理菜单上. */
    private Boolean showOnMenu;

    /** 计划上线时间, 单位毫秒. 仅做展示, 不参与逻辑. */
    private Long startTimeByPlan;

    /** 计划下线时间, 单位毫秒. 仅做展示, 不参与逻辑. */
    private Long endTimeByPlan;

}
