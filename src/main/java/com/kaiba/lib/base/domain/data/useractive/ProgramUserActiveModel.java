package com.kaiba.lib.base.domain.data.useractive;

import com.kaiba.lib.base.domain.data.IKbDataV;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 19-12-11
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class ProgramUserActiveModel implements IKbDataV {

    /** 节目 id */
    private String programId;

    /** 节目排班实例 id */
    private String instanceId;

    /** 节目话题 id */
    private String topicId;

    /** 该当节目的主持人列表, 用户 id */
    private List<Integer> es;

}
