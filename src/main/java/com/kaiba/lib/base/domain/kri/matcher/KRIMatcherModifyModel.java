package com.kaiba.lib.base.domain.kri.matcher;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.kri.KRIMatcherFact;
import com.kaiba.lib.base.domain.kri.IKbResource;
import com.kaiba.lib.base.domain.kri.KbResource;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2024-12-04
 *
 * kaiba resource identifier. 开吧统一资源标识.
 * 匹配器-修改接口入参.
 */
@Data
@ToString
@NoArgsConstructor
public class KRIMatcherModifyModel implements IKbResource {

    /** 业务类型. {@link KbModule} */
    private String biz;

    /** 功能单元 */
    private String unit;

    /** 对象标识 1, 匹配参数. */
    private String ref1;

    /** 对象标识 2, 匹配参数. */
    private String ref2;

    /** 对象标识 3, 匹配参数. */
    private String ref3;

    /** 业务事实类型. {@link KRIMatcherFact}. */
    private String fact;

    /** 业务事实对应的数据. */
    private String data;

    @Override
    public KbResource obtainKbResource() {
        return new KbResource(biz, unit, ref1, ref2, ref3);
    }
}
