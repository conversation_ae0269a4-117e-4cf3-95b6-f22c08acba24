package com.kaiba.lib.base.domain.auth;

import com.kaiba.lib.base.domain.user.UserModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 19-8-9
 */
@Data
@NoArgsConstructor
@ToString
public class AuthRoleModel {

    /** 角色字符串. 角色的唯一标识 */
    private String role;

    /** 角色被授予的权限列表 */
    private List<String> permissions;

    /** 与该角色冲突的角色列表 */
    private List<String> collideWith;

    /** 适用范围. 参考 {@link com.kaiba.lib.base.constant.auth.AuthScope} */
    private Integer scope;

    /** 适用范围文字说明 */
    private String scopeName;

    /** 对象 id, 比如电台 id. 配合 scope 字段使用. */
    private String referenceId;

    /** 通配格式, 比如 site.admin.${siteId}. */
    private String format;

    /** 描述 */
    private String description;

    /** 创建时间 */
    private Long createTime;

    /** 授权者 */
    private UserModel authUser;

    /** 授权时间 */
    private Long authCreateTime;

    /** 授权到期时间 */
    private Long authExpireTime;

}
