package com.kaiba.lib.base.domain.playback;

import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.program.EmceeModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * author: lyux
 * date: 19-7-24
 */
@Data
@NoArgsConstructor
public class PlaybackModel {

    /** 主键 */
    private String id;

    /** 所属专辑 id */
    private String albumId;

    /** 所属专辑 id 数组*/
    private List<String> albumIds;

    /** 对应的节目 id */
    private String programId;

    /** 电台 id */
    private Integer siteId;

    /** 标题 */
    private String title;

    /** 副标题 */
    private String subTitle;

    /** 封面 */
    private String cover;

    /** 头图 */
    private List<Image> headImageList;

    /** 本期主持人 */
    private List<EmceeModel> emceeList;

    /** 音频 url */
    private String audioUrl;

    /** 音频时长, 单位秒 */
    private Long audioDuration;

    /** 音频大小, 单位 B */
    private Long audioSize;

    /** 对应的帖子板块 id */
    private String threadId;

    /** 对应节目的播出时间 */
    private Long programTime;

    /** 对应音频上传时间 */
    private Long createTime;

    /** 播放次数 */
    private Integer playCount;

    private ShareModel shareModel;

    /** 推荐列表 */
    private List<PlaybackModel> recommendList;

    /** 内容是否可以被第三方电台合作伙伴分享 */
    private Boolean isShare;
}
