package com.kaiba.lib.base.domain.news.channel;

import com.kaiba.lib.base.domain.news.article.ArticleModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 2024-09-09
 *
 * 频道批量排序.
 */
@Data
@ToString
@NoArgsConstructor
public class ChannelUpdateOrderModel {

    /** 指定排序 */
    private List<Order> orders;

    // ------------------------------------------------------------

    @Data
    @ToString
    @NoArgsConstructor
    public static class Order {

        /** {@link ArticleModel#getId()} */
        private String channel;

        /** 顺序, 降序排列, 最大值 999 */
        private Long order;

    }

}
