package com.kaiba.lib.base.domain.da.sensors.kanban;

import com.kaiba.lib.base.constant.da.sensors.KanbanAggrType;
import com.kaiba.lib.base.constant.da.sensors.KanbanColDataType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2024-02-27
 *
 * 看板列定义
 */
@Data
@ToString
@NoArgsConstructor
public class KanbanColumn {

    /** 列所在分组名称, 可为空. 相同分组名称的列在看板展示时会被聚合在一起. */
    private String group;

    /** 列名 */
    private String name;

    /** 数据种类 {@link KanbanColDataType} */
    private String type;

    /**
     * 分时数据配置, 根据 type 不同, 可能对应以下三个配置中的一个:
     * {@link DivConfigPGCModel}, {@link DivConfigActModel}, {@link DivConfigPageModel}
     */
    private String configId;

    /**
     *  聚合类型.
     * {@link KanbanAggrType}
     */
    private String algorithm;

    /** 是否显示本列总值, 默认为 false. */
    private Boolean showTotal;

    /** 是否显示本列均值, 默认为 false. */
    private Boolean showAverage;

    /** 列宽*/
    private Integer width;

}
