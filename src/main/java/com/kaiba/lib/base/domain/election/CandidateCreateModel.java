package com.kaiba.lib.base.domain.election;

import com.kaiba.lib.base.domain.IAttrGetterSetter;
import com.kaiba.lib.base.domain.counter.KbCounterVirtualModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * author: lyux
 * date: 2022-09-01
 */
@Data
@NoArgsConstructor
public class CandidateCreateModel implements IAttrGetterSetter {

    /** 操作者 id */
    private Integer operatorId;

    /** 投票实例 id, 与 electionKey 必传其一. {@link ElectionModel} */
    private String electionId;

    /** 投票实例 key, 与 electionId 必传其一. {@link ElectionModel} */
    private String electionKey;

    /** 候选人编号, 用于前端显示. */
    private String idx;

    /** 运营手动排序序号. */
    private Integer orderIdx;

    /**
     * 虚拟计数规则实例 id. {@link KbCounterVirtualModel}
     * 如果 {@link ElectionModel#getVirtualId()} 不为空, 优先使用本实例中的虚拟计数规则.
     */
    private String virtualId;

    /** {@link com.kaiba.lib.base.constant.election.CandidateState} */
    private Integer state;

    /** 以 key-value 存储的属性值 */
    private Map<String, String> attr;

    /** 可被模糊搜索到的候选人信息 */
    private String searchable;
    
    // ---------------------------------------
    
    /** 使用创建序号生成候选人编号 */
    private Boolean idxByCreate = true;

}
