package com.kaiba.lib.base.domain.prize;

/**
 * author: lyux
 * date: 2020-04-30
 */
public interface IDispatchTimeCalculator {

    Long getDispatchBaseTime();

    Long getDispatchStartTimeOffset();

    Long getDispatchEndTimeOffset();

    default long getDispatchStartTime(long prizeCreateTime) {
        Long dispatchStartTimeOffset = getDispatchStartTimeOffset();
        if (dispatchStartTimeOffset == null) {
            return 0L;
        }
        Long dispatchBaseTime = getDispatchBaseTime();
        if (dispatchBaseTime == null || dispatchBaseTime == 0) {
            dispatchBaseTime = prizeCreateTime;
        }
        return dispatchBaseTime + dispatchStartTimeOffset;
    }

    default long getDispatchEndTime(long prizeCreateTime) {
        Long dispatchEndTimeOffset = getDispatchEndTimeOffset();
        if (dispatchEndTimeOffset == null) {
            return 0L;
        }
        Long dispatchBaseTime = getDispatchBaseTime();
        if (dispatchBaseTime == null || dispatchBaseTime == 0) {
            dispatchBaseTime = prizeCreateTime;
        }
        return dispatchBaseTime + dispatchEndTimeOffset;
    }

}
