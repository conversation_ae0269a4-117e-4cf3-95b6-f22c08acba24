package com.kaiba.lib.base.domain.news.pool.knowledge;

import lombok.Data;
import lombok.Getter;
import org.springframework.data.domain.Pageable;

/**
 * Description: 知识库查询Model
 * Author: ZM227
 * Date: 2025/1/23 16:10
 */
@Data
public class KnowledgeQueryModel {

    /**
     * 知识条目Id
     */
    private String knowledgeId;

    /**
     * 知识点关键字，title+tags
     */
    private String keyWords;

    /**
     * 关联文章id
     */
    private String articleId;

    /**
     * 分类code
     */
    private String categoryId;

    /**
     * 状态:0-无效,1-有效
     */
    private Integer status;
}
