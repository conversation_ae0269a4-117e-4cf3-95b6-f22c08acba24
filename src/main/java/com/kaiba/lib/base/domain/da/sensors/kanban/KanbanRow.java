package com.kaiba.lib.base.domain.da.sensors.kanban;

import com.kaiba.lib.base.constant.da.sensors.KanbanColDataType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2024-02-26
 *
 * 看板行数据
 */
@Data
@ToString
@NoArgsConstructor
public class KanbanRow {

    /** 列所在分组名称, 可为空. 相同分组名称的列在看板展示时会被聚合在一起. */
    private String group;

    /** 列名 */
    private String name;

    /** 数据种类 {@link KanbanColDataType} */
    private String type;

    /**
     * 分时数据配置, 根据 type 不同, 可能对应以下三个配置中的一个:
     * {@link DivConfigPGCModel}, {@link DivConfigActModel}, {@link DivConfigPageModel}
     */
    private String configId;

    /** 去重数, 0 或负数代表不去重. */
    private Integer card;

}
