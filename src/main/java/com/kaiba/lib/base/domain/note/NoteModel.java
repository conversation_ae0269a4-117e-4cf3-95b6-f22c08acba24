package com.kaiba.lib.base.domain.note;

import com.kaiba.lib.base.constant.note.NoteReviewState;
import com.kaiba.lib.base.constant.note.NoteState;
import com.kaiba.lib.base.domain.common.*;
import com.kaiba.lib.base.domain.user.UserModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 19-5-21
 */
@Data
@ToString
@NoArgsConstructor
public class NoteModel {

    private String id;

    /** 电台 id */
    private Integer siteId;

    /** 地市 id */
    private Integer source;

    /** 发帖者 id */
    private Integer userId;

    /** 内容: 音频 */
    private Audio audio;

    /** 内容: 视频 */
    private Video video;

    /** 内容: 图片列表 */
    private List<Image> images;

    /** 内容: 文字 */
    private String content;

    /** 图片组 */
    private Gallery gallery;

    /** 内容: 备注. 备注内容一般由运营人员添加, 且 C 端用户不可见. */
    private String remark;

    /** 发帖时用户坐标-经度 */
    private Double longitude;

    /** 发帖时用户坐标-纬度 */
    private Double latitude;

    /** 逆地理信息得到的街道名称 */
    private String street;

    /** 广告标签文案. 比如 "广告", "赞助商提供的广告" 等. 以该字段是否有值判断该条数据是否广告 */
    private String adMark;

    /** 广告跳转链接 */
    private String adLink;

    /** 外链列表 */
    private List<NoteLink> links;

    /** 置顶的评论 id */
    private String stickyCommentId;

    /** 帖子发布时所选择的板块 id . */
    private String originThreadId;

    /** 所属板块 id 列表. */
    private List<String> threadIds;

    /** {@link com.kaiba.lib.base.constant.note.NoteState} */
    private Integer state;

    /** 热度, 排序依据 */
    private Long heat;

    /** 是否已经被标记为删除. 一般由用户删除会开启此标志. Deprecated, in honor of {@link #state}. */
    @Deprecated
    private Boolean isSoftDeleted;

    /** 是否允许评论. 默认为空, 表示允许. */
    private Boolean isAllowComment;

    /** 是否允许点赞. 默认为空, 表示允许. */
    private Boolean isAllowPraise;

    /** 是否匿名发布, 即不显示发布者信息 */
    private Boolean isAnonymous;

    /** 当前用户是否已对该贴点赞 */
    private Boolean isPraised;

    /** 该贴是否置顶帖 */
    private Boolean isTop;

    /** 该贴是否热议帖子 */
    private Boolean isHot;

    /** 发帖时间, 单位秒 */
    private Long createTime;

    /** 发帖时间, 单位毫秒 */
    private Long createTimeMS;

    /** 更新时间, 单位秒 */
    private Long updateTime;

    /** 刷新时间, 单位秒 */
    private Long refreshTime;

    /** 刷新时间, 单位毫秒 */
    private Long refreshTimeMS;

    /** 评论数 */
    private Long commentCount;

    /** 点赞数 */
    private Long praiseCount;

    /** 浏览数 */
    private Long viewCount;

    /** 分享数 */
    private Long shareCount;

    /** 以键值定义的额外属性值 */
    private Map<String, String> extra;

    // -------------------------------------------------
    // 根据请求数据时 NoteGrainFlag 的设置而组装的数据字段

    /** 发帖者. 根据请求数据时的 {@link NoteGrainFlag} 设置, 用户数据可能仅有 {@link UserModel#getUserId()} 非空. */
    private UserModel user;

    /** 置顶的评论. 根据请求数据时的 {@link NoteGrainFlag} 设置, 即使 {@link #stickyCommentId} 存在该数据也可能为空. */
    private NoteCommentModel stickyComment;

    /** 帖子发布时所选择的板块. */
    private NoteThreadModel originThread;

    /** 所属板块列表. */
    private List<NoteThreadModel> threads;

    /** 点赞列表 */
    private List<UserModel> praiseList;

    /** 评论列表 */
    private List<NoteCommentModel> commentList;

    /** 置顶的主题列表, 仅后台需要该字段 */
    private List<NoteThreadModel> topThreads;

    /** 热议的主题列表, 仅后台需要该字段 */
    private List<NoteThreadModel> hotThreads;

    /** 分享数据 */
    private ShareModel shareModel;

    // -------------------------------------------------
    // for note review only

    /** 审核表 id, 仅后台需要该字段 */
    private String noteReviewId;

    /** 审核状态. {@link NoteReviewState}, 仅后台需要该字段 */
    private Integer noteReviewState;

    /** 审核批注, 仅后台需要该字段 */
    private String noteReviewRemark;

    // -------------------------------------------------
    // for note delete only

    /** 删除表 id, 仅后台需要该字段 */
    private String noteDeleteId;

    /** 删除批注, 仅后台需要该字段 */
    private String noteDeleteRemark;

    /** 删除者, 仅后台需要该字段 */
    private UserModel operator;

    /** 删除时间, 仅后台需要该字段 */
    private Long deleteTime;

    // -------------------------------------------------

    /** 辅助翻页请求的标签参数. 请求下一页时, 应始终将上一页第一个元素的该字段作为请求参数. */
    private String mark;

    // -------------------------------------------------
    // for tmyun

    /** 点位标识. {@link com.kaiba.lib.base.domain.tmuyun.latch.EventPositionModel} */
    private String tmuPosition;

    // -------------------------------------------------

    public boolean isSoftDeleted() {
        if (state == null) {
            return isSoftDeleted != null && isSoftDeleted;
        } else {
            return state == NoteState.SOFT_DELETE.getValue();
        }
    }

    public boolean isAuthorOnly() {
        return state != null && state == NoteState.AUTHOR_ONLY.getValue();
    }

    public static final Comparator<NoteModel> CREATE_TIME_DESC_COMPARATOR = (o1, o2) -> {
        long c1 = o1.getCreateTime() == null ? 0 : o1.getCreateTime();
        long c2 = o2.getCreateTime() == null ? 0 : o2.getCreateTime();
        return (int) (c2 - c1);
    };

}
