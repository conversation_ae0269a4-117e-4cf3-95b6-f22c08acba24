package com.kaiba.lib.base.domain.appwidget.actionlist;

import com.kaiba.lib.base.constant.appwidget.WidgetItemState;
import com.kaiba.lib.base.constant.common.KbImageFormat;
import com.kaiba.lib.base.domain.IAttrGetterSetter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Map;

/**
 * author: lyux
 * date: 2023-12-05
 */
@Data
@ToString
@NoArgsConstructor
public class ActionListDataModel {

    /** 主键 */
    private String id;

    /** {@link ActionListInstanceModel} */
    private String instanceId;

    /** {@link WidgetItemState} */
    private Integer state;

    /** 顺序 */
    private Integer idx;
    
    /** 图片 url. */
    private String imageUrl;

    /** 图片格式. {@link KbImageFormat} */
    private String imageFormat;

    /** 标题. */
    private String title;

    /** 副标题. 可为空. */
    private String subTitle;

    /** 左下角文字. 可为空 */
    private String blTitle;

    /** 左下角文字前的图标. 可为空 */
    private String blIcon;

    /** 右下角文字. 可为空 */
    private String brTitle;

    /** 右下角文字前的图标. 可为空 */
    private String brIcon;

    /** 点击时的跳转页面. */
    private String action;

    /** 点击时的跳转页面, 参数. */
    private Map<String, Object> actionParams;

    /** 更新时间, 单位毫秒. 该字段会影响缓存的加载机制. */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
