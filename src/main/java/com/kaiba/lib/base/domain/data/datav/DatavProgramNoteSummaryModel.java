package com.kaiba.lib.base.domain.data.datav;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class DatavProgramNoteSummaryModel {

    private String date;
    private Long startTime;
    private Long noteCount;
    private Long commentCount;
    private Long praiseCount;
    private Long shareCount;
    private Long imagesCount;
    private Long videoCount;
    private Long audioCount;

    public DatavProgramNoteSummaryModel() {
        this.noteCount = 0L;
        this.commentCount = 0L;
        this.praiseCount = 0L;
        this.shareCount = 0L;
        this.imagesCount = 0L;
        this.videoCount = 0L;
        this.audioCount = 0L;
    }
}
