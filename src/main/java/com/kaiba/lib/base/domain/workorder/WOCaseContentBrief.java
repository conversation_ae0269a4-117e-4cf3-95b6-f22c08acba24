package com.kaiba.lib.base.domain.workorder;

import com.kaiba.lib.base.domain.common.Audio;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 2024-01-23
 *
 * 以统一格式表示的案件内容摘要, 用以平抑各业务间数据格式的不同点, 对外通过统一接口提供部分数据, 典型使用场景比如案件列表页.
 * 所有字段均非必填, 根据业务特点传值即可.
 */
@Data
@ToString
@NoArgsConstructor
public class WOCaseContentBrief implements IWOCaseContent {

    /** 内容 id. 注意: 这不是 caseId */
    private String id;

    /** 内容类型. 可为空. */
    private String type;

    /** 标题 */
    private String title;

    /** 副标题 */
    private String subtitle;

    /** 内容: 文字 */
    private String content;

    /** 内容: 音频 */
    private Audio audio;

    /** 内容: 视频 */
    private Video video;

    /** 内容: 图片列表 */
    private List<Image> images;

    /** 以键值定义的额外属性值 */
    private Map<String, String> attr;

    /** 原始内容JSON字符串. 此属性不对外展示,仅用于内容序列化和反序列化解析 */
    private String originContentJson;

    // ---------------------------------------------

    @Override
    public String obtainContentId() {
        return id;
    }

    @Override
    public String obtainContentText() {
        return content;
    }
}
