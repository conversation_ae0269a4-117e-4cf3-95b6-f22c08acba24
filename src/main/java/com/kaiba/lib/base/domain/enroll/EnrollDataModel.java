package com.kaiba.lib.base.domain.enroll;

import com.kaiba.lib.base.constant.enroll.EnrollDataType;
import com.kaiba.lib.base.constant.enroll.EnrollModuleType;
import com.kaiba.lib.base.domain.common.Audio;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
public class EnrollDataModel {

    private String id;

    /** 报名活动标识 */
    private String enrollId;

    /** 组件标识 */
    private String moduleId;

    /** 设备标识. */
    private String cid;

    private Integer userId;

    private String title;

    /** 索引 */
    private String mark;

    /** 创建时间 */
    private Long createTime;

    /** 组件的类型. {@link EnrollModuleType} */
    private Integer type;

    /** 该项的填写内容. 建议使用分开的内容描述. */
    @Deprecated
    private Object data;

    /** {@link EnrollDataType#TEXT} */
    private String text;

    /** {@link EnrollDataType#IMAGE} */
    private List<Image> images;

    /** {@link EnrollDataType#VIDEO} */
    private Video video;

    /** {@link EnrollDataType#AUDIO} */
    private Audio audio;

    /** {@link EnrollDataType#CHOICE} */
    private List<Integer> choice;

    /** {@link EnrollDataType#RATING} */
    private Integer rating;

    private Long updateTime;
}
