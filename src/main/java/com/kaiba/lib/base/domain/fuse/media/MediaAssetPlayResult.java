package com.kaiba.lib.base.domain.fuse.media;

import com.kaiba.lib.base.constant.fuse.media.MediaAssetState;
import lombok.Data;

/**
 * 媒资播放查询结果
 * <AUTHOR>
 * @version MediaAssetPlayResult, v0.1 2024/10/23 15:41 daopei Exp $
 **/
@Data
public class MediaAssetPlayResult {

    /** 媒资ID */
    private String mediaId;

    /** 媒资状态. {@link MediaAssetState} */
    private String state;

    /** 存储平台 */
    private String storagePlatform;

    // 视频信息----------------------

    /** 视频封面 */
    private String coverUrl;

    /** 可播放视频信息. 可能为空 */
    private MediaAssetPlayInfoModel playInfo;

    public static MediaAssetPlayResult on(String mediaId) {
        MediaAssetPlayResult result = new MediaAssetPlayResult();
        result.setMediaId(mediaId);
        return result;
    }

    public MediaAssetPlayResult storagePlatform(String storagePlatform) {
        this.storagePlatform = storagePlatform;
        return this;
    }

    public MediaAssetPlayResult coverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
        return this;
    }

    public MediaAssetPlayResult playInfo(MediaAssetPlayInfoModel playInfo) {
        this.playInfo = playInfo;
        return this;
    }

    public MediaAssetPlayResult state(String state) {
        this.state = state;
        return this;
    }
}
