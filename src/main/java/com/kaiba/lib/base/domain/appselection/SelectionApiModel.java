package com.kaiba.lib.base.domain.appselection;

import com.kaiba.lib.base.constant.apphome.AppHomeSelectionFrom;
import com.kaiba.lib.base.constant.apphome.AppHomeSelectionOrigin;
import com.kaiba.lib.base.constant.apphome.AppHomeSelectionStyle;
import com.kaiba.lib.base.constant.apphome.AppHomeSelectionTarget;
import com.kaiba.lib.base.domain.IActionGetterSetter;
import com.kaiba.lib.base.domain.da.sensors.channel.SensorsChannelModel;
import com.kaiba.lib.base.domain.da.sensors.virtual.ISensorsVirtualSetter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Map;

/**
 * author: lyux
 * date: 2023-08-08
 */
@Data
@ToString
@NoArgsConstructor
public class SelectionApiModel implements IActionGetterSetter, ISensorsVirtualSetter {

    private String id;

    /** 推荐位置. {@link AppHomeSelectionTarget} */
    private Integer target;

    /** 展示样式. {@link AppHomeSelectionStyle} */
    private Integer style;

    /** 标题 */
    private String title;

    /** 副标题, 主要用于推送副标题, 截止 2023-08028 前端不予展示 */
    private String subTitle;

    /** 内容提示, 用于前端展示. 如 "11月12日 20:56 资讯"; "火热进行中". */
    private String hint;

    /** 浏览量, 负数表示不显示 */
    private Long viewCount;

    /** 大图/一图/二图 模式封面. 宽高比固定为 3:2 */
    private SelectionCover image1;

    /** 二图 模式封面. 宽高比固定为 3:2 */
    private SelectionCover image2;

    /** 腰封模式封面. 宽高比固定为 6.98:1 */
    private SelectionCover imageBelt;

    /** 跳转页面. */
    private String action;

    /** 跳转页面参数. */
    private Map<String, Object> actionParams;

    /** 内容来源 {@link AppHomeSelectionOrigin} */
    private String origin;

    /** 供 C 端展示的来源名称 */
    private String originName;

    /** 数据推荐依据: 为配合神策资源位运营和曝光埋点. {@link AppHomeSelectionFrom}. */
    private String dataFrom;

    /** 指示后续列表中不要再次出现该元素 */
    private Boolean uniq;

    /** 查询时以此字段降序排列. */
    private Long seq;

    /**
     * 人工指定的排序依据, 降序, 最大值 999.
     * 最终会根据算法生成 {@link #seq}. 因此本字段仅做记录, 实际排序应使用 {@link #seq} 字段
     */
    private Long idx;

    /** 进入推荐池的时间, 单位毫秒. */
    private Long selectTime;

    /** 向客户端展示的时间戳, 单位毫秒 */
    private Long displayTime;

    /** VirtualViewCount 虚拟浏览量, 负数表示不显示 */
    private Long vvc;

    // 冗余selectionContent的一些字段...

    /** 内容引用 ID. 和 {@link #origin} 一起定位具体内容. */
    private String ref1;

    /** 内容引用 ID. 和 {@link #origin} 一起定位具体内容. */
    private String ref2;

    /** 内容引用 ID. 和 {@link #origin} 一起定位具体内容. */
    private String ref3;

    /** 内容关联频率频道 */
    private SensorsChannelModel channel;


    @Override
    public void setVirtualViewCount(Long viewCount) {
        this.vvc = viewCount;
    }
}
