package com.kaiba.lib.base.domain.circle;

import com.kaiba.lib.base.constant.circle.CircleAttrKeys;
import com.kaiba.lib.base.constant.circle.CircleState;
import com.kaiba.lib.base.constant.note.NoteThreadCondition;
import com.kaiba.lib.base.domain.note.NoteThreadPostRule;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 19-5-30
 *
 * 板块模组. 板块模组代表一组板块(thread), 用以方便对某些业务进行操作, 比如车友圈.
 */
@Data
@ToString
@NoArgsConstructor
public class CircleThreadCreateModel {

    /** 板块状态. {@link CircleState} */
    private Integer state;

    /** 用于老版圈子排序. 升序排列. */
    private Integer order;

    /** 板块 id. */
    private String threadId;

    /** 板块 key. */
    private String threadKey;

    // ----------------------------------------------

    /**
     * 个人专版 - 头像.
     * 对应 {@link CircleThreadModel#getAttr()}, key 值 {@link CircleAttrKeys#TOPIC_AVATAR}
     */
    private String topicAvatar;

    /**
     * 热门话题 - 封面.
     * 对应 {@link CircleThreadModel#getAttr()}, key 值 {@link CircleAttrKeys#HOT_TOPIC_BKG_IMAGE_1}
     */
    private String hotTopicImage1;

    /**
     * 热门话题 - 背景色, 格式 "#808080".
     * 对应 {@link CircleThreadModel#getAttr()}, key 值 {@link CircleAttrKeys#HOT_TOPIC_BKG_COLOR}
     */
    private String hotTopicColor;

    // ----------------------------------------------
    // 板块信息. 若 threadId/threadKey 不存在, 则使用下述信息创建板块. 若 threadId/threadKey 存在, 则忽略下述信息.

    /** 电台 id */
    private Integer siteId;

    /** 板块状态. {@link NoteThreadCondition} */
    private Integer condition;

    /** 板块评论状态. {@link NoteThreadCondition}. 默认为空, 为空则取值 {@link #condition} */
    private Integer commentCondition;

    /** 板块名称 */
    private String title;

    /** 板块描述 */
    private String description;

    /** 热议帖子上限 */
    private Integer hotMax;

    /** 置顶帖子上限 */
    private Integer topMax;

    /** 是否开启审核列表和已审列表混合分页功能 */
    private Boolean mixReviewPageable;

    /** 发帖规则. 首发于该板块下的帖子都要遵守该规则. 若该字段不存在, 则遵循默认规则. */
    private NoteThreadPostRule postRule;

    /** 有新帖发布到该板块时, 还要同时发布到的板块列表 */
    private List<String> routeThreads;

}
