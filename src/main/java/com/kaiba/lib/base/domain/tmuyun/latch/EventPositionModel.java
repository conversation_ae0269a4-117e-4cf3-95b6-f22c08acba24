package com.kaiba.lib.base.domain.tmuyun.latch;

import com.kaiba.lib.base.constant.tmuyun.EventPositionState;
import com.kaiba.lib.base.constant.tmuyun.TmuyunRefType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * author: lyux
 * date: 2022-07-27
 */
@Data
@ToString
@NoArgsConstructor
public class EventPositionModel {

    /** 物理主键 */
    private String id;

    /** 事件点位. 逻辑主键. */
    private String position;

    /** 事件类型. {@link TmuyunRefType} */
    private Integer type;

    /** 点位状态. {@link EventPositionState} */
    private Integer state;

    private Integer siteId;

    /** 点位描述 */
    private String description;

    /** 创建时间 */
    private Long updateTime;

    /** 修改时间 */
    private Long createTime;

    private Integer userId;
}
