package com.kaiba.lib.base.constant.news.videoclip;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/07/04 10:32
 **/
@Getter
@Deprecated
public enum VideoClipState implements IKbEnumValueGetter {
    /** 短视频状态 */
    INIT(1, "初始化"),
    SIGNED(2, "已签发未上线"),
    ONLINE(3, "上线"),
    OFFLINE(4, "下线")
    ;

    private final int value;
    private final String description;

    VideoClipState(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<VideoClipState> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (VideoClipState item : values()) {
            if (item.value == value) {
                return Optional.of(item);
            }
        }
        return Optional.empty();
    }
}
