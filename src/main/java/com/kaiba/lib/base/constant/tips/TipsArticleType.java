package com.kaiba.lib.base.constant.tips;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 2023-02-10
 */
@Getter
public enum TipsArticleType implements IKbEnumValueGetter {

    ARTICLE(1, "文章"),
    SINGLE_CHOICE(2, "单选题目"),
    MULTI_CHOICE(3, "多选题目"), // 暂不支持
    ;

    private final int value;

    private final String description;

    TipsArticleType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static Optional<TipsArticleType> resolveByValue(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (TipsArticleType v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }
}
