package com.kaiba.lib.base.constant.vote;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-10-20
 *
 * 通用投票, 样式枚举.
 */
@Getter
public enum VoteStyle {

    LIST(1, 1000, 1000, "投票列表"),
    FOR_AGAINST_1(2, 2, 20, "支持-反对"),
    FOR_AGAINST_2(2, 2, 20, "支持-反对"),
    FOR_AGAINST_3(2, 2, 20, "支持-反对"),
    TRI_SECTION1(3, 3, 20, "胜-平-负"),
    TRI_SECTION2(3, 3, 20, "胜-平-负"),
    TRI_SECTION3(3, 3, 20, "胜-平-负"),
    ;

    private final int minOption;
    private final int maxOption;
    private final int maxText;
    private final String desc;

    VoteStyle(int minOption, int maxOption, int maxText, String desc) {
        this.minOption = minOption;
        this.maxOption = maxOption;
        this.maxText = maxText;
        this.desc = desc;
    }

    public static Optional<VoteStyle> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (VoteStyle v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<VoteStyle> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (VoteStyle v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
