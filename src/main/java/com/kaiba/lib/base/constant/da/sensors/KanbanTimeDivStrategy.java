package com.kaiba.lib.base.constant.da.sensors;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 24-02-26
 *
 * 神策数据看板的数据分时策略配置
 */
@Getter
public enum KanbanTimeDivStrategy {

    HOUR("按小时分时"),
    DAY("按天分时"),
    ;

    private final String description;

    KanbanTimeDivStrategy(String description) {
        this.description = description;
    }

    public static Optional<KanbanTimeDivStrategy> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (KanbanTimeDivStrategy v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<KanbanTimeDivStrategy> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (KanbanTimeDivStrategy v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
