package com.kaiba.lib.base.constant.banner;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import java.util.Optional;

public enum BannerState implements IKbEnumValueGetter {

    INIT(1, "配置中"),
    SIGNED(2, "已签发"),
    ONLINE(3, "已上线"),
    PAUSE(5, "已暂停"),
    OFFLINE(4, "已下线"),
    ;

    private int value;
    private String description;

    BannerState(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<BannerState> valueOf(Integer value) {
        if (null == value) return Optional.empty();
        for (BannerState v : values()) {
            if (v.getValue() == value) return Optional.of(v);
        }
        return Optional.empty();
    }
}

