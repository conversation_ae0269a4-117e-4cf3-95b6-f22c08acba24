package com.kaiba.lib.base.constant.issue;

import com.kaiba.lib.base.domain.issue.PayLevelModel;
import com.kaiba.lib.base.domain.issue.RefuseReasonModel;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * author: lyux
 * date: 19-11-14
 */
public final class IssueConsts {

    /** 问答-即时通讯中, 系统主持人角色的头像 */
    public static final String ISSUE_HOST_AVATAR = "kaiba-logo.png";

    /** 问答-即时通讯中, 系统主持人角色的名字 */
    public static final String ISSUE_HOST_NAME = "开吧小秘";

    /** 问答-即时通讯中, 单个系统主持人可接待的最大单数 */
    public static final int ISSUE_HOST_MAX_SERVICE = 400;


    public static final List<RefuseReasonModel> EXPERT_REFUSE_REASONS = Arrays.asList(
            new RefuseReasonModel(1, "该问题跟车品牌关联度大,我无法回答", true),
            new RefuseReasonModel(2, "临时有事情,怕耽误车主急事", true),
            new RefuseReasonModel(3, "该问题难度较大,超出我的认知范围", true),
            new RefuseReasonModel(4, "由于公司管理因素,现在不方便回答问题", true),
            new RefuseReasonModel(0, "其他", true));

    public static final List<PayLevelModel> ISSUE_PAY_LEVELS_IM_DEFAULT = Arrays.asList(
                new PayLevelModel(1, 3f, TimeUnit.HOURS.toSeconds(12)),
                new PayLevelModel(2, 6f, TimeUnit.HOURS.toSeconds(24)),
                new PayLevelModel(3, 18f, TimeUnit.HOURS.toSeconds(48)));

    public static final List<PayLevelModel> ISSUE_PAY_LEVELS_IM_EXPERT = Arrays.asList(
            new PayLevelModel(1, 5f, TimeUnit.HOURS.toSeconds(12)));

    public static final List<PayLevelModel> ISSUE_PAY_LEVELS_PHONE_DEFAULT = Arrays.asList(
            new PayLevelModel(1, 5f, TimeUnit.MINUTES.toSeconds(10)),
            new PayLevelModel(2, 10f, TimeUnit.MINUTES.toSeconds(20)),
            new PayLevelModel(3, 15f, TimeUnit.MINUTES.toSeconds(30)));

    public static final List<PayLevelModel> ISSUE_PAY_LEVELS_PHONE_EXPERT = Arrays.asList(
            new PayLevelModel(1, 5f, TimeUnit.MINUTES.toSeconds(10)));

}
