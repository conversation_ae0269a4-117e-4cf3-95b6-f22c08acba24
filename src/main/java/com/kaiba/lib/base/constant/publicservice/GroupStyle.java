package com.kaiba.lib.base.constant.publicservice;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import lombok.Getter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/01/05 14:48
 */
@Getter
public enum GroupStyle implements IKbEnumValueGetter {

    /** 分组类型 */
    TWO_PER_ROW(1, "一行两列"),
    FIVE_PER_ROW(2, "一行五列"),
    ;

    private final int value;
    private final String description;

    GroupStyle(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<GroupStyle> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (GroupStyle v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }
}
