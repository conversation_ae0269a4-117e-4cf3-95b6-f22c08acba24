package com.kaiba.lib.base.constant.common;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-07-24
 *
 * 开吧内容通用状态码
 */
@Getter
public enum KbContentState implements IKbEnumValueGetter {

    PREPARE(1, "准备中"),
    SIGNED(2, "已签发"),
    ONLINE(3, "已上线"),
    OFFLINE(4, "已下线"),
    ;

    private final int value;
    private final String description;

    KbContentState(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static Optional<KbContentState> resolveByValue(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (KbContentState v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<KbContentState> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (KbContentState v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<KbContentState> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (KbContentState v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
