package com.kaiba.lib.base.constant.modularization;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * author: lyux
 * date: 19-4-11
 */
public enum AppModulePageType implements IKbEnumValueGetter {

    MAIN_PAGE_TAB1(11, "主页tab1"),
    MAIN_PAGE_TAB2(12, "主页tab2"),
    MAIN_PAGE_TAB3(13, "主页tab3"),
    MAIN_PAGE_TAB4(14, "主页tab4"),
    ;

    private int value;
    private String description;

    AppModulePageType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static Optional<AppModulePageType> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        } else {
            for (AppModulePageType item : values()) {
                if (item.value == value) {
                    return Optional.of(item);
                }
            }
            return Optional.empty();
        }
    }
}
