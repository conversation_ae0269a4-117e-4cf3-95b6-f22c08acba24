package com.kaiba.lib.base.constant.da.sensors;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 24-02-26
 *
 * 神策数据看板配置的状态
 */
@Getter
public enum KanbanDivState {

    PREPARE("配置中"),
    ACTIVE("已启用"),
    READ_ONLY("只读, 不再生成新数据"),
    DISABLED("不再提供数据展示"),
    ;

    private final String description;

    KanbanDivState(String description) {
        this.description = description;
    }

    public static Optional<KanbanDivState> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (KanbanDivState v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<KanbanDivState> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (KanbanDivState v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
