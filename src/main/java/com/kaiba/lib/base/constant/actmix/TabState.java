package com.kaiba.lib.base.constant.actmix;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/11/08 15:43
 **/
public enum TabState implements IKbEnumValueGetter {
    /** 搜索内容分类 */
    SHOW(1, "展示"),
    HIDE(2, "隐藏")
    ;

    private final int value;
    private final String description;

    TabState(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    public static Optional<TabState> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (TabState v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }
}
