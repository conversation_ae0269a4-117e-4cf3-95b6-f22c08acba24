package com.kaiba.lib.base.constant.workorder;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-10-20
 *
 * 工单系统, 回调计时策略.
 */
@Getter
public enum WOCallbackStrategy {

    FIRST("使用周期内第一个同名事件触发回调计时"),
    LAST("使用周期内最后一个同名事件重新触发回调计时"),
    NEW("撤销所有同名事件回调计时,保留最新事件"),
    EACH("每个同名时间均触发回调计时"),
    CANCEL("撤销所有同名事件回调计时"),
    IGNORE("不执行任何动作"),
    ;

    private final String description;

    WOCallbackStrategy(String description) {
        this.description = description;
    }

    public static Optional<WOCallbackStrategy> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WOCallbackStrategy v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<WOCallbackStrategy> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WOCallbackStrategy v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
