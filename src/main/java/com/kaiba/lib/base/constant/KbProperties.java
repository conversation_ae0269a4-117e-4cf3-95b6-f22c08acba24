package com.kaiba.lib.base.constant;

/**
 * author: lyux
 * date: 18-9-15
 */
public final class KbProperties {

    // ------------------------------------------------------------------
    // 电台 ID 常量

    /** 在某些场景下, 为了对齐电台配置, 此为平台的电台标识, 可以理解为占位符. */
    public static final Integer PLATFORM_SITE_ID = 1;

    /** 杭州台 */
    public static final Integer HANGZHOU_SITE_ID = 9;

    // ------------------------------------------------------------------
    // 用户 ID 常量

    /** 为系统设立的用户id */
    public static final Integer SYSTEM_USER_ID = Integer.MAX_VALUE;

    /** 默认的管理员用户id */
    public static final Integer ADMIN_USER_ID = 1;

    /** 开吧机器人客服用户id */
    public static final Integer KAIBA_ROBOT_USER_ID = 4602920;

    // ------------------------------------------------------------------
    // 环境名称

    public static final String ENV_PRODUCT = "product";
    public static final String ENV_RELEASE = "release";
    public static final String ENV_TEST = "test";
    public static final String ENV_DEV = "dev";
    public static final String ENV_LOCAL = "local";

    // ------------------------------------------------------------------

}
