package com.kaiba.lib.base.constant.prize;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * author: lyux
 * date: 19-11-12
 *
 * 奖品核销方式.
 */
public enum DispatchWriteOffType implements IKbEnumValueGetter {

    NONE(1, "不需要核销"), // 不需要核销. 比如卡券类奖品.
    SYSTEM(2, "系统核销"), // 由开吧平台提供的核销相关功能进行核销
    OFFLINE(3, "线下核销"), // 由派奖责任方在线下进行核销
    ;

    private int value;
    private String description;

    DispatchWriteOffType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<DispatchWriteOffType> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (DispatchWriteOffType v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
