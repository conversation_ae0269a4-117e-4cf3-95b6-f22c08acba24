package com.kaiba.lib.base.constant.common;

import lombok.Getter;

import java.util.Collections;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

/**
 * author: lyux
 * date: 23-05-25
 * 
 * 组件化页面的框架结构
 */
@Getter
public enum KbAlign {

    TOP(1 << 1, "上"),
    RIGHT(1 << 2, "右"),
    BOTTOM(1 << 3, "下"),
    LEFT(1 << 4, "左"),
    CENTER(1 << 5, "中"),
    ;

    private final int value;
    private final String description;

    KbAlign(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public boolean isOn(long flag) {
        return (flag & value) == value;
    }

    /** align 的各种组合中, 只有 9 个组合是合法的位置 */
    public static boolean isValidPosition(int value) {
        return VALID_POSITIONS.contains(value);
    }

    public static int createFlag(KbAlign... fields) {
        if (fields == null || fields.length == 0) {
            return 0;
        }
        int flag = 0;
        for (KbAlign field : fields) {
            flag |= field.getValue();
        }
        return flag;
    }

    public static Optional<KbAlign> resolveByValue(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (KbAlign v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<KbAlign> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (KbAlign v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<KbAlign> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (KbAlign v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    /** align 的各种组合中, 只有 9 个组合是合法的位置 */
    public static final Set<Integer> VALID_POSITIONS;
    static {
        Set<Integer> aligns = new HashSet<>(9);
        for (KbAlign v : values()) {
            aligns.add(v.value);
        }
        aligns.add(createFlag(KbAlign.TOP, KbAlign.LEFT));
        aligns.add(createFlag(KbAlign.TOP, KbAlign.RIGHT));
        aligns.add(createFlag(KbAlign.BOTTOM, KbAlign.LEFT));
        aligns.add(createFlag(KbAlign.BOTTOM, KbAlign.RIGHT));
        VALID_POSITIONS = Collections.unmodifiableSet(aligns);
    }

}
