package com.kaiba.lib.base.constant.appwidget;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 2024-09-04
 */
@Getter
public enum WidgetVideoFormat {

    MP4(),
    HLS(),
    ;

    public static Optional<WidgetVideoFormat> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WidgetVideoFormat v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<WidgetVideoFormat> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WidgetVideoFormat v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
