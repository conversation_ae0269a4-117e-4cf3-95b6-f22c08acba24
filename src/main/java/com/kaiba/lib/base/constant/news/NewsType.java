package com.kaiba.lib.base.constant.news;

import java.util.HashMap;
import java.util.Map;

public enum NewsType {

    ROUTE_CONDITION(1, "路况限行"),
    EMERGENCY(2, "突发应急"),
    ATMOSPHERE_ALERT(3, "气象预警"),
    CAR_NEWS(4, "汽车资讯"),
    SOCIAL_HOT(5, "社会热点"),
    SITE_ANNOUNCE(6, "电台公告"),
    TRAFFIC_ROLE(7, "交管法规"),
    OIL_PRICE(8, "油价变动"),
    CAR_RECALL(9, "汽车召回"),
    SAFEGUARD_ROLE(10, "维权法规"),
    AD_POPULARIZE(11, "广告推广"),
    OTHER(12, "其他"),
    WX_MINI_PROGRAM(13, "小程序"),
    CIVILIZED_PROCLAIM(14, "文明宣传"),
    SAFEGUARD_PRIZE(15, "维权颁奖"),
    ASIAN_GAMES_19TH(16, "亚运");

    private Integer value;

    private String name;

    NewsType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName()
    {
        return name;
    }

    public static NewsType valueOf(Integer value) {
        if (null == value) return OTHER;
        for (NewsType v : values()) {
            if (v.getValue() == value) return v;
        }
        return OTHER;
    }

    public static Map<Integer,String> typeMap = new HashMap<>();

    static {
        for (NewsType newsType : NewsType.values()) {
            typeMap.put(newsType.getValue(),newsType.getName());
        }
    }
}
