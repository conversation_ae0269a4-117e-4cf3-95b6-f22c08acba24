package com.kaiba.lib.base.constant.note;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 2022-04-15
 */
@Getter
public enum NoteState implements IKbEnumValueGetter {

    NORMAL(1,"正常帖"),
    SOFT_DELETE(2,"软删除"), // 一般由用户删除会开启此标志.
    AUTHOR_ONLY(3,"仅作者可见"), // 一般由运营人员设置
    ;

    private final int value;
    private final String description;

    NoteState(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static Optional<NoteState> resolveByValue(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (NoteState v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
