package com.kaiba.lib.base.constant.wx;

import lombok.Getter;

/**
 * author: lyux
 * date: 2022-05-11
 */
@Getter
public enum WXPlatform {

    KAIBA("杭州开吧传媒有限公司", "wxc20ac493c21d74db", null, "gh_d057368e5ec1"),
    INFO_TECH_918("杭州交通久一点吧信息科技有限公司", "wxb550a5a29beaf0a7", null, "gh_b723463c376a"),
    ;

    private final String name;
    private final String appId;
    private final String secret;
    private final String originId;

    WXPlatform(String name, String appId, String originId, String secret) {
        this.name = name;
        this.appId = appId;
        this.secret = secret;
        this.originId = originId;
    }
}
