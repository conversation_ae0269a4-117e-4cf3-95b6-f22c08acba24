package com.kaiba.lib.base.constant.route;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 2020-03-24
 */
@Getter
public enum KbRouteRuleState implements IKbEnumValueGetter {

    ENABLED(1, "启用"),
    DISABLED(2, "停用"),
    ;

    private final int value;
    private final String description;

    KbRouteRuleState(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static Optional<KbRouteRuleState> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (KbRouteRuleState v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
