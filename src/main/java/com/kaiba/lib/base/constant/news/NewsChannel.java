package com.kaiba.lib.base.constant.news;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 2024-09-05
 */
@Getter
public enum NewsChannel {

    /** 杭州电视台内部单位 **/
    HTV1(NewsChannelType.HCRT_CHANNEL, "综合频道"),
    HTV2(NewsChannelType.HCRT_CHANNEL, "明珠电视"),
    HTV3(NewsChannelType.HCRT_CHANNEL, "生活频道"),
    HTV4(NewsChannelType.HCRT_CHANNEL, "影视频道"),
    HTV5(NewsChannelType.HCRT_CHANNEL, "青少体育频道"),
    FM_89(NewsChannelType.HCRT_CHANNEL, "杭州之声"),
    FM_1054(NewsChannelType.HCRT_CHANNEL, "西湖之声"),
    FM_918(NewsChannelType.HCRT_CHANNEL, "杭州交通经济广播"),
    FM_907(NewsChannelType.HCRT_CHANNEL, "杭州城市资讯广播"),
    AM_954(NewsChannelType.HCRT_CHANNEL, "杭州老朋友广播"),
    RHFZZX(NewsChannelType.HCRT_CHANNEL, "融合发展中心"),
    MOBILE_MEDIA(NewsChannelType.HCRT_CHANNEL, "杭州移动电视"),
    HPAG(NewsChannelType.HCRT_CHANNEL, "杭州演艺集团"),
    OTHER(NewsChannelType.HCRT_CHANNEL, "其他"),

    /** 杭州区县市 **/
    HZ_SC(NewsChannelType.HZ_COUNTY, "上城区"),
    HZ_GS(NewsChannelType.HZ_COUNTY, "拱墅区"),
    HZ_XH(NewsChannelType.HZ_COUNTY, "西湖区"),
    HZ_BJ(NewsChannelType.HZ_COUNTY, "滨江区"),
    HZ_QT(NewsChannelType.HZ_COUNTY, "钱塘区"),
    HZ_LP(NewsChannelType.HZ_COUNTY, "临平区"),
    HZ_XS(NewsChannelType.HZ_COUNTY, "萧山区"),
    HZ_YH(NewsChannelType.HZ_COUNTY, "余杭区"),
    HZ_FY(NewsChannelType.HZ_COUNTY, "富阳区"),
    HZ_LA(NewsChannelType.HZ_COUNTY, "临安区"),
    HZ_TL(NewsChannelType.HZ_COUNTY, "桐庐县"),
    HZ_CA(NewsChannelType.HZ_COUNTY, "淳安县"),
    HZ_JD(NewsChannelType.HZ_COUNTY, "建德市"),

    ;

    private final NewsChannelType type;
    private final String name;

    NewsChannel(NewsChannelType type, String name) {
        this.type = type;
        this.name = name;
    }

    public static Optional<NewsChannel> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (NewsChannel v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
