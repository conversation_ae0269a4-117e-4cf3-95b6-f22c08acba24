package com.kaiba.lib.base.constant.apphome;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-05-25
 *
 * 组件化页面场景
 */
@Getter
public enum AppHomeTabType {

    TAB_HOME("首页组件化", "home"),
    TAB_CIRCLE("新圈子", "circle"),
    TAB_PROGRAM_CIRCLE("节目单和老圈子", "program_circle"),
    TAB_PROGRAM_LIST("节目单", "program_list"),
    TAB_MESSAGE("消息", null),
    TAB_ACTIVITY("活动", "act_mix"),
    TAB_SERVICE("服务页3.0版", "service"),
    TAB_SERVICE_2("服务页2.0版", "service_2"),
    TAB_ROAD("路况", "road_condition"),
    TAB_SELF("我的", "personal"),
    TAB_YOUZAN("918小卖铺", null),
    TAB_WEB("H5", null),
    TAB_NOTICE("整页公告", null),
    TAB_SERVICE_OLD("服务页1.0版", "service_old"),
    TAB_HZ_POLITICS("杭州时政", null),
    ;

    private final String description;

    /** 默认tabKey值 */
    private final String defaultTabKey;

    AppHomeTabType(String description, String defaultTabKey) {
        this.description = description;
        this.defaultTabKey = defaultTabKey;
    }

    public static Optional<AppHomeTabType> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (AppHomeTabType v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<AppHomeTabType> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (AppHomeTabType v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
