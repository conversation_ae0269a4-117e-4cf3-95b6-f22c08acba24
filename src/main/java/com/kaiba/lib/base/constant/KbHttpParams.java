package com.kaiba.lib.base.constant;

/**
 * author: lyux
 * date: 2020-04-24
 */
public enum KbHttpParams {

    /** 电台 id, siteId. */
    SITE_ID("siteId", Integer.class),

    /** 地市编码, source. */
    SOURCE("source", Integer.class),

    /** 用户 id, userId. */
    USER_ID("userId", String.class),

    /** 令牌, token. */
    TOKEN("token", String.class),

    /** 签名, 用于某些接口的数据校验 */
    KB_SIGN("kbSign", String.class),

    /** 签名类型, 用于某些接口的数据校验 */
    KB_SIGN_TYPE("kbSignType", Integer.class),

    /** 签名时效检验参数, 单位毫秒, 用于某些接口的数据校验 */
    KB_SIGN_TIME("kbSignTime", Long.class),

    ;

    private String name;
    private Class<?> dataType;

    KbHttpParams(String name, Class<?> dataType) {
        this.name = name;
        this.dataType = dataType;
    }

    public String getName() {
        return name;
    }

    public Class<?> getDataType() {
        return dataType;
    }
}
