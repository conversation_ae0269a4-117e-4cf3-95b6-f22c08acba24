package com.kaiba.lib.base.constant.prize;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * author: lyux
 * date: 19-11-12
 *
 * 奖品类型.
 */
public enum PrizeType implements IKbEnumValueGetter {

    CASH(1, "现金"),
    COUPON(2, "奖券"),
    ITEM(3, "实物"),
    COUPON_PAY(11, "奖券-由用户购得"),
    ITEM_PAY(12, "实物-由用户购得"),
    CASH_CONFIRM(21, "现金-进一步确认后方可入账"),
    ;

    private final int value;
    private final String description;

    PrizeType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<PrizeType> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (PrizeType v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
