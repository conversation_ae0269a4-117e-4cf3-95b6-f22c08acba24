package com.kaiba.lib.base.constant;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 18-9-29
 */
@Getter
public enum KbEndpoint implements IKbEnumValueGetter {

    /** 开吧后端 */
    BACKEND(1),

    /** 开吧主应用 */
    KAIBA(11,
            "com.hz.czfw.app",
            "cn.czfw.HangZhou",
            "com.kaiba.app"),

    /** 济南1031车主服务应用 */
    @Deprecated
    CZFW(12,
            "com.fmczfw.app",
            "cn.czfw.fmczfw",
            null),

    /** 开吧主应用 flutter 版 */
    KAIBA_FLUTTER(16,
            "com.hz.czfw.app",
            "cn.czfw.HangZhou",
            "com.kaiba.app"),

    /** 汽车问答-技师端 */
    @Deprecated
    EXPERT(21,
            "com.kaiba315.issue.expert",
            "cn.czfw.expert",
            null),

    /** 律师端 */
    @Deprecated
    LAWYER(22,
            "com.kaiba315.issue.expert",
            "cn.czfw.expert",
            null),

    /** 小程序-节目互动 */
    WX_PROGRAM(31),

    /** 小程序-维权 */
    WX_SAFEGUARD(32),

    /** 小程序-报名 */
    WX_SIGN_UP(33),

    /** 小程序-商家中心 */
    WX_MERCHANT(34),

    /** 小程序-开吧 */
    WX_KAIBA(35),

    /** 小程序-开吧商城 */
    WX_MALL(36),

    /** 视频直播录制端 */
    @Deprecated
    LIVE_RECORD(51, "live.app.kaiba315.com", "cn.czfw.kblive", null),

    /** 管理后台 1.0 */
    @Deprecated
    CMS(61),

    /** 管理后台 2.0 */
    CMS_PLATFORM(71),

    /** 管理后台 3.0*/
    CMS_PLATFORM_V3(72),

    /** PHP7 laravel 后台-临时活动管理后台 */
    @Deprecated
    CMS_ACTIVITY(81),

    /** PHP7 laravel 后台-新商城管理后台 */
    @Deprecated
    CMS_MALL(82),

    /** PHP7 laravel 后台-维权商家管理后台 */
    CMS_PARTNER(101),

    /** H5 前端页面 */
    H5(201),

    /** H5 临时活动框架的前端页面 */
    @Deprecated
    H5_ACTIVITY(211),

    /** H5 新商城的前端页面 */
    H5_MALL(212),

    /** 第三方合作伙伴 */
    THIRD_PARTY(901),
    ;

    private final int value;
    private final String androidPackageName;
    private final String iosBundleId;
    private final String ohosPackageId;

    KbEndpoint(int value) {
        this.value = value;
        this.androidPackageName = null;
        this.iosBundleId = null;
        this.ohosPackageId = null;
    }

    KbEndpoint(int value, String androidPackageName, String iosBundleId, String ohosPackageId) {
        this.value = value;
        this.androidPackageName = androidPackageName;
        this.iosBundleId = iosBundleId;
        this.ohosPackageId = ohosPackageId;
    }

    public static Optional<KbEndpoint> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (KbEndpoint v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
