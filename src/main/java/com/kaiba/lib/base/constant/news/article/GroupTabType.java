package com.kaiba.lib.base.constant.news.article;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 24-09-26
 *
 * tab 类型.
 */
@Getter
public enum GroupTabType {

    ARTICLE_GROUP("文章列表"),
    NOTE_THREAD("帖子列表"),
    IMAGE_BANNER("图片轮播"),
    ARTICLE_DETAIL("文章详情"),
    TAB_CONTAINER("分组容器"),
    ;

    private final String description;

    GroupTabType(String description) {
        this.description = description;
    }

    public static Optional<GroupTabType> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (GroupTabType v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<GroupTabType> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (GroupTabType v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
