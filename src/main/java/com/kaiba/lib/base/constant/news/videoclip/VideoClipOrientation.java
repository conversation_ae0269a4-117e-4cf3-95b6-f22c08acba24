package com.kaiba.lib.base.constant.news.videoclip;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/07/04 10:32
 **/
@Getter
@Deprecated
public enum VideoClipOrientation implements IKbEnumValueGetter {
    /** 短视频状态 */
    HORIZONTAL(1, "横向"),
    VERTICAL(2, "竖向")
    ;

    private final int value;
    private final String description;

    VideoClipOrientation(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<VideoClipOrientation> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (VideoClipOrientation item : values()) {
            if (item.value == value) {
                return Optional.of(item);
            }
        }
        return Optional.empty();
    }
}
