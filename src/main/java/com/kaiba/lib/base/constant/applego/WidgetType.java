package com.kaiba.lib.base.constant.applego;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-05-25
 * 
 * 组件化页面的框架结构
 */
@Getter
public enum WidgetType {

    // 展示控件
    TEXT("文本", true),
    IMAGE("图片", true),
    VIDEO("视频", true),
    PREF_HELIUM("图片主副标控件 helium 版 (头图+主副标题)", true),

    // 模板控件
    BANNER("Banner", true),
    BANNER_SHOW_CASE_A("轮播橱窗A款", true),
    ICON_GRID("图标网格", true),
    TEXT_TICKER("滚动词条", true),
    ACTION_LIST("通用列表", true),
    NEO_ARTICLE_BANNER("文章轮播", true),
    NEO_ARTICLE_LIST("文章列表", true),
    CIRCLE_TOPIC("圈子话题", 1440, -1, true),

    // 业务控件
    BI_PANEL_REVEAL("爆料台区块", 654, 436, false),
    BI_PANEL_SAFEGUARD("维权区块", 654, 436, false),
    BI_PANEL_VIDEO_LIVE("视频直播区块", 654, 436, false),

    // 业务控件
    PROGRAM_GALLERY("节目画廊", 1440, null, false),
    LUCKY_ME("活动福利", 1440, null, false),
    NEWS_LIST("资讯列表", 1440, -1, false),
    POLICY_NEWS_TICKER("时政滚动词条", 1440, -1, false),
    REVEAL_LIST("爆料台列表", 1440, -1, false),
    REVEAL_TICKER("爆料台词条", 1440, null, false),
    YOUZAN_918("有赞918小卖铺", 1440, -1, false),
    HTV1_PROGRAMME_LIST("综合频道栏目列表", 1440, -1, false),
    CIRCLE("吧友圈主页", 1440, -1, false),
    CIRCLE_TICKER("圈子热帖", 1440, -1, false),
    NOTE_ADD("发帖控件", 1440, null, false),
    VIDEO_LIVE("视频直播",1440,-1,false),
    ACT_MIX_LIST("活动列表", 1440, -1, true),
    ACT_MIX_TAB_BANNER("活动 TAB 轮播", 1440, null, true),

    HZ_HEADLINE("杭州首页要闻", 1440, null, true),
    HOME_RECOMMEND_TOP("首页竖向推荐置顶", 1440, null, false),
    HOME_RECOMMEND_LIST("首页竖向推荐列表", 1440, -1, true),

    ;

    private final String description;
    private final Integer width;
    private final Integer height;
    private final boolean requireId;

    WidgetType(String description, Integer width, Integer height, boolean requireId) {
        this.description = description;
        this.width = width;
        this.height = height;
        this.requireId = requireId;
    }

    WidgetType(String description, boolean requireId) {
        this(description, null, null, requireId);
    }

    public static Optional<WidgetType> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WidgetType v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<WidgetType> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WidgetType v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
