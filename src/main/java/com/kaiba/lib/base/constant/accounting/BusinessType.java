package com.kaiba.lib.base.constant.accounting;

import java.util.HashMap;

public enum BusinessType {

    RECHARGE("recharge", "账户充值", false),
    INHERITANCE("inheritance", "旧版资金转入", false),
    SITE_RECHARGE("site_recharge", "电台账户充值", false),
    RECHARGE_REFUND("recharge_refund", "退款给用户", false),
    REFUND_WITHDRAW("refund_withdraw", "退款提现", true),
    TRANSACTION_REVERSE("transaction_reverse", "交易回退", false),

    USER_WITHDRAW("user_withdraw", "用户提现", true),
    EXPERT_WITHDRAW("expert_withdraw", "技师提现", true),
    WITHDRAW_APPROVE("withdraw_approve", "提现通过", true),
    WITHDRAW_DISAPPROVE("withdraw_disapprove", "提现驳回", true),

    WRITE_OFF("write_off", "账户核销", false),
    WRITE_OFF_APPROVE("write_off_approve", "账户核销通过", false),
    WRITE_OFF_DISAPPROVE("write_off_disapprove", "账户核销驳回", false),

    ISSUE("issue", "咨询提问", false),
    ISSUE_REWARD("issue_reward", "打赏技师", true),
    ISSUE_REFUND("issue_refund", "问答退款", false),
    ISSUE_PAY_EXPERT("issue_pay_expert", "问答结束, 付款给技师", false),

    @Deprecated
    PRIZE_PAY("prize_pay", "奖品付款", true),
    PRIZE_REFUND("prize_refund", "奖品退款", true),
    IRREGULAR_ACTIVITY_PAY("irregular_activity_pay","活动支付",true),
    IRREGULAR_ACTIVITY_PAY_UNIQUE("irregular_activity_pay_unique","活动支付",false),
    @Deprecated
    ACTIVITY_RED("activity_red","活动红包",true),

    PRIZE_CONFIRM_TRANSACTION("prize_confirm_transaction","幸福消费券支付用户",false),

    RUSH_RED("rush_red", "抽奖红包", false),
    RUSH_RED_FUND("rush_red_fund", "电台账户填充红包账户", true),
    RUSH_RED_REFUND("rush_red_refund", "红包余额退款至电台账户", false),
    RUSH_PAY("rush_pay", "抢购奖品付款", false),
    RUSH_PAY_REFUND("rush_pay_refund", "抢购奖品退款", false),

    ACTIVITY_FUND("activity_fund", "电台账户填充活动账户", true),
    ACTIVITY_REFUND("activity_refund", "活动余额退款至电台账户", false),

    PROGRAM_REWARD("program_reward", "节目打赏", true),
    SAFEGUARD_REWARD("safeguard_reward", "维权打赏", true),
    NEWS_REWARD("news_reward", "资讯打赏", true),
    SITE_SUMMARY("site_summary", "电台账户汇总",true),

    FEIMA_PAY_DEPOSIT("feima_pay_deposit","飞马救援支付押金",false),
    FEIMA_PAY_SERVICE("feima_pay_service","飞马救援支付服务",false),
    FEIMA_REFUND_DEPOSIT("feima_refund_deposit","飞马救援押金退还",false),
    FEIMA_FORFEIT_DEPOSIT("feima_forfeit_deposit","飞马救援押金扣除",false),

    CHEDIANDIAN_TICKET_PAY("chediandan_ticket_pay","车点点购买洗车券",false),
    CHEDIANDIAN_TICKET_REFUND("chediandian_ticket_refund","车点点洗车券退款",false),

    HZ_NO_VIOLATION_2023_3RD_AWARD("hz_no_violation_2023_3rd_award", "杭州无违法三期派奖", false),
    HZ_NO_VIOLATION_2023_3RD_AWARD_FUND("hz_no_violation_2023_3rd_award_fund", "杭州无违法三期, 电台账户填充派奖账户", true),
    HZ_NO_VIOLATION_2023_3RD_AWARD_REFUND("hz_no_violation_2023_3rd_award_refund", "杭州无违法三期, 派奖账户回退到电台账户", true),

    HCRT_HAPPY_POINT_EXCHANGE("hcrt_happy_point_exchange","文广幸福吧积分兑换",false),
    HCRT_HAPPY_PRIZE_POOL("hcrt_happy_prize_pool","文广幸福吧奖池瓜分",false),
    HCRT_HAPPY_INVITE_REWARD("hcrt_happy_invite_reward","文广幸福吧邀请奖励",false),


    ;

    private final String type;
    private final String description;
    private final boolean duplicated; //是否允许业务标识重复

    BusinessType(String type, String description, Boolean duplicated) {
        this.type = type;
        this.description = description;
        this.duplicated = duplicated;
    }

    public String getType() {
        return this.type;
    }

    public String getDescription() {
        return this.description;
    }

    public boolean getDuplicated() {
        return this.duplicated;
    }

    @Override
    public String toString() {
        return type;
    }

    private static HashMap<String, BusinessType> map = new HashMap<>();

    static {
        for (BusinessType businessType : BusinessType.values()) {
            map.put(businessType.getType(), businessType);
        }
    }

    public static BusinessType valueOfType(String type) {
        return map.get(type);
    }

}
