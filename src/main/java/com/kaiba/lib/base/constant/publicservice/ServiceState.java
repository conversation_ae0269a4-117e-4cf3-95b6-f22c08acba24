package com.kaiba.lib.base.constant.publicservice;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import lombok.Getter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/01/05 14:48
 */
@Getter
public enum ServiceState implements IKbEnumValueGetter {

    /** 服务状态 */
    ENABLE(1, "启用"),
    DISABLE(2, "禁用"),
    ;

    private final int value;
    private final String description;

    ServiceState(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<ServiceState> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (ServiceState v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }
}
