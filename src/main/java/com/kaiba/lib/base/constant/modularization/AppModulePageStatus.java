package com.kaiba.lib.base.constant.modularization;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/4/3
 *
 * 组件化Tab状态
 */
public enum AppModulePageStatus implements IKbEnumValueGetter {

    BETA(1, "待上线"),
    ONLINE(2, "已上线"),
    CANCEL(3, "已废弃"),
    ;

    static {
        BETA.next = statuses(ONLINE, CANCEL);
        ONLINE.next = statuses(CANCEL);
        CANCEL.next = statuses();
    }

    private int value;
    private String description;
    private AppModulePageStatus[] next;

    AppModulePageStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public boolean isStatusChangeAllowed(AppModulePageStatus nextStatus) {
        for (AppModulePageStatus s : this.next) {
            if (s == nextStatus) return true;
        }
        return false;
    }

    public static Optional<AppModulePageStatus> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        } else {
            for (AppModulePageStatus item : values()) {
                if (item.value == value) {
                    return Optional.of(item);
                }
            }
            return Optional.empty();
        }
    }

    private static AppModulePageStatus[] statuses(AppModulePageStatus... statuses) {
        return statuses;
    }

    public static final Integer[] ALL_STATUS_ARRAY = new Integer[] {
            BETA.getValue(),
            ONLINE.getValue(),
            CANCEL.getValue()
    };

    public static final Integer[] ACTIVE_STATUS_ARRAY = new Integer[] {
            BETA.getValue(),
            ONLINE.getValue()
    };

    public static final Integer[] ONLINE_STATUS_ARRAY = new Integer[] {
            ONLINE.getValue()
    };

    public static final Integer[] BETA_STATUS_ARRAY = new Integer[] {
            BETA.getValue()
    };

}
