package com.kaiba.lib.base.constant.workorder;

import com.kaiba.lib.base.domain.workorder.WOConfigRole;
import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-10-20
 *
 * 工单系统, 内容可见性配置中的用户分类方式.
 */
@Getter
public enum WOAccessBy {

    /** 根据案件关联的 提问者/处理者/参与者. 举例: c:c; c:m; o:r; c:ra; c:p . */
    BY_CASE("根据案件关联", "c"),

    /** 根据身份 {@link WOIdentity} 分类. 举例: i:CLIENT, i:RESOLVER, i:VIEWER */
    BY_IDENTITY("根据身份", "i"),

    /** 根据 {@link WOConfigRole} 配置的角色分类. 举例: r:governor, r:client */
    BY_ROLE("根据角色", "r"),

    /** 根据用户组, 一般用做反向排除. 举例: t:transport_bu, t:education_bu */
    BY_TEAM("根据用户分组", "t"),

    /** 根据用户 ID, 一般用做反向排除. 举例: u:1037278 */
    BY_USER("根据用户ID", "u"),

    ;

    private final String description;
    private final String prefix;

    WOAccessBy(String description, String prefix) {
        this.description = description;
        this.prefix = prefix;
    }

    public String by(String subject) {
        return prefix + ":" + subject;
    }

    public static Optional<WOAccessBy> resolveByPrefix(String prefix) {
        if (null == prefix) {
            return Optional.empty();
        }
        for (WOAccessBy v : values()) {
            if (prefix.equals(v.getPrefix())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<WOAccessBy> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WOAccessBy v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<WOAccessBy> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WOAccessBy v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static boolean isValidCaseRelationType(String ownerType) {
        return CASE_R_CLIENT.equals(ownerType)
                || CASE_R_MANAGER.equals(ownerType)
                || CASE_R_RESOLVER.equals(ownerType)
                || CASE_R_RESOLVER_ALL.equals(ownerType)
                || CASE_R_PARTICIPANT.equals(ownerType);
    }

    /** 案件的提问者 */
    public static final String CASE_R_CLIENT = "c";
    /** 案件的主持者 */
    public static final String CASE_R_MANAGER = "m";
    /** 案件的当前处理者 */
    public static final String CASE_R_RESOLVER = "r";
    /** 案件的当前及过往处理者 */
    public static final String CASE_R_RESOLVER_ALL = "ra";
    /** 案件的当前参与者, 包括提问者, 主持者, 当前和过往处理者, 以及当前响应者 */
    public static final String CASE_R_PARTICIPANT = "p";

}
