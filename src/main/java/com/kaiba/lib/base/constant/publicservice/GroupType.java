package com.kaiba.lib.base.constant.publicservice;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import lombok.Getter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/01/05 14:48
 */
@Getter
public enum GroupType implements IKbEnumValueGetter {

    /** 分组类型 */
    MAIN_GROUP(1, -1, "首页分组"),
    INNER_GROUP(2, -1, "内页分组"),
    RECOMMEND(3, 1, "推荐分组"),
    FREQUENTLY(4, 1, "默认常用"),
    ;

    private final int value;
    /** 分组类型可存在的最大数量 -1为不限制 */
    private final int total;
    private final String description;

    GroupType(int value, int total, String description) {
        this.value = value;
        this.total = total;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<GroupType> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (GroupType v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }
}
