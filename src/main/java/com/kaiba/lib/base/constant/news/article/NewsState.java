package com.kaiba.lib.base.constant.news.article;

import lombok.Getter;

import java.util.*;

/**
 * author: lyux
 * date: 23-07-19
 *
 * 资讯文章状态.
 */
@Getter
public enum NewsState {

    DRAFT("草稿"),
    SIGNED("已签发"), // 列表不可见, 详情可进入
    ONLINE("已上线"), // 列表可见, 详情可进入
    ARCHIVED("已下架"),
    ;

    private final String description;
    private Set<NewsState> nextStates;

    static {
        DRAFT.nextStates = next(SIGNED, ONLINE, ARCHIVED);
        SIGNED.nextStates = next(DRAFT, ONLINE, ARCHIVED);
        ONLINE.nextStates = next(DRAFT, SIGNED, ARCHIVED);
        ARCHIVED.nextStates = next(DRAFT);
    }

    NewsState(String description) {
        this.description = description;
    }

    public boolean isStateChangeAllowed(NewsState next) {
        return next != null && nextStates.contains(next);
    }

    public static Optional<NewsState> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (NewsState v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<NewsState> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (NewsState v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    private static Set<NewsState> next(NewsState... states) {
        if (states == null || states.length == 0) {
            return Collections.emptySet();
        } else {
            return new HashSet<>(Arrays.asList(states));
        }
    }

}
