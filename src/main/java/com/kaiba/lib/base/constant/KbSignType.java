package com.kaiba.lib.base.constant;

import java.time.Duration;
import java.util.Optional;

public enum KbSignType implements IKbEnumValueGetter {

    NONE(0, Duration.ofMinutes(3), ""),
    ACCOUNT(1, Duration.ofMinutes(3)),
    LOGIN(2, Duration.ofMinutes(10),"sM4AOVdWfPEDxkXGE8VMCPGGVi4CVM0P37wVUCFvkVAy_90u5h9nbSlYy3-Sl-HhTdfl2fzFy1AOcHKP7"),
    CALLBACK(3, Duration.ofMinutes(10),"90c3c8389c49173c45439e041f4261ce"),
    THIRD_PARTY(4, Duration.ofMinutes(5)), // 第三方对接
    WX_WEB_AUTH(5, Duration.ofMinutes(3)), // 微信打开的网页鉴权
    CID(6, Duration.ofMinutes(2)), // 调用方 cid 的前 8 位作为盐值
    API(7, Duration.ofMinutes(2)), // 由具体接口的注解指定盐值
    APP_ENFORCE(8, Duration.ofMinutes(2)), // 只能由客户端产生的盐值, 不需要登录. app_fixed+type6.
    APP_USER_ENFORCE(9, Duration.ofMinutes(2)), // 只能由客户端产生的盐值, 必须登录. app_fixed+type6+type1.
    ;

    private final int value;
    private final Duration timeLimit; // 签名有效期
    private final String salt; // 签名盐值

    KbSignType(int value, Duration timeLimit, String salt) {
        this.value = value;
        this.timeLimit = timeLimit;
        this.salt = salt;
    }

    KbSignType(int value, Duration timeLimit) {
        this(value, timeLimit, "");
    }

    @Override
    public int getValue() {
        return value;
    }

    public String getSalt() {
        return salt;
    }

    public Duration getTimeLimit() {
        return timeLimit;
    }

    public long getTimeLimitMillis() {
        return timeLimit.toMillis();
    }

    public static Optional<KbSignType> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (KbSignType v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }
}
