package com.kaiba.lib.base.constant;

/**
 * author: lyux
 * date: 2020-04-24
 */
public interface KbHeader {

    /** 电台 id, siteId. */
    String KB_SITE_ID = "kb_sid";

    /** 地市编码, source. */
    String KB_SOURCE = "kb_src";

    /** 用户 id, userId. */
    String KB_USER_ID = "kb_uid";

    /** 令牌, token. */
    String KB_TOKEN = "kb_tkn";

    /** 设备编号, cid. */
    String KB_CID = "kb_cid";

    /** 设备编号, accessId. */
    String KB_AID = "kb_aid";

    /** 渠道号, channel. */
    String KB_CNL = "kb_cnl";

    /** app version code, 应用版本号. 如 30201. */
    String KB_VC = "kb_vc";

    /** device origin, 设备类型. 如 Android, iOS, ohos鸿蒙  */
    String KB_DO = "kb_do";

    /** device brand, 设备品牌型号. 如 Meizu Pro6 / iPhone 10,1. */
    String KB_DB = "kb_db";

    /** device version, 设备操作系统版本. 如  = Android) 4.4.1 /  = iOS) 12.1. */
    String KB_DV = "kb_dv";

    /** operating system, 载体类型. 如 Android, WXTiny, Browser. */
    String KB_OS = "kb_os";

    /** operation system version, 载体版本号. 如 Android 系统版本, 浏览器系统版本. */
    String KB_OV = "kb_ov";

    /** end point, 端类型. 如 车主端, 技师端, 节目互动小程序. */
    String KB_EP = "kb_ep";

    /** 若识别请求带有此 header, 则将返回调试信息 kbDebug 字段. */
    String KB_DEBUG = "kb_dbg";

    /**
     * 微服务内部 header: ip address, 访问者 ip.
     * 此 header 仅限微服务内部传递使用, 为了应对服务链路过长时, 下游 remote ip 无法正确获取的问题.
     */
    String KB_IP = "kb_ip";

}
