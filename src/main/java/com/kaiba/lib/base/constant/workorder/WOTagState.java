package com.kaiba.lib.base.constant.workorder;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-10-20
 *
 * 工单系统, 案件标签状态.
 */
@Getter
public enum WOTagState {

    AS_IS("遵循默认"),
    SHOW("向C端展示"),
    HIDE("向C端隐藏"),
    ;

    private final String description;

    WOTagState(String description) {
        this.description = description;
    }

    public static Optional<WOTagState> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WOTagState v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<WOTagState> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WOTagState v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
