package com.kaiba.lib.base.constant.minitheatre;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-10-20
 */
@Getter
public enum MTEpisodeOrient {

    PORTRAIT("竖屏模式"),
    LANDSCAPE("横屏模式"),
    ;

    private final String description;

    MTEpisodeOrient(String description) {
        this.description = description;
    }

    public static Optional<MTEpisodeOrient> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (MTEpisodeOrient v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<MTEpisodeOrient> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (MTEpisodeOrient v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
