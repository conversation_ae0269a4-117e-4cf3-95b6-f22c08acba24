package com.kaiba.lib.base.constant;

/**
 * author: lyux
 * date: 18-7-19
 */
public final class Values {

    public static final int TRUE = 1;
    public static final int FALSE = 0;

    // ------------------------------------------------

    public static final int GENDER_OTHER = 0;
    public static final int GENDER_MALE = 1;
    public static final int GENDER_FEMALE = 2;

    // ------------------------------------------------

    public static final String DEVICE_ANDROID = "Android";
    public static final String DEVICE_IOS = "iOS";
    public static final String DEVICE_OHOS = "ohos";

    // ------------------------------------------------

    public static final int GRAIN_UNSPECIFIED = -1;
    public static final int GRAIN_BASIC = 200;
    public static final int GRAIN_SUMMARY = 400;
    public static final int GRAIN_DETAIL = 600;
    public static final int GRAIN_ALL = 800;

    // ------------------------------------------------


    public static boolean isTrue(Integer is) {
        return null != is && Values.TRUE == is;
    }

    public static boolean isTrue(int is) {
        return Values.TRUE == is;
    }

    public static boolean isFalse(Integer is) {
        return null != is && Values.FALSE == is;
    }

    public static boolean isFalse(int is) {
        return Values.FALSE == is;
    }

    public static boolean isMale(Integer is) {
        return null != is && Values.GENDER_MALE == is;
    }

    public static boolean isMale(int is) {
        return Values.GENDER_MALE == is;
    }

    public static boolean isFemale(Integer is) {
        return null != is && Values.GENDER_FEMALE == is;
    }

    public static boolean isFemale(int is) {
        return Values.GENDER_FEMALE == is;
    }

    public static boolean isValidGender(Integer gender) {
        return gender == null
                || gender == Values.GENDER_OTHER
                || gender == Values.GENDER_MALE
                || gender == Values.GENDER_FEMALE;
    }

    // ------------------------------------------------


    private Values() {}

}
