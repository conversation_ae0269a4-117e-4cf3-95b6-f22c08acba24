package com.kaiba.lib.base.constant;

/**
 * author: lyux
 * date: 18-10-16
 */
public enum KbAmqp {

    USER_MESSAGE("kaiba-message-user", "kaiba-message-user", "kaiba.message.user"),
    ISSUE_TASK("kaiba-issue-task", "kaiba-issue-task", "kaiba.issue.task"),
    SEARCH_CONTENT("kaiba-search-content", "kaiba-search-content", "kaiba.search.content"),
    DYNAMIC_CHANNEL("kaiba-dynamic-channel", "kaiba-dynamic-channel", "kaiba.dynamic.channel"),
    DATA_V("kaiba-datav", "kaiba-datav", "kaiba.datav"),
    NOTE_CHANNEL("kaiba-note-channel","kaiba-note-channel","kaiba.note.channel"),
    NEWS_CHANNEL("kaiba-news-channel","kaiba-news-channel","kaiba.news.channel"),
    ;

    private String queue;
    private String exchange;
    private String routing;

    KbAmqp(String queue, String exchange, String routing) {
        this.queue = queue;
        this.exchange = exchange;
        this.routing = routing;
    }

    public String getQueue() {
        return queue;
    }

    public String getExchange() {
        return exchange;
    }

    public String getRouting() {
        return routing;
    }
}
