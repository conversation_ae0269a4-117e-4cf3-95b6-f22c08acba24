package com.kaiba.lib.base.constant.news;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 2024-09-05
 */
@Getter
public enum HCRTProgramme {

    HTV1_NEWS(NewsChannel.HTV1, "杭州新闻联播"),
    NEWS_60M(NewsChannel.HTV1, "新闻60分"),
    FOCUS_TODAY(NewsChannel.HTV1, "今日关注"), // 译自央视同名栏目
    FINANCE_FRONT_LINE(NewsChannel.HTV1, "财经第一线"),
    POPULAR_WILL_OBSERVE(NewsChannel.HTV1, "民情观察室"),
    WE_ROUND_TABLE(NewsChannel.HTV1, "我们圆桌会"),
    GOVERNOR_CITIZEN_TALK(NewsChannel.HTV1, "公述民评"),

    HTV2_NEWS(NewsChannel.HTV2, "明珠新闻"),
    PLEASANT_TEA_HOUSE(NewsChannel.HTV2, "开心茶馆"), // 译名来自栏目百度百科
    ALIUTOU_NEWS_REPORT(NewsChannel.HTV2, "阿六头说新闻"), // 译名来自栏目百度百科
    PEACEMAKER(NewsChannel.HTV2, "和事佬"), // 译名来自栏目百度百科
    COMMUNITY_BIG_DEAL(NewsChannel.HTV2, "小区大事"), // 译名来自栏目百度百科
    POLICE_41(NewsChannel.HTV2, "警界41"),
    HEALTHY_MOMENT(NewsChannel.HTV2, "健康生活圈"), // 官方译名

    CITIZEN_SUPERVISOR(NewsChannel.HTV3, "市民监督团"),

    HOT_SPOT_TODAY(NewsChannel.HTV4, "今日新看点"),
    CHEER_BABY(NewsChannel.HTV4, "元气宝贝"),

    STAGE_PASSION(NewsChannel.HTV5, "爱上舞台"),
    GYM_ANY_TIME(NewsChannel.HTV5, "随时健身房"),
    DATE_WITH_SCHOOL(NewsChannel.HTV5, "名校有约"),

    HZ_CAR_TALK(NewsChannel.FM_918, "我的汽车有话说"),

    CITIZEN_HOT_LINE(NewsChannel.FM_89, "民情热线"),

    CITIZEN_VOICE_EXPRESS(NewsChannel.MOBILE_MEDIA, "民生快车"),
    ;

    private final NewsChannel channel;
    private final String name;

    HCRTProgramme(NewsChannel channel, String name) {
        this.channel = channel;
        this.name = name;
    }

    public static Optional<HCRTProgramme> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (HCRTProgramme v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
