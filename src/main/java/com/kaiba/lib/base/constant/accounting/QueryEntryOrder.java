package com.kaiba.lib.base.constant.accounting;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import org.springframework.data.domain.Sort;

import java.util.Optional;

/**
 * author: lyux
 * date: 19-8-30
 */
public enum QueryEntryOrder implements IKbEnumValueGetter {

    AMOUNT_ASC(1, Sort.by(Sort.Direction.ASC, "amount")),
    AMOUNT_DESC(2, Sort.by(Sort.Direction.DESC, "amount")),
    CREATE_TIME_ASC(3, Sort.by(Sort.Direction.ASC, "createTime")),
    CREATE_TIME_DESC(4, Sort.by(Sort.Direction.DESC, "createTime")),
    ;

    private int value;
    private Sort sort;

    QueryEntryOrder(int value, Sort sort) {
        this.value = value;
        this.sort = sort;
    }

    public Sort getSort() {
        return sort;
    }

    @Override
    public int getValue() {
        return value;
    }

    public static Optional<QueryEntryOrder> valueOf(Integer value) {
        if (null == value) return Optional.empty();
        for (QueryEntryOrder v : values()) {
            if (v.getValue() == value) return Optional.of(v);
        }
        return Optional.empty();
    }
}
