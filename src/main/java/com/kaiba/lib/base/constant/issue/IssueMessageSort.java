package com.kaiba.lib.base.constant.issue;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * author: lyux
 * date: 18-11-23
 */
public enum IssueMessageSort implements IKbEnumValueGetter {

    ALL_ASC(1, true),
    ALL_DESC(2, false),
    SENDER_EXPERT_ASC(3, true),
    SENDER_EXPERT_DESC(4, false),
    SENDER_USER_ASC(5, true),
    SENDER_USER_DESC(6, false),
    ;

    private int value;
    private boolean asc;

    IssueMessageSort(int value, boolean asc) {
        this.value = value;
        this.asc = asc;
    }

    @Override
    public int getValue() {
        return value;
    }

    public boolean isAsc() {
        return asc;
    }

    public static Optional<IssueMessageSort> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (IssueMessageSort v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
