package com.kaiba.lib.base.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 23-07-19
 */
@Getter
public enum KbModule implements IKbEnumValueGetter {

    SYSTEM(0, "系统"),
    OTHER(100, "其他"), // 未归类模块

    USER(200, "用户"),
    AUTH(201, "权限"),
    BONUS(202, "老积分"),
    POINT(203, "新积分"),
    MESSAGE(206, "用户消息"),
    PUSH(207, "推送"),
    CAR(210, "车型信息"),
    FLOAT_VIEW(211, "浮窗浮标"),
    ACCOUNTING(220, "资金"),
    PRIZE(221, "奖品"),
    ACTIVITY_HUB(223, "活动注册中心"),
    ACT_CONF(225, "活动配置"),
    MALL(224, "商城模块"), // Java 版商城
    SITE(230, "电台信息模块"),
    NOTE(231, "通用圈子模块"),
    EMCEE(240, "主持人模块"),
    SEARCH(251, "搜索模块"),
    MEDIA_ASSET(259, "媒资管理(新)"),
    MEDIA(260, "媒资管理(旧)"),

    APP_MODULE(209, "老组件化"),
    APP_VERSION(261, "客户端版本管理"),
    APP_CONFIG(262, "客户端配置管理"),
    APP_SPLASH(271, "闪屏页"),
    APP_LEGO(272, "新组件化"),
    APP_BANNER(273, "通用 banner 组件数据源"),
    APP_ICON_GRID(274, "通用 网格图标 组件数据源"),
    APP_TEXT_TICKER(275, "通用 此条滚动器 组件数据源"),
    APP_HOME_BEST(281, "首页推荐池"),
    VOTE(800, "通用投票"),

    SAFEGUARD(301, "车主维权模块"),
    ISSUE(302, "汽车问答模块"),
    ROAD_CONDITION(352, "路况信息模块"),

    PROGRAM(410, "电台-节目互动模块"),
    PLAYBACK(411, "电台-节目回播模块"),
    ENROLL(419, "电台-报名模块"),
    CIRCLE(430, "电台-车友圈模块"),
    VIDEO_LIVE(452, "视频直播模块"),
    AUDIO_LIVE(453, "音频直播模块"),
    REVEAL(494, "爆料台"),
    MINI_THEATRE(495, "小剧场短剧"),
    RUSH(496, "通用抽奖模块"),
    HCRT_HAPPY(497, "文广幸福吧"),
    EDUCATION(498, "开吧教育"),

    NEWS(501, "电台-资讯新闻模块"),
    NEWS_NEO(502, "电台-新资讯模块"),

    HOT_TOPIC(651, "热门专题模板"),
    ACTION_MAP(652, "帖子地图模板"),
    ACT_MIX(654, "瀑布流列表"), // 活动列表
    SERV_BLOCK(655, "服务网格"),
    WORK_ORDER(660, "工单系统"),
    ELECTION(665, "通用票选引擎"),
    TIPS(666, "小贴士"),

    THIRD_PARTY(900, "第三方服务"),
    THIRD_PARTY_RONG(901, "第三方服务-容云"),
    THIRD_PARTY_RONG_LIAN(902, "第三方服务-容联云"),
    THIRD_PARTY_QINIU(903, "第三方服务-七牛云"),
    THIRD_PARTY_YUNXIN(904, "第三方服务-网易云信"),

    APP_MAIN_PAGE(701, "客户端首页"),
    SINGLE_PAGE(702, "各类单页"),

    @Deprecated
    ACTIVITY(491, "活动支撑模块"),
    @Deprecated
    ACTIVITY_PRIZE(492, "活动奖品模块"),
    @Deprecated
    LICENSE_LOTTERY(493, "摇号宠粉日模块"),
    @Deprecated
    SCORE_SHOP(601, "积分商城模块"),
    @Deprecated
    EMERGENCY(421, "电台-应急资讯模块"),
    @Deprecated
    LIVE(451, "电台-直播模块"),
    @Deprecated
    POLICY_TOPIC(653, "时政专题模板"),
    @Deprecated
    NEWS_VIDEO_CLIP(503, "电台-要闻短视频模块"),
    @Deprecated
    MALL_PHP7(222, "商城模块"), // PHP7 laravel 版, 计划废弃
    @Deprecated
    CAR_REPUTATION(351, "爱车口碑模块"),

    ;

    private final int value;
    private final String description;

    KbModule(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static Optional<KbModule> resolveByValue(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        return Optional.ofNullable(MAP_BY_VALUE.get(value));
    }

    public static Optional<KbModule> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (KbModule v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<KbModule> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (KbModule v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    private static final Map<Integer, KbModule> MAP_BY_VALUE =
            Arrays.stream(values()).collect(Collectors.toMap(KbModule::getValue, c -> c));

}
