package com.kaiba.lib.base.constant.point;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 2022-04-15
 */
@Getter
public enum PointTransactionStrategy implements IKbEnumValueGetter {

    AUTO(1, "根据积分实例阈值决定"),
    NO_TRANSACTION(2, "不使用事务"),
    TRANSACTION(3, "使用事务"),
    ;

    private final int value;

    private final String description;

    PointTransactionStrategy(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static Optional<PointTransactionStrategy> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (PointTransactionStrategy v : values()) {
            if (v.toString().equals(name)) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<PointTransactionStrategy> resolveByValue(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (PointTransactionStrategy v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
