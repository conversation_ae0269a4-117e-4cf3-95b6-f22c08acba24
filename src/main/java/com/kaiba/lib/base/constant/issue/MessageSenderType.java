package com.kaiba.lib.base.constant.issue;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * author: lyux
 * date: 18-9-15
 */
public enum MessageSenderType implements IKbEnumValueGetter {

    USER(1),
    EXPERT(2),
    HOST(3),
    ;

    private int value;

    MessageSenderType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static Optional<MessageSenderType> valueOf(Integer value) {
        if (null == value) return Optional.empty();
        for (MessageSenderType v : values()) {
            if (v.getValue() == value) return Optional.of(v);
        }
        return Optional.empty();
    }

}
