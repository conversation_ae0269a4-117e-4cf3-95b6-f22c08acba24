package com.kaiba.lib.base.constant.appversion;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 22-11-04
 */
@Getter
public enum AppVersionState implements IKbEnumValueGetter {

    PREPARE(1, "未上线"),
    ONLINE(2, "已上线, 正常运作"),
    NOT_RECOMMEND(3, "已上线, 建议升级"),
    FATAL(4, "已上线, 有致命问题, 应强制升级"),
    ARCHIVED(5, "已无用户"),
    ;

    private final int value;
    private final String description;

    AppVersionState(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static Optional<AppVersionState> resolveByValue(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (AppVersionState v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
