package com.kaiba.lib.base.constant.apphome;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import lombok.Getter;

import java.util.*;

/**
 * author: lyux
 * date: 23-07-19
 *
 * 推荐池内容来源枚举.
 */
@Getter
public enum AppHomeSelectionOrigin implements IKbEnumValueGetter {

    BY_OP(1, "运营手动创建"),
    AD_MARKET(2, "营销广告"),
    AD_INTERNAL(3, "内宣广告"),
    NEO_NEWS(10, "新资讯"),
    NEWS(11, "老资讯"),
    HEADLINE(12, "要闻"),
    NOTE(13, "帖子"),
    ACT_MIX(14, "活动"),
    SAFEGUARD(15, "维权"),
    REVEAL_TOPIC(16, "爆料台话题"),
    HOT_TOPIC(17, "热门话题"),
    GROUP_TOPIC(18, "资讯话题"),
    ;

    private final int value;
    private final String description;

    AppHomeSelectionOrigin(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static Optional<AppHomeSelectionOrigin> resolveByValue(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (AppHomeSelectionOrigin v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<AppHomeSelectionOrigin> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (AppHomeSelectionOrigin v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<AppHomeSelectionOrigin> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (AppHomeSelectionOrigin v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
