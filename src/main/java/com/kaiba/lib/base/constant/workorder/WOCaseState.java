package com.kaiba.lib.base.constant.workorder;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-10-20
 *
 * 工单系统, 案件状态.
 */
@Getter
public enum WOCaseState {

    PENDING("待处理"),
    DOING("处理中"),
    CLOSED("已办结"),
    ;

    /** 描述 */
    private final String description;

    WOCaseState(String description) {
        this.description = description;
    }

    public static Optional<WOCaseState> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WOCaseState v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<WOCaseState> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WOCaseState v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
