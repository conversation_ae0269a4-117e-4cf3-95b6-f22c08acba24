package com.kaiba.lib.base.constant.videolive;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/6/4
 */
public enum PortalState implements IKbEnumValueGetter {

    INIT(1, "配置中"),
    SIGNED(2, "已签发"),
    ONLINE(3, "已上线"),
    ENDED(4, "已结束"),
    SEALED(5, "已归档");

    static {
        INIT.next = statuses(SIGNED, SEALED);
        SIGNED.next = statuses(INIT, ONLINE, SEALED);
        ONLINE.next = statuses(ENDED, SEALED);
        ENDED.next = statuses(SEALED);
        SEALED.next = statuses();
    }

    private int value;
    private String description;
    private PortalState[] next;

    PortalState(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    @Override
    public String getDescription() {
        return this.description;
    }


    public boolean isStateChangeAllowed(PortalState nextState) {
        for (PortalState s : this.next) {
            if (s == nextState) return true;
        }
        return false;
    }

    public static Optional<PortalState> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (PortalState item : values()) {
            if (item.value == value) {
                return Optional.of(item);
            }
        }
        return Optional.empty();
    }

    private static PortalState[] statuses(PortalState... states) {
        return states;
    }
}
