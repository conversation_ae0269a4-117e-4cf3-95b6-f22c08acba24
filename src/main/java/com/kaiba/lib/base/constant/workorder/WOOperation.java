package com.kaiba.lib.base.constant.workorder;

import com.kaiba.lib.base.domain.workorder.WOCaseModel;
import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-10-20
 *
 * 工单系统, 对案件的处理动作枚举.
 * 注意: 各个动作的注释中, 各类数据变更均为引擎默认的行为逻辑, 业务层可在事件生成时改变默认行为.
 */
@Getter
public enum WOOperation {

    /**
     * 动作场景: 创建案件.
     * 状态变更: 案件进入 "待处理"阶段 ({@link WOCaseState#PENDING}).
     * 处理者变更:
     * * 如果指明了初始处理者 ({@link WOCaseModel#getOriginResolver()}, 则初始处理者成为案件的当前处理者.
     * 响应者变更:
     * * 如果存在当前处理者, 则当前处理者为下一任响应者;
     * * 如果未指明初始处理者, 则主持者为下一任响应者;
     */
    CREATE("发起案件"),

    /**
     * 动作场景: 案件在待处理阶段进行审核. 审核通过由商家自行受理.
     * 状态变更: 无状态变更.
     * 处理者变更: 指定的处理者成为案件的当前处理者.
     * 响应者变更: 当前处理者成为下一任响应者.
     */
    AUDIT("审核案件"),

    /**
     * 动作场景: 案件进入处理阶段. 可能是提问者指明处理者, 也可能是主持者将案件指派给处理者, 也可能是某个处理者主动接取.
     * 状态变更: 案件由 {@link WOCaseState#PENDING} 变更为 {@link WOCaseState#DOING} 状态.
     * 处理者变更: 指定的处理者成为案件的当前处理者.
     * 响应者变更: 当前处理者成为下一任响应者.
     */
    ACCEPT("受理案件"),

    /**
     * 动作场景: 对案件内容进行编辑.
     * 状态变更: 无状态变更.
     * 处理者变更: 无处理者变更.
     * 响应者变更: 无响应者变更.
     */
    EDIT("编辑案件"),

    /**
     * 动作场景: 对案件添加补充信息. 补充信息是独立数据, 因此该动作不会改变案件内容本身.
     * 状态变更: 无状态变更.
     * 处理者变更: 无处理者变更.
     * 响应者变更: 无响应者变更.
     */
    ENRICH("补充信息"),

    /**
     * 动作场景: 产生一条交互信息.
     * 状态变更: 无状态变更.
     * 处理者变更: 无处理者变更.
     * 响应者变更:
     * * 如果此条信息由提问者发出, 则案件的当前处理者会作为下一任响应者; 如果当前无处理者, 则主持者会作为下一任响应者.
     * * 如果此条信息由提问者以外的用户发出, 则提问者会作为下一任响应者.
     */
    REPLY("回复案件"),

    /**
     * 动作场景: 向当前响应者发出提醒.
     * 状态变更: 无状态变更.
     * 处理者变更: 无处理者变更.
     * 响应者变更: 无响应者变更.
     */
    REMIND("催促提醒"),

    /**
     * 动作场景: 向主持者提出下一步处理建议.
     * 状态变更: 无状态变更.
     * 处理者变更: 无处理者变更.
     * 响应者变更: 主持者成为下一任响应者.
     */
    SUGGEST("处理建议"),

    /**
     * 动作场景: 问题交还给主持者, 但不转换阶段.
     * 状态变更: 无状态变更.
     * 处理者变更: 将当前处理者置空.
     * 响应者变更: 主持者成为下一任响应者.
     */
    BACK("交还案件"),

    /**
     * 动作场景: 转交给另一位解决者.
     * 状态变更: 无状态变更.
     * 处理者变更: 变更为指定的下一任处理者.
     * 响应者变更: 变更后的当前处理者成为下一任响应者.
     */
    HANDOVER("转交案件"),

    /**
     * 动作场景: 解决者或主持者询问提问者, 是否可以结案.
     * 状态变更: 无状态变更.
     * 处理者变更: 无处理者变更.
     * 响应者变更: 提问者成为下一任响应者.
     */
    CLOSE_SUGGEST("建议结案"),

    /**
     * 动作场景: 提问者拒绝结案建议.
     * 状态变更: 无状态变更.
     * 处理者变更: 无处理者变更.
     * 响应者变更: 当前处理者成为下一任响应者.
     */
    CLOSE_REFUSE("拒绝结案"),

    /**
     * 动作场景: 案件办结, 可能是用户主动结案, 也可能是用户接受结案建议, 也可能是其他有权限的 B 端强制结案.
     * 状态变更: 状态变更为 "已办结" {@link WOCaseState#CLOSED}.
     * 处理者变更: 无处理者变更.
     * 响应者变更: 提问者成为下一任响应者, 以提醒用户评论.
     */
    CLOSE("案件办结"),

    /**
     * 动作场景: 在办结阶段且未归档, 则可评价.
     * 状态变更: 无状态变更.
     * 处理者变更: 无处理者变更.
     * 响应者变更: 下一任响应者置空.
     */
    RATE("打分评价"),

    ;

    private final String description;

    WOOperation(String description) {
        this.description = description;
    }

    public static Optional<WOOperation> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WOOperation v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<WOOperation> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WOOperation v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
