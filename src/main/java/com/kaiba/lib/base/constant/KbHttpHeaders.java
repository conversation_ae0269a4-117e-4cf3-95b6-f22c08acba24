package com.kaiba.lib.base.constant;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2020-04-24
 */
public enum KbHttpHeaders {

    /** 电台 id, siteId. */
    KB_SITE_ID(KbHeader.KB_SITE_ID),

    /** 地市编码, source. */
    KB_SOURCE(KbHeader.KB_SOURCE),

    /** 用户 id, userId. */
    KB_USER_ID(KbHeader.KB_USER_ID),

    /** 令牌, token. */
    KB_TOKEN(KbHeader.KB_TOKEN),

    /** 设备编号, cid. */
    KB_CID(KbHeader.KB_CID),

    /** 设备编号, accessId. */
    KB_AID(KbHeader.KB_AID),

    /** 渠道号, channel. */
    KB_CNL(KbHeader.KB_CNL),

    /** app version code, 应用版本号. 如 30201. */
    KB_VC(KbHeader.KB_VC),

    /** device origin, 设备类型. 如 Android, iOS, ohos鸿蒙 */
    KB_DO(KbHeader.KB_DO, possibleValues("Android", "iOS", "ohos")),

    /** device brand, 设备品牌型号. 如 Meizu Pro6 / iPhone 10,1. */
    KB_DB(KbHeader.KB_DB),

    /** device version, 设备操作系统版本. 如 (Android) 4.4.1 / (iOS) 12.1. */
    KB_DV(KbHeader.KB_DV),

    /** operation system, 载体类型. 如 Android, WXTiny, Browser. */
    KB_OS(KbHeader.KB_OS, possibleValues(
            "Android", "iOS", "ohos", "WXTiny", "Android-Web", "iOS-Web", "WX-Web", "Browser"
    )),

    /** operation system version, 载体版本号. 如 Android 系统版本, 浏览器系统版本. */
    KB_OV(KbHeader.KB_OV),

    /** end point, 端类型. 如 车主端, 技师端, 节目互动小程序. */
    KB_EP(KbHeader.KB_EP, Arrays.stream(KbEndpoint.values())
            .map(KbEndpoint::getValue)
            .map(Objects::toString)
            .collect(Collectors.toSet())),

    /** 若识别请求带有此 header, 则将返回调试信息 kbDebug 字段. */
    KB_DEBUG(KbHeader.KB_DEBUG, possibleValues("1")),

    /**
     * 微服务内部 header: ip address, 访问者 ip.
     * 此 header 仅限微服务内部传递使用, 为了应对服务链路过长时, 下游 remote ip 无法正确获取的问题.
     */
    KB_IP(KbHeader.KB_IP),

    ;

    private final String header;
    private final Set<String> possibleValues;

    KbHttpHeaders(String header) {
        this(header, Collections.emptySet());
    }

    KbHttpHeaders(String header, Set<String> possibleValues) {
        this.header = header;
        this.possibleValues = possibleValues;
    }

    public String getHeaderName() {
        return header;
    }

    public boolean hasHeader(HttpServletRequest req) {
        return req != null && req.getHeader(header) != null;
    }

    public Optional<String> getValidHeader(HttpServletRequest req) {
        String headerValue = req.getHeader(header);
        if (possibleValues.size() == 0 || possibleValues.contains(headerValue)) {
            return Optional.of(headerValue);
        } else {
            return Optional.empty();
        }
    }

    public String getValidHeaderOrNull(HttpServletRequest req) {
        String headerValue = req.getHeader(header);
        if (possibleValues.size() == 0 || possibleValues.contains(headerValue)) {
            return headerValue;
        } else {
            return null;
        }
    }

    public Optional<Integer> getValidIntegerHeader(HttpServletRequest req) {
        return getValidHeader(req).map(v -> {
            try {
                return Integer.valueOf(v);
            } catch (NumberFormatException e) {
                return null;
            }
        });
    }

    public Integer getValidIntegerHeaderOrNull(HttpServletRequest req) {
        String value  = getValidHeaderOrNull(req);
        if (value == null) {
            return null;
        } else {
            try {
                return Integer.valueOf(value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
    }

    public Optional<Long> getValidLongHeader(HttpServletRequest req) {
        return getValidHeader(req).map(v -> {
            try {
                return Long.valueOf(v);
            } catch (NumberFormatException e) {
                return null;
            }
        });
    }

    public Long getValidLongHeaderOrNull(HttpServletRequest req) {
        String value  = getValidHeaderOrNull(req);
        if (value == null) {
            return null;
        } else {
            try {
                return Long.valueOf(value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
    }

    private static Set<String> possibleValues(String... v) {
        if (null == v || v.length == 0) {
            return Collections.emptySet();
        } else {
            return Collections.unmodifiableSet(new HashSet<>(Arrays.asList(v)));
        }
    }

}
