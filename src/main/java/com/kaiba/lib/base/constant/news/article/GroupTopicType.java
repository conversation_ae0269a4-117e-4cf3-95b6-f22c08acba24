package com.kaiba.lib.base.constant.news.article;

import lombok.Getter;

import java.util.Optional;

/**
 * 专题类型
 * <AUTHOR>
 * @version GroupTopicType, v0.1 2025/2/13 10:43 daopei Exp $
 **/
@Getter
public enum GroupTopicType {
    VERSION_BASE("基础版本, 布局 + tab列表"),
    VERSION_CONTAINER("容器版本, 布局 + tab列表 + headTab列表(支持容器)")
    ;

    private final String description;

    GroupTopicType(String description) {
        this.description = description;
    }


    public static Optional<GroupTopicType> resolveByName(String value) {
        for (GroupTopicType type : GroupTopicType.values()) {
            if (type.name().equalsIgnoreCase(value)) {
                return Optional.of(type);
            }
        }
        return Optional.empty();
    }
}
