package com.kaiba.lib.base.constant.common;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-05-25
 * 
 * 常见奇幻生物名称. 部分业务的枚举值难以赋予有意义的命名, 可用此枚举类做泛化命名.
 */
@Getter
public enum DummyFantasy {

    CENTAUR,
    PEGASUS,
    UNICORN,
    KYLIN,
    PHOENIX,
    GRIFFIN,
    PIXIE,
    FAERIE,
    GENIE,
    DRAGON,
    GARGO<PERSON>LE,
    GOLEM,
    TITAN,
    BEHEMOTH,
    BASILISK,
    WYVERN,
    HYDRA,
    IMP,
    ORC,
    TROLL,
    GOBLIN,
    CYCLOPS,
    CERBERUS,
    VAMPIRE,
    HARPY,
    MEDUSA,
    MINOTAUR,
    MANTICORE,
    WEREWOLF,
    KRAKEN,
    ;

    public static Optional<DummyFantasy> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (DummyFantasy v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<DummyFantasy> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (DummyFantasy v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
