package com.kaiba.lib.base.constant.issue;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * author: lyux
 * date: 18-9-21
 */
public enum IssueTaskType implements IKbEnumValueGetter {

    /** 自动派单 */
    DISPATCH(1),

    /** 指定专家 */
    SPECIFY(2),
    ;

    private int value;

    IssueTaskType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static Optional<IssueTaskType> valueOf(Integer value) {
        if (null == value) return Optional.empty();
        for (IssueTaskType v : values()) {
            if (v.getValue() == value) return Optional.of(v);
        }
        return Optional.empty();
    }

}
