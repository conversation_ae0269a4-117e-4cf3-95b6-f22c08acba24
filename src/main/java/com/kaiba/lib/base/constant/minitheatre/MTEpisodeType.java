package com.kaiba.lib.base.constant.minitheatre;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 23-10-20
 */
@Getter
public enum MTEpisodeType {

    TRAILER("预告", "预告%s", 4),
    PROLOG("序章", "序章%s", 1),
    FEATURE("正片", "第%s集", 2),
    SIDE_LIGHTS("花絮", "花絮%s", 5),
    SPECIAL("特别节目", "特别节目%s", 3),
    ;

    private final String description;
    private final String idxFormatter;
    private final int order;

    MTEpisodeType(String description, String idxFormatter, int order) {
        this.description = description;
        this.idxFormatter = idxFormatter;
        this.order = order;
    }

    public String formatIdxName(int idx) {
        return String.format(idxFormatter, idx);
    }

    public static String formatIdxNameByType(String type, int idx) {
        MTEpisodeType episodeType = types.get(type);
        if (episodeType == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown type: " + type);
        } else {
            return episodeType.formatIdxName(idx);
        }
    }

    public static Optional<MTEpisodeType> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (MTEpisodeType v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<MTEpisodeType> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (MTEpisodeType v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    private static final Map<String, MTEpisodeType> types = Arrays.stream(values())
            .collect(Collectors.toMap(MTEpisodeType::name, t -> t));

}
