package com.kaiba.lib.base.constant.enroll;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

public enum EnrollState implements IKbEnumValueGetter {

    INIT(1, "活动正在配置,未开始"),
    ONLINE(2, "活动上线"),
    SEALED(3, "活动归档"),
    CANCEL(4, "活动取消"),
    ;

    private int value;
    private String description;

    EnrollState(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<EnrollState> valueOf(Integer value) {
        if (null == value) return Optional.empty();
        for (EnrollState v : values()) {
            if (v.getValue() == value) return Optional.of(v);
        }
        return Optional.empty();
    }

    public static final Integer[] ACTIVE_STATES = new Integer[] {
            INIT.getValue(),
            ONLINE.getValue(),
    };
}
