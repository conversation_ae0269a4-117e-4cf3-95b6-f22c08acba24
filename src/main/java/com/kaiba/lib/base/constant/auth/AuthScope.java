package com.kaiba.lib.base.constant.auth;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 19-8-9
 *
 * 权限作用范围
 */
public enum AuthScope implements IKbEnumValueGetter {

    ALL(1, "全站"),
    USER(2, "单独用户", "${userId}"),
    APP(21, "前端"),
    PLATFORM(41, "平台管理"),
    SITE(51, "电台管理", "${siteId}"),
    SAFEGUARD(61, "维权商家管理"),
    CIRCLE(71, "车友圈版主", "${threadId}"),
    TECH(200, "仅用于编程接口"),
    ;

    private int value;
    private String description;
    private String wildcard; // 占位符

    AuthScope(int value, String description) {
        this.value = value;
        this.description = description;
    }

    AuthScope(int value, String description, String wildcard) {
        this.value = value;
        this.wildcard = wildcard;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public boolean isNeedFormat() {
        return wildcard != null;
    }

    public String getWildcard() {
        return wildcard;
    }

    public static Optional<AuthScope> valueOf(Integer value) {
        if (null == value) return Optional.empty();
        for (AuthScope v : values()) {
            if (v.getValue() == value) return Optional.of(v);
        }
        return Optional.empty();
    }

    public static final List<Integer> SCOPE_WITH_FORMAT_LIST = new ArrayList<>();
    public static final List<Integer> SCOPE_WITHOUT_FORMAT_LIST = new ArrayList<>();
    static {
        for (AuthScope scope : values()) {
            if (scope.isNeedFormat()) {
                SCOPE_WITH_FORMAT_LIST.add(scope.getValue());
            } else {
                SCOPE_WITHOUT_FORMAT_LIST.add(scope.getValue());
            }
        }
    }

}
