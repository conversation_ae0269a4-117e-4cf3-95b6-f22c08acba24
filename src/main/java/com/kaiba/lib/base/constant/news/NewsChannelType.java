package com.kaiba.lib.base.constant.news;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 2024-08-08
 *
 * 频道栏目列表展示配置.
 */
@Getter
public enum NewsChannelType {

    HCRT_CHANNEL("杭州文广频率频道"),
    HZ_COUNTY("杭州区县市融媒"),
    ;

    private final String description;

    NewsChannelType(String description) {
        this.description = description;
    }

    public static Optional<NewsChannelType> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (NewsChannelType v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<NewsChannelType> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (NewsChannelType v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
