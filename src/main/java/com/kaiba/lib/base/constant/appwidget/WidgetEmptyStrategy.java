package com.kaiba.lib.base.constant.appwidget;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 23-05-25
 */
@Getter
public enum WidgetEmptyStrategy implements IKbEnumValueGetter {

    HIDE(1, "无数据则隐藏控件"),
    DEFAULT_IMAGE(2, "无数据则填充默认数据"),
    ;

    private final int value;
    private final String description;

    WidgetEmptyStrategy(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static Optional<WidgetEmptyStrategy> resolveByValue(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (WidgetEmptyStrategy v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<WidgetEmptyStrategy> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WidgetEmptyStrategy v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

    public static Optional<WidgetEmptyStrategy> resolveByNameIgnoreCase(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (WidgetEmptyStrategy v : values()) {
            if (name.equalsIgnoreCase(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
