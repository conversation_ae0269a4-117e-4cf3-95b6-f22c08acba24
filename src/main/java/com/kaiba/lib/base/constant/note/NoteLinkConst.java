package com.kaiba.lib.base.constant.note;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * author: lyux
 * date: 19-5-21
 *
 * 固定的帖子超链接类型信息.
 */
public enum NoteLinkConst implements IKbEnumValueGetter {

    TOUTIAO(1, "http://static.kaiba315.com.cn/logo_toutiao.png", "今日头条"),
    WEIBO(2, "http://static.kaiba315.com.cn/logo_weibo.png", "微博"),
    DOUYIN(3, "http://static.kaiba315.com.cn/logo_douyin.png", "抖音"),
    KAIBA_918(4, "http://static.kaiba315.com.cn/logo_918.png", "918"),
    KAIBA(5, "http://static.kaiba315.com.cn/logo_kaiba.png", "开吧"),
    WEIXIN(6, "http://static.kaiba315.com.cn/logo_kaiba.png", "微信"),
    ;

    private int value;
    private String icon;
    private String description;

    NoteLinkConst(int value, String icon, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    public String getIcon() {
        return icon;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<NoteLinkConst> valueOf(Integer value) {
        if (null == value) return Optional.empty();
        for (NoteLinkConst v : values()) {
            if (v.getValue() == value) return Optional.of(v);
        }
        return Optional.empty();
    }
}
