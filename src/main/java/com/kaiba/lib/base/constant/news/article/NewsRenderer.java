package com.kaiba.lib.base.constant.news.article;

import lombok.Getter;

import java.util.Optional;

/**
 * author: lyux
 * date: 24-07-10
 *
 * 文章渲染方式.
 */
@Getter
public enum NewsRenderer {

    /**
     * 文章为外链时, 不需要在前端页面渲染.
     * 内容类字段无效.
     */
    NONE("不需要渲染", false),

    /**
     * 针对具体业务的定制化渲染, 内容的约束条件由具体业务自行实现.
     */
    CUSTOM("定制化渲染", false),

    /**
     * 使用 HTML 排版的文章.
     * content 必填, contentType 为支持 HTML 的类型.
     */
    NEWS("资讯文章", false),

    /**
     * 使用 HTML 排版的文章, 有一段吸顶音频.
     * audio 必填, contentType 为支持 HTML 的类型或 PLAIN_TEXT.
     */
    AUDIO_TOP("吸顶音频", true),

    /**
     * 使用 HTML 排版的文章, 有一段吸顶视频.
     * video 必填, contentType 为支持 HTML 的类型或 PLAIN_TEXT.
     */
    VIDEO_TOP("吸顶视频", true),

    /**
     * 全屏展示的音频内容.
     * audio 必填, contentType 建议为 PLAIN_TEXT.
     */
    AUDIO_FULL("全屏音频", true),

    /**
     * 全屏展示的视频内容.
     * video 必填, contentType 建议为 PLAIN_TEXT.
     */
    VIDEO_FULL("全屏视频", true),

    /**
     * 栏目的单期视频或音频. 单期只有在从属于一个栏目时, 往期列表和拆条列表功能才可以展示.
     * video/audio 必填一个, contentType 为支持 HTML 的类型或 PLAIN_TEXT.
     */
    VIDEO_EPISODE("栏目单期视频", true),

    /**
     * 栏目单期的拆条视频或音频. 拆条只有在从属于一个栏目时, 往期列表和拆条列表功能才可以展示.
     * video/audio 必填一个, contentType 为支持 HTML 的类型或 PLAIN_TEXT.
     */
    VIDEO_EPISODE_CLIP("栏目拆条视频", true),

    ;

    private final String description;
    private final boolean hasVideo;

    NewsRenderer(String description, boolean hasVideo) {
        this.description = description;
        this.hasVideo = hasVideo;
    }

    public static Optional<NewsRenderer> resolveByName(String name) {
        if (null == name) {
            return Optional.empty();
        }
        for (NewsRenderer v : values()) {
            if (name.equals(v.name())) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
