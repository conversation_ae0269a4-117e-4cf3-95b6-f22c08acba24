package com.kaiba.lib.base.constant.circle;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * author: lyux
 * date: 2020-04-25
 */
public enum CircleState implements IKbEnumValueGetter {

    SHOW(1, "显示"),
    HIDE(2, "隐藏"),
    ;

    private final int value;
    private final String description;

    CircleState(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<CircleState> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (CircleState v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }

}
