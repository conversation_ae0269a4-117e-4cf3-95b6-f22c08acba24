package com.kaiba.lib.base.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version KbPushChannel, v0.1 2024/2/5 10:40 daopei Exp $
 **/
@Getter
public enum KbPushChannel implements IKbEnumValueGetter{
    JIGUANG(1,"极光推送(android通过极光渠道,ios通过本地推送)"),
    GETUI(2,"个推推送, 对应应用[开吧]"),
    GETUI_V2(3,"个推推送，对应应用[开吧-埋点demo]"),
    ;

    KbPushChannel(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    private int value;

    private String desc;

    @Override
    public int getValue() {
        return value;
    }
}
