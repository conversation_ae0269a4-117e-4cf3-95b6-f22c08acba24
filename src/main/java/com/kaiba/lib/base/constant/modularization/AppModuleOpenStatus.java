package com.kaiba.lib.base.constant.modularization;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

/**
 * <AUTHOR>
 * @date 2019/5/31
 */
public enum AppModuleOpenStatus implements IKbEnumValueGetter {

    CLOSE(0, "未启用组件化"),
    OPEN(1, "启用组件化"),;

    private int value;
    private String description;

    AppModuleOpenStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }
}
