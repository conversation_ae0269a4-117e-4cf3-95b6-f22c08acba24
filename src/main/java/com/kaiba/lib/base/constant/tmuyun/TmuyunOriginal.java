package com.kaiba.lib.base.constant.tmuyun;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/07/21 14:21
 **/
public enum TmuyunOriginal implements IKbEnumValueGetter {
    /** 稿件属性 */
    NON_ORIGINAL(0, "非原创"),
    ORIGINAL(1, "原创")
    ;

    private final int value;
    private final String description;

    TmuyunOriginal(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<TmuyunOriginal> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (TmuyunOriginal v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }
}
