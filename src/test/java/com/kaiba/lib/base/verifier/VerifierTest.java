package com.kaiba.lib.base.verifier;

import com.kaiba.lib.base.constant.user.UserLoginType;
import com.kaiba.lib.base.constant.user.UserRole;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.V;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.lib.base.test.util.DataGenerator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * author: lyux
 * date: 2020-05-09
 */
@RunWith(JUnit4.class)
public class VerifierTest {

    @Test
    public void basicPassTest1() {
        User user = randomUser();

        // logic and
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(F.number(User::getId).gte(0))
                .and(F.str(User::getCastle).notEmpty())
                .create()
                .verify(user);

        // logic or
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(V.or(F.obj(User::getIdCard).notNull(), F.obj(User::getId).notNull()))
                .create()
                .verify(user);

        // condition
        new VerifierBuilder<User>().defaultOrElseThrow()
                .ifTrue(u -> u.getId() != null, F.number(User::getId).gte(0))
                .ifFalse(u -> u.getId() == null, F.number(User::getId).lt(9999999))
                .ifNotNull(F.str(User::getIdCard).isIDCard())
                .create()
                .verify(user);

        // override terminator error handler, should not throw exception
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(F.number(User::getId).eq(-1).debug().orElseContinue())
                .create()
                .verify(user);

        // negate should pass
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(V.eq(User::getId, -1).negate())
                .create()
                .verify(user);

        // nested
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(V.notNull(User::getId))
                .and(User::getArticle, new VerifierBuilder<Article>()
                        .and(V.notNull(Article::getId))
                        .and(V.timeGTE(Article::getStartTime, "1990-01-01 00:00:00"))
                        .create())
                .create()
                .verify(user);

        // basic verifiers
        user.setIdCard("370802198407032116");
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(V.rangeClosedOpen(User::getId, -1, 10000))
                .and(F.number(User::getId).gte(0))
                .and(F.timestamp(User::getCreateTime).gt("1980-01-01 00:00:00"))
                .and(F.timestamp(User::getCreateTime).inMillis().lte("2050-01-01"))
                .and(F.timestamp(User::getCreateTime).between("2020-07-03", "2050-01-01"))
                .and(F.str(User::getBirthday).isTimeFormat("YYYY-MM-dd"))
                .and(F.str(User::getIdCard).isIDCard())
                .and(F.str(User::getAreaCode).isGeoAreaCode())
                .and(F.list(User::getTels).allowEmpty().each(V::strMobileOrTel))
                .and(User::getArticle, new VerifierBuilder<Article>()
                        .and(V.notNull(Article::getId))
                        .and(V.timeGTE(Article::getStartTime, "1990-01-01 00:00:00"))
                        .create())
                .create().verify(user);
    }

    @Test(expected = Exception.class)
    public void logicAndFailTest() {
        User user = randomUser();
        user.setIdCard(null);
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(V.and(V.notNull(User::getName), V.notNull(User::getIdCard)))
                .create()
                .verify(user);
    }

    @Test(expected = Exception.class)
    public void logicOrFailTest() {
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(V.or(V.notNull(User::getName).negate(), V.notNull(User::getId).negate()))
                .create()
                .verify(randomUser());
    }

    @Test(expected = Exception.class)
    public void nestedFailTest() {
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(V.notNull(User::getId))
                .and(User::getArticle, new VerifierBuilder<Article>()
                        .and(V.notNull(Article::getId))
                        .and(V.timeLTE(Article::getStartTime, "1990-01-01 00:00:00"))
                        .create())
                .create()
                .verify(randomUser());
    }

    @Test
    public void listSuccessTest() {
        User user = randomUser();

        user.setTels(Arrays.asList("15098837373", "0571-89833533", "0571 89833533", "89833533", "15258884881"));
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(F.list(User::getTels).each(V::strMobileOrTel))
                .create()
                .verify(user);

        user.setTels(null);
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(F.list(User::getTels).allowEmpty().each(V::strMobileOrTel))
                .create()
                .verify(user);
    }

    @Test(expected = Exception.class)
    public void listFailTest() {
        User user = randomUser();
        user.setTels(Arrays.asList("15098837373", "12345", "0571-89833533"));
        new VerifierBuilder<User>().defaultOrElseThrow().defaultDebug(true)
                .and(F.list(User::getTels).each(V::strMobileOrTel))
                .create()
                .verify(user);
    }

    @Test(expected = Exception.class)
    public void logicTest2() {
        User user = randomUser();
        user.setId(null);
        user.setName(null);
        new VerifierBuilder<User>().defaultOrElseThrow()
                .addVerifier(V.or(V.notNull(User::getId), V.notNull(User::getName)))
                .create()
                .verify(user);
    }

    @Test(expected = Exception.class)
    public void negateTest() {
        User user = randomUser();
        new VerifierBuilder<User>().defaultOrElseThrow()
                .addVerifier(V.notNull(User::getId).negate())
                .create()
                .verify(user);
    }

    @Test()
    public void enumTest() {
        User user = randomUser();
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(F.obj(User::getType).in(1, 2, 3))
                .create()
                .verify(user);
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(F.intF(User::getRole).enums(UserRole.values()))
                .and(F.str(User::getLoginType).enums(UserLoginType.values()))
                .create()
                .verify(user);
    }

    @Test()
    public void preCheckTest() {
        User user = randomUser();
        user.setIdCard(" 370802198407032116   ");
        user.setCreateTime(null);
        user.setBirthday(null);
        new VerifierBuilder<User>().defaultOrElseThrow().defaultDebug(true)
                .and(F.timestamp(User::getCreateTime).ifNullSetCurrent(User::setCreateTime).notNull())
                .and(F.str(User::getIdCard).trim(User::setIdCard).isIDCard())
                .and(F.str(User::getBirthday).allowNull().isTimeFormat("YYYY-MM-DD"))
                .create().verify(user);
        assert user.getCreateTime() - System.currentTimeMillis() < 100;
        assert user.getIdCard().equals("370802198407032116");
    }

    @Test
    public void setterHandlerTest() {
        User user = randomUser();
        user.setName("Jon Stark");
        new VerifierBuilder<User>().defaultOrElseThrow()
                .addVerifier(V.notEmpty(User::getName).thenSet(User::setName, "Arya Stark"))
                .create()
                .verify(user);
        assert "Arya Stark".equals(user.getName());

        new VerifierBuilder<User>().defaultOrElseThrow()
                .addVerifier(V.notNull(User::getName).orElseSet(User::setName, "Rob Stark"))
                .create()
                .verify(user);
        assert "Arya Stark".equals(user.getName());

        new VerifierBuilder<User>().defaultOrElseThrow()
                .addVerifier(V.eq(User::getName, "Rikken Stark")
                        .thenSet(User::setName, "Bran Stark")
                        .orElseContinue())
                .create()
                .verify(user);
        assert "Arya Stark".equals(user.getName());

        user.setName("");
        new VerifierBuilder<User>().defaultOrElseThrow()
                .addVerifier(V.notEmpty(User::getName).orElseSet(User::setName, "Rob Stark"))
                .create()
                .verify(user);
        assert "Rob Stark".equals(user.getName());

        user.setCastle(null);
        user.setName(null);
        user.setType(null);
        new VerifierBuilder<User>().defaultOrElseThrow()
                .ifNullSet(User::getName, User::setName, "Tyrion Lannister")
                .ifNullSet(User::getType, User::setType, 1)
                .ifNullSet(User::getCastle, User::setCastle, "Casterly Rock")
                .create()
                .verify(user);
        assert user.getType() == 1;
        assert "Tyrion Lannister".equals(user.getName());
        assert "Casterly Rock".equals(user.getCastle());

        user.setCastle("");
        user.setName("");
        new VerifierBuilder<User>().defaultOrElseThrow()
                .ifEmptySet(User::getName, User::setName, "Daenerys Targaryon")
                .ifNullSet(User::getCastle, User::setCastle, "Dragon Stone")
                .ifEmptySet(User::getCastle, User::setCastle, "King's Landing")
                .create()
                .verify(user);
        assert "Daenerys Targaryon".equals(user.getName());
        assert "King's Landing".equals(user.getCastle());

        user.setId(-1);
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(V.gt(User::getId, 0).orElseSet(User::setId, 11))
                .create()
                .verify(user);
        assert user.getId() == 11;
    }

    @Test
    public void strTrimTest() {
        User user = randomUser();
        user.setName("Jon Stark   ");
        new VerifierBuilder<User>().defaultOrElseThrow()
                .trim(User::getName, User::setName)
                .create()
                .verify(user);
        assert "Jon Stark".equals(user.getName());
    }

    // ------------------------------------------------

    @Test
    public void idCardTest() {
        Verifier<User> verifier = new VerifierBuilder<User>().defaultOrElseThrow()
                .and(V.strIDCard(User::getIdCard))
                .create();
        User user = randomUser();
        verifier.verify(user.idCard("110226198509021418"));
        verifier.verify(user.idCard("23020219910527003X"));
        verifier.verify(user.idCard("370683198901117657"));
        verifier.verify(user.idCard("350424870506202"));
    }

    @Test(expected = Exception.class)
    public void idCardFailTest() {
        new VerifierBuilder<User>().defaultOrElseThrow()
                .and(V.or(
                        V.strIDCard(u -> "110226"),
                        V.strIDCard(u -> "110226198509021417"),
                        V.strIDCard(u -> "110226088509021418")
                ))
                .create()
                .verify(randomUser());
    }

    // ------------------------------------------------

    private static User randomUser() {
        User model = new User();
        model.setId(DataGenerator.random.nextInt(1000) + 1);
        model.setName(DataGenerator.random(DataGenerator.sNameList));
        model.setCastle(DataGenerator.random(DataGenerator.sCastleList));
        model.setCreateTime(System.currentTimeMillis());
        model.setBirthday("1984-07-03");
        model.setArticle(randomArticle());
        model.setType(1);
        model.setRole(UserRole.USER.getValue());
        model.setLoginType(UserLoginType.AUTO.name());
        model.setAreaCode("330101");
        return model;
    }

    private static Article randomArticle() {
        Article model = new Article();
        model.setId(DataGenerator.randomString(16));
        model.setName(DataGenerator.random(DataGenerator.sCastleList));
        model.setStartTime(System.currentTimeMillis());
        model.setEndTime(model.getStartTime() + TimeUnit.DAYS.toMillis(15));
        model.setUrl("https://page.kaiba315.com.cn");
        return model;
    }

}
