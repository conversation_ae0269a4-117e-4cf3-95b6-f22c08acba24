package com.kaiba.lib.base.verifier;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 18-7-27
 */
@Data
@ToString
@NoArgsConstructor
public class User {

    private Integer id;
    private Integer type;
    private Integer role;
    private String name;
    private String castle;
    private String idCard;
    private String email;
    private String url;
    private String birthday;
    private Long createTime; // in millis
    private String areaCode;
    private String loginType;

    private List<String> tels;

    private Article article;

    public User idCard(String idCard) {
        this.idCard = idCard;
        return this;
    }

}
