package com.kaiba.lib.base.assembler;

import com.kaiba.lib.base.assembler.models.Article;
import com.kaiba.lib.base.assembler.models.User;
import com.kaiba.lib.base.test.util.AbstractTestCase;
import com.kaiba.lib.base.test.util.DataGenerator;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;
import org.junit.runners.MethodSorters;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 18-7-18
 */
@RunWith(JUnit4.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class AssemblerTests extends AbstractTestCase {

    @Test
    public void t11AssemblerToMapTest() {
        User user = User.random();
        Article article = Article.random();
        stdout("assemble bean user", user);
        stdout("assemble bean article", article);

        Map<String, Object> result1 = Assembler.on().withBean(user).result();
        stdout("assemble single bean user, result:", result1);
        assertEquals(user.id, result1.get("id"));
        assertEquals(user.name, result1.get("name"));

        Map<String, Object> result2 = Assembler.on().withBean(article).result();
        stdout("assemble single bean article, result:", result2);
        assertEquals(article.getId(), result2.get("id"));
        assertEquals(article.getName(), result2.get("name"));
    }

    @Test
    public void t12AssemblerTogetherTest() {
        User user = User.random();
        Article article = Article.random();
        stdout("assemble bean user", user);
        stdout("assemble bean article", article);

        Map<String, Object> result = Assembler.on()
                .withBean(user, new NameMapper.Prefix("user"))
                .withBean(article, new NameMapper.PrefixCamel("article"))
                .put("test_int", 1)
                .put("test_obj", User.random())
                .result();
        stdout("assemble single bean with prefix, result", result);
        assertEquals(user.id, result.get("userid"));
        assertEquals(user.name, result.get("username"));
        assertEquals(article.getName(), result.get("articleName"));
        assertEquals(article.getTime(), result.get("articleTime"));
    }

    @Test
    public void t21MapperAsModelTest() {
        User user = User.random();
        stdout("mapping as model, user", user);
        TargetUser targetUser = Mapper.map(user, TargetUser.class);
        assertNotNull(targetUser);
        assertEquals(user.id, targetUser.getId());
        assertEquals(user.name, targetUser.getName());
        assertEquals(user.castle, targetUser.castle);
        stdout("mapping as model, user result:", targetUser);

        Article article = Article.random();
        stdout("mapping as model, article", article);
        TargetArticle targetArticle = Mapper.map(article, TargetArticle.class);
        assertNotNull(targetArticle);
        assertEquals(article.getId(), targetArticle.id);
        assertEquals(article.getTime(), targetArticle.time);
        stdout("mapping as model, article result:", targetArticle);
    }

    @Test
    public void t22MapperAsMapTest() {
        User user = User.random();
        stdout("mapping as map, user", user);
        Map<String, Object> targetUser = Mapper.asMap(user, "name", "age");
        assertNotNull(targetUser);
        assertEquals(user.age, targetUser.get("age"));
        assertEquals(user.name, targetUser.get("name"));
        stdout("mapping as map, user result:", targetUser);

        Article article = Article.random();
        stdout("mapping as map, article", article);
        Map<String, Object> targetArticle = Mapper.asMap(
                article, new NameMapper.PrefixCamel("post"), "id", "name");
        assertNotNull(targetArticle);
        assertEquals(article.getId(), targetArticle.get("postId"));
        assertEquals(article.getName(), targetArticle.get("postName"));
        stdout("mapping as map, article result:", targetArticle);
    }

    @Test
    public void t23MapperAnnotationTest() {
        User user = User.random();
        stdout("mapping annotation, user", user);
        RenamedUser renamedUser = Mapper.map(user, RenamedUser.class);
        stdout("mapping annotation, result:", renamedUser);
        assertNotNull(renamedUser);
        assertEquals(user.id, renamedUser.renamedId);
        assertEquals(user.name, renamedUser.userName);
        assertEquals(user.age, renamedUser.howOld);
    }

    @Test
    public void t24MapperToListTest() {
        int minSize = 3;
        int size = minSize + DataGenerator.random.nextInt(6);
        List<User> sourceList = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            sourceList.add(User.random());
        }
        List<TargetUser> targetList = Mapper.mapList(sourceList, TargetUser.class);
        assertNotEmpty(targetList);

        User sourceUser = sourceList.get(minSize - 1);
        TargetUser targetUser = targetList.get(minSize - 1);
        assertEquals(sourceUser.id, targetUser.id);
        assertEquals(sourceUser.name, targetUser.name);
        stdout("mapping to list, result:", targetList);
    }

    @Test
    public void t31AssemblerGrainAnnotationTest() {
        User user = User.random();
        stdout("assembler grain test, user:", user);

        {
            Map<String, Object> result = Assembler.on().withBean(user, KbGrain.UNSPECIFIED).result();
            stdout("assembler grain test, grain unspecified:", result);
            assertEquals(user.id, result.get("id"));
            assertEquals(user.name, result.get("name"));
            assertEquals(user.age, result.get("age"));
            assertEquals(user.signature, result.get("signature"));
            assertEquals(user.castle, result.get("castle"));
        }

        {
            Map<String, Object> result = Assembler.on().withBean(user, KbGrain.ALL).result();
            stdout("assembler grain test, grain all:", result);
            assertEquals(user.id, result.get("id"));
            assertEquals(user.name, result.get("name"));
            assertEquals(user.age, result.get("age"));
            assertEquals(user.signature, result.get("signature"));
            assertEquals(user.castle, result.get("castle"));
        }

        {
            Map<String, Object> result = Assembler.on().withBean(user, KbGrain.BASIC).result();
            stdout("assembler grain test, grain basic:", result);
            assertEquals(user.id, result.get("id"));
            assertEquals(user.name, result.get("name"));
            assertNull(result.get("age"));
            assertNull(result.get("signature"));
            assertNull(result.get("castle"));
        }

        {
            Map<String, Object> result = Assembler.on().withBean(user, KbGrain.SUMMARY).result();
            stdout("assembler grain test, grain summary:", result);
            assertEquals(user.id, result.get("id"));
            assertEquals(user.name, result.get("name"));
            assertEquals(user.age, result.get("age"));
            assertEquals(user.signature, result.get("signature"));
            assertNull(result.get("castle"));
        }

        {
            int randomGrain = KbGrain.BASIC + DataGenerator.random.nextInt(KbGrain.SUMMARY - KbGrain.BASIC);
            Map<String, Object> result = Assembler.on().withBean(user, randomGrain).result();
            stdout("assembler grain test, grain:", randomGrain, result);
            assertEquals(user.id, result.get("id"));
            assertEquals(user.name, result.get("name"));
            assertNull(result.get("age"));
            assertNull(result.get("signature"));
            assertNull(result.get("castle"));
        }
    }

    @Test
    public void t32AssemblerGrainAnnotationWithListTest() {
        User user = User.random();
        user.populateArticles();
        stdout("assembler grain test, user:", user);
        {
            Assembler.on().asList(user.articles, KbGrain.BASIC).forEach(a -> {
                Map<String, Object> article = (Map<String, Object>) a;
                assertNotNull(article.get("id"));
                assertNotNull(article.get("time"));
                assertNull(article.get("name"));
            });
        }

        {
            Map<String, Object> result = Assembler.on().withBean(user, KbGrain.DETAIL).result();
            assertNotNull(result.get("id"));
            assertNotNull(result.get("name"));
            assertNotNull(result.get("age"));
            assertNotNull(result.get("signature"));
            assertNotNull(result.get("castle"));
            assertNotNull(result.get("articles"));

            List<Map<String, Object>> articles = (List<Map<String, Object>>) result.get("articles");
            articles.forEach(a -> {
                assertNotNull(a.get("id"));
                assertNotNull(a.get("time"));
                assertNull(a.get("name"));
            });
        }
    }


    public static class TargetUser {

        private Integer id;
        private String name;
        public String castle;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return "TargetUser{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    ", castle='" + castle + '\'' +
                    '}';
        }
    }


    public static class TargetArticle {
        public String id;
        public Long time;

        @Override
        public String toString() {
            return "TargetArticle{" +
                    "id='" + id + '\'' +
                    ", time=" + time +
                    '}';
        }
    }


    public static class RenamedUser {
        @KbMapping("id")
        public Integer renamedId;
        @KbMapping("name")
        public String userName;
        @KbMapping("age")
        public Integer howOld;

        @Override
        public String toString() {
            return "RenamedUser{" +
                    "renamedId=" + renamedId +
                    ", userName='" + userName + '\'' +
                    ", howOld=" + howOld +
                    '}';
        }
    }

}
