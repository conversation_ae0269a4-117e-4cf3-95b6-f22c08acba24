package com.kaiba.lib.base.assembler.models;

import com.kaiba.lib.base.assembler.KbGrain;
import com.kaiba.lib.base.test.util.DataGenerator;

/**
 * author: lyux
 * date: 18-7-27
 */
public class Article {

    @KbGrain(KbGrain.BASIC)
    private String id;
    @KbGrain(KbGrain.SUMMARY)
    private String name;
    private Long time;

    public static Article random() {
        Article model = new Article();
        model.id = DataGenerator.randomString(16);
        model.name = DataGenerator.random(DataGenerator.sCastleList);
        model.time = System.currentTimeMillis();
        return model;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    @Override
    public String toString() {
        return "Article{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", time=" + time +
                '}';
    }
}
