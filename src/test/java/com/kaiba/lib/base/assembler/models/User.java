package com.kaiba.lib.base.assembler.models;

import com.kaiba.lib.base.assembler.KbGrain;
import com.kaiba.lib.base.test.util.DataGenerator;

import java.util.Arrays;
import java.util.List;

/**
 * author: lyux
 * date: 18-7-27
 */
public class User {

    public Integer id;
    @KbGrain(KbGrain.BASIC)
    public String name;
    @KbGrain(KbGrain.SUMMARY)
    public Integer age;
    @KbGrain(KbGrain.SUMMARY)
    public String signature;
    @KbGrain(KbGrain.DETAIL)
    public String castle;

    @KbGrain(grain = KbGrain.DETAIL, subEnabled = true, subGrain = KbGrain.BASIC)
    public List<Article> articles;

    public static User random() {
        User model = new User();
        model.id = DataGenerator.random.nextInt(1000);
        model.age = DataGenerator.random.nextInt(100);
        model.name = DataGenerator.random(DataGenerator.sNameList);
        model.signature = DataGenerator.random(DataGenerator.sMottoList);
        model.castle = DataGenerator.random(DataGenerator.sCastleList);
        return model;
    }

    public void populateArticles() {
        articles = Arrays.asList(Article.random(), Article.random(), Article.random());
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", age=" + age +
                ", signature='" + signature + '\'' +
                ", castle='" + castle + '\'' +
                ", articles=" + articles +
                '}';
    }
}
