package com.kaiba.lib.base.rte;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * author: lyux
 * date: 2020-11-06
 */
@RunWith(JUnit4.class)
public class RelativeTimeExpressionTest {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Test
    public void testOperatorBegin() {
        // year
        assertExpression(
                "^y",
                "2020-12-01 09:05:31",
                "2020-01-01 00:00:00"
        );
        // month
        assertExpression(
                "^M",
                "2020-12-11 00:05:31",
                "2020-12-01 00:00:00"
        );
        // month - boundary condition
        assertExpression(
                "^M",
                "2020-10-01 00:00:00",
                "2020-10-01 00:00:00"
        );
        // week
        assertExpression(
                "^w",
                "2020-12-03 11:11:11",
                "2020-11-30 00:00:00"
        );
        // day
        assertExpression(
                "^d",
                "2020-12-03 11:11:11",
                "2020-12-03 00:00:00"
        );
        // hour
        assertExpression(
                "^H",
                "2020-12-03 11:11:11",
                "2020-12-03 11:00:00"
        );
        // minute
        assertExpression(
                "^m",
                "2020-12-03 11:11:11",
                "2020-12-03 11:11:00"
        );
        // second
        assertExpression(
                "^s",
                "2020-12-03 11:11:11",
                "2020-12-03 11:11:11"
        );
    }

    @Test
    public void testOperatorEnd() {
        // year
        assertExpression(
                "$y",
                "2020-11-15 09:05:31",
                "2020-12-31 23:59:59"
        );
        // month
        assertExpression(
                "$M",
                "2020-02-01 09:05:31",
                "2020-02-29 23:59:59"
        );
        // month - boundary condition
        assertExpression(
                "$M",
                "2020-12-31 23:59:59",
                "2020-12-31 23:59:59"
        );
        // week
        assertExpression(
                "$w",
                "2020-06-08 11:33:33",
                "2020-06-14 23:59:59"
        );
        // week - year's eve
        assertExpression(
                "$w",
                "2020-12-31 11:33:33",
                "2021-01-03 23:59:59"
        );
        // day
        assertExpression(
                "$d",
                "2020-12-03 11:11:11",
                "2020-12-03 23:59:59"
        );
        // hour
        assertExpression(
                "$H",
                "2020-12-03 11:11:11",
                "2020-12-03 11:59:59"
        );
        // minute
        assertExpression(
                "$m",
                "2020-12-03 11:11:11",
                "2020-12-03 11:11:59"
        );
        // second
        assertExpression(
                "$s",
                "2020-12-03 11:11:11",
                "2020-12-03 11:11:11"
        );
    }

    @Test
    public void testOperatorForward() {
        // year
        assertExpression(
                "+y",
                "2020-11-15 09:05:31",
                "2021-11-15 09:05:31"
        );
        // year - leap year
        assertExpression(
                "+y",
                "2020-02-29 09:05:31",
                "2021-02-28 09:05:31"
        );
        // month
        assertExpression(
                "+M",
                "2020-11-15 09:05:31",
                "2020-12-15 09:05:31"
        );
        // month - with less days
        assertExpression(
                "+4M",
                "2020-05-31 09:05:31",
                "2020-09-30 09:05:31"
        );
        // month - year's eve
        assertExpression(
                "+2M",
                "2020-11-30 09:05:31",
                "2021-01-30 09:05:31"
        );
        // week
        assertExpression(
                "+3w",
                "2020-12-17 09:05:31",
                "2021-01-07 09:05:31"
        );
        // day
        assertExpression(
                "+50d",
                "2020-12-03 09:05:31",
                "2021-01-22 09:05:31"
        );
        // hour
        assertExpression(
                "+H",
                "2020-12-03 09:05:31",
                "2020-12-03 10:05:31"
        );
        // minute
        assertExpression(
                "+120m",
                "2020-12-03 22:05:31",
                "2020-12-04 00:05:31"
        );
        // second
        assertExpression(
                "+7s",
                "2020-12-03 22:05:31",
                "2020-12-03 22:05:38"
        );
    }

    @Test
    public void testOperatorBackward() {
        // year
        assertExpression(
                "-y",
                "2020-11-15 09:05:31",
                "2019-11-15 09:05:31"
        );
        // month
        assertExpression(
                "-3M",
                "2020-11-15 09:05:31",
                "2020-08-15 09:05:31"
        );
        // month - with less days
        assertExpression(
                "-M",
                "2020-03-31 09:05:31",
                "2020-02-29 09:05:31"
        );
        // week
        assertExpression(
                "-2w",
                "2020-12-17 09:05:31",
                "2020-12-03 09:05:31"
        );
        // day
        assertExpression(
                "-d",
                "2020-12-03 09:05:31",
                "2020-12-02 09:05:31"
        );
        // hour
        assertExpression(
                "-24H",
                "2020-12-03 09:05:31",
                "2020-12-02 09:05:31"
        );
        // minute
        assertExpression(
                "-10m",
                "2020-12-03 22:05:31",
                "2020-12-03 21:55:31"
        );
        // second
        assertExpression(
                "-7s",
                "2020-12-03 22:05:05",
                "2020-12-03 22:04:58"
        );
    }

    @Test
    public void testOperatorRandom() {
        for (int i = 0; i < 50; i ++) {
            assertExpressionRange("~2M",
                    "2020-12-03 09:05:31",
                    "2020-12-03 09:05:31",
                    "2021-02-03 09:05:31"
                    );
            assertExpressionRange("~12H",
                    "2020-12-03 09:05:31",
                    "2020-12-03 09:05:31",
                    "2020-12-03 21:05:31"
                    );
        }
    }

    @Test
    public void testUnitNoon() {
        assertExpression(
                "+n",
                "2024-11-15 15:33:39",
                "2024-11-16 12:00:00"
        );
        assertExpression(
                "-n",
                "2024-11-15 15:33:39",
                "2024-11-15 12:00:00"
        );
        assertExpression(
                "+n",
                "2024-11-15 09:33:39",
                "2024-11-15 12:00:00"
        );
        assertExpression(
                "-n",
                "2024-11-15 09:33:39",
                "2024-11-14 12:00:00"
        );
        assertExpression(
                "+3n",
                "2024-11-15 09:33:39",
                "2024-11-17 12:00:00"
        );
        assertExpression(
                "-5n",
                "2024-11-15 09:33:39",
                "2024-11-10 12:00:00"
        );
    }

    @Test
    public void testCombine() {
        // 第二天加一小时
        assertExpression(
                "$d+1H",
                "2024-11-15 15:33:39",
                "2024-11-16 00:59:59"
        );
        // 兼容大小 H
        assertExpression(
                "$d+1h",
                "2024-11-15 15:33:39",
                "2024-11-16 00:59:59"
        );
        // 下月月初
        assertExpression(
                "+M^M",
                "2020-12-03 09:05:31",
                "2021-01-01 00:00:00"
        );
        // 上月第一个周日的中午
        assertExpression(
                "-M$w^d+12H",
                "2020-12-03 09:05:31",
                "2020-11-08 12:00:00"
        );
        // 次日凌晨
        assertExpression(
                "+d^d",
                "2020-11-15 09:05:31",
                "2020-11-16 00:00:00"
        );
        // 上月15日9点半
        assertExpression(
                "-M^M+14d+9H+30m",
                "2020-11-15 14:05:31",
                "2020-10-15 09:30:00"
        );
    }

    @Test
    public void testAbsolute() {
        // 绝对时间-秒
        assertExpression(
                "=1742023825s",
                "2020-11-15 14:05:31",
                "2025-03-15 15:30:25"
        );
        // 绝对时间-分
        assertExpression(
                "=29033730m",
                "2020-11-15 14:05:31",
                "2025-03-15 15:30:00"
        );
        // 绝对时间-时
        assertExpression(
                "=483895H",
                "2020-11-15 14:05:31",
                "2025-03-15 15:00:00"
        );
        // 绝对时间-天
        assertExpression(
                "=366d",
                "2020-12-15 14:35:36",
                "1971-01-02 08:00:00"
        );
        // 绝对时间-日期
        assertExpression(
                "=20150315D",
                "2020-12-15 14:35:36",
                "2015-03-15 00:00:00"
        );
        // 绝对时间-日期, + 偏移
        assertExpression(
                "=20150315D+15H+20m+45s",
                "2020-12-15 14:35:36",
                "2015-03-15 15:20:45"
        );
        // 绝对时间-日期, + 偏移, 前置操作符无效
        assertExpression(
                "+18d=20150315D+15H+20m+45s",
                "2020-12-15 14:35:36",
                "2015-03-15 15:20:45"
        );
    }

    @Test
    public void misc() {
    }

    private static void testExpression(
            String expression, String originalTime) {
        LocalDateTime ot = LocalDateTime.from(formatter.parse(originalTime));
        LocalDateTime rt = RelativeTimeExpression.calculate(ot, expression);
        String resultTime = formatter.format(rt);
        System.out.println("relative expression result not equal expected:");
        System.out.println("originalTime:   " + originalTime);
        System.out.println("resultTime:     " + resultTime);
        System.out.println("expression:     " + expression);
    }

    private static void assertExpression(
            String expression, String originalTime, String expectedTime) {
        LocalDateTime ot = LocalDateTime.from(formatter.parse(originalTime));
        LocalDateTime et = LocalDateTime.from(formatter.parse(expectedTime));
        LocalDateTime rt = RelativeTimeExpression.calculate(ot, expression);
        if (!rt.equals(et)) {
            String resultTime = formatter.format(rt);
            System.out.println("relative expression result not equal expected:");
            System.out.println("originalTime:   " + originalTime);
            System.out.println("expectedTime:   " + expectedTime);
            System.out.println("resultTime:     " + resultTime);
            System.out.println("expression:     " + expression);
            throw new RuntimeException("relative expression result not equal expected: "
                    + originalTime + " -> " + expression + " -> " + expectedTime + ", actual: " + resultTime);
        }
    }

    private static void assertExpressionRange(
            String expression, String originalTime, String expectedTimeBegin, String expectedTimeEnd) {
        LocalDateTime ot = LocalDateTime.from(formatter.parse(originalTime));
        LocalDateTime etb = LocalDateTime.from(formatter.parse(expectedTimeBegin));
        LocalDateTime ete = LocalDateTime.from(formatter.parse(expectedTimeEnd));
        LocalDateTime rt = RelativeTimeExpression.calculate(ot, expression);
        if (rt.compareTo(etb) < 0 || rt.compareTo(ete) > 0) {
            String resultTime = formatter.format(rt);
            System.out.println("relative expression result not equal expected:");
            System.out.println("originalTime:       " + originalTime);
            System.out.println("expectedTimeBegin:  " + expectedTimeBegin);
            System.out.println("expectedTimeEnd:    " + expectedTimeEnd);
            System.out.println("resultTime:         " + resultTime);
            System.out.println("expression:         " + expression);
            throw new RuntimeException("relative expression result not equal expected: "
                    + originalTime + " -> " + expression + " -> [" + expectedTimeBegin + "," + expectedTimeEnd + "], actual: " + resultTime);
        }
    }

}
