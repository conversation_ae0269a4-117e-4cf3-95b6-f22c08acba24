package com.kaiba.lib.base.appversion;

import com.kaiba.lib.base.util.AppVersion;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

/**
 * author: lyux
 * date: 2022-06-15
 */
@RunWith(JUnit4.class)
public class AppVersionTest {

    @Test
    public void name2code() {
        AppVersion v1 = new AppVersion("6.58.1");
        AppVersion v2 = new AppVersion("6.58.3");
        AppVersion v3 = new AppVersion("6.58.3");
        AppVersion v4 = new AppVersion("6.6.40");
        AppVersion v5 = new AppVersion("6.58.1.1");
        AppVersion v6 = new AppVersion("6.0.0");
        System.out.println("name2code:");
        System.out.println("v1: " + v1);
        System.out.println("v2: " + v2);
        System.out.println("v3: " + v3);
        System.out.println("v4: " + v4);
        System.out.println("v5: " + v5);
        System.out.println("v6: " + v6);
        assert v1.getVersionCode() == 65801;
        assert v2.getVersionCode() == 65803;
        assert v4.getVersionCode() == 60640;
        assert v5.getVersionCode() == 6580101;
        assert v1.compareTo(v2) < 0;
        assert v2.compareTo(v3) == 0;
        assert v1.compareTo(v4) > 0;
        assert v2.compareTo(v5) > 0;
    }

    @Test
    public void code2name() {
        AppVersion v1 = new AppVersion(30201);
        AppVersion v2 = new AppVersion(33522);
        AppVersion v3 = new AppVersion(30522);
        AppVersion v4 = new AppVersion("3.35.22");
        AppVersion v5 = new AppVersion(30000);
        AppVersion v6 = new AppVersion(6580101);
        System.out.println("code2name:");
        System.out.println("v1: " + v1);
        System.out.println("v2: " + v2);
        System.out.println("v3: " + v3);
        System.out.println("v5: " + v5);
        System.out.println("v6: " + v6);
        assert v1.getVersionName().equals("3.2.1");
        assert v2.getVersionName().equals("3.35.22");
        assert v3.getVersionName().equals("3.5.22");
        assert v2.compareTo(v4) == 0;
    }

    @Test(expected = Exception.class)
    public void failTest1() {
        new AppVersion("6.123.4");
    }

    @Test(expected = Exception.class)
    public void failTest2() {
        new AppVersion("0.12.4");
    }

    @Test(expected = Exception.class)
    public void failTest3() {
        new AppVersion("0124");
    }

    @Test(expected = Exception.class)
    public void failTest4() {
        new AppVersion(123456789);
    }

    @Test(expected = Exception.class)
    public void failTest5() {
        new AppVersion(1234);
    }

}
