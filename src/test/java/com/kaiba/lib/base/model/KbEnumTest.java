package com.kaiba.lib.base.model;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.util.appaction.AppActionType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.util.HashSet;
import java.util.Set;

/**
 * author: lyux
 * date: 2024-02-20
 */
@RunWith(JUnit4.class)
public class KbEnumTest {

    @Test
    public void testKbCodeUnique() {
        Set<Integer> codes = new HashSet<>(KbCode.values().length);
        for (KbCode c : KbCode.values()) {
            assert !codes.contains(c.getCode());
            codes.add(c.getCode());
        }
    }

    @Test
    public void testAppActionTypeCodeUnique() {
        Set<Integer> codes = new HashSet<>(AppActionType.values().length);
        for (AppActionType a : AppActionType.values()) {
            assert !codes.contains(a.getCode());
            codes.add(a.getCode());
        }
    }

    @Test
    public void testKbModuleCodeUnique() {
        Set<Integer> codes = new HashSet<>(KbModule.values().length);
        for (KbModule c : KbModule.values()) {
            assert !codes.contains(c.getValue());
            codes.add(c.getValue());
        }
    }

}
