package com.kaiba.lib.base.test.util;

import org.junit.Assert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;

/**
 * author: lyux
 * date: 18-7-19
 */
public abstract class AbstractTestCase extends Assert {

    public static Logger logger = LoggerFactory.getLogger("kaiba.test");

    public static String string(Object ...msg) {
        StringBuilder sb = new StringBuilder();
        for (Object obj : msg) {
            sb.append(obj == null ? null : obj.toString()).append(" ");
        }
        return sb.toString();
    }

    public static void stdout(Object ...msg) {
        System.out.println(string(msg));
    }

    public static void stdoutWithTrace(Object... msg) {
        if (msg.length != 0) {
            System.out.println(AbstractTestCase.string(msg));
        }
        new Exception("print trace").printStackTrace(System.out);
    }

    public static void log(Object ...msg) {
        logger.info(string(msg));
    }

    // -----------------------------------------------------------
    // self defined assertion

    public static void assertNotZero(int value) {
        assertNotEquals(value, 0);
    }

    public static void assertNotZero(long value) {
        assertNotEquals(value, 0);
    }

    public static void assertNotNegative(int value) {
        assertTrue(value >= 0);
    }

    public static void assertNotNegative(long value) {
        assertTrue(value >= 0);
    }

    public static void assertPositive(int value) {
        assertTrue(value > 0);
    }

    public static void assertPositive(long value) {
        assertTrue(value > 0);
    }

    public static void assertNotEmpty(String value) {
        assertTrue(value != null && "".equals(value.trim()));
    }

    public static void assertNotEmpty(Object[] value) {
        assertTrue(value != null && value.length != 0);
    }

    public static void assertNotEmpty(Collection<?> value) {
        assertTrue(value != null && !value.isEmpty());
    }

}
