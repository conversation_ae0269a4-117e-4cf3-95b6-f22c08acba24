package com.kaiba.lib.base.test.util;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * author: lyux
 * date: 18-7-22
 */
public class DataGenerator {

    private static final String STR_SOURCE = "abcdefghijklmnopqrstuvwxyz";

    public static final Random random = new Random();

    public static <T> T random(List<T> list) {
        return list.get(random.nextInt(list.size()));
    }

    public static String randomString(int length) {
        StringBuilder sb = new StringBuilder();
        int len = STR_SOURCE.length();
        for (int i = 0; i < length; i++) {
            int randomPosition = (int) Math.round(Math.random() * (len - 1));
            sb.append(STR_SOURCE.charAt(randomPosition));
        }
        return sb.toString();
    }

    // -----------------------------------------

    public static final List<String> sHouseList = Arrays.asList(
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON>"
    );

    public static final List<String> sNameList = Arrays.asList(
            "<PERSON><PERSON>d <PERSON>",
            "<PERSON> <PERSON>",
            "<PERSON>b <PERSON>",
            "<PERSON> <PERSON>",
            "<PERSON><PERSON> <PERSON>",
            "<PERSON>rya <PERSON>",
            "<PERSON><PERSON> <PERSON>",
            "<PERSON><PERSON> <PERSON>nister",
            "<PERSON>rion <PERSON>nister",
            "<PERSON> <PERSON>nister",
            "<PERSON>rsei Lanister",
            "<PERSON>ener<PERSON> <PERSON>rgaryen",
            "<PERSON> <PERSON>ath<PERSON>",
            "<PERSON><PERSON> <PERSON>ath<PERSON>",
            "<PERSON><PERSON> <PERSON><PERSON><PERSON>",
            "<PERSON>n <PERSON>joy",
            "Samwell Tarly",
            "Petyr Bealish",
            "Varys",
            "Melisandre",
            "Hodor"
    );

    public static final List<String> sCastleList = Arrays.asList(
            "Winterfell",
            "King's Landing",
            "Casterly Rock",
            "Eyrie",
            "Dragon Stone",
            "River Run",
            "Twins",
            "White Harbor",
            "Lannisport",
            "High Tower",
            "Storm's End",
            "Sun Spear",
            "Harrenhal",
            "Highgarden",
            "Hightower",
            "Pyke",
            "The Wall"
    );

    public static final List<String> sMottoList = Arrays.asList(
            "Winter is coming",
            "Blood and Fire",
            "Ours is the Fury",
            "Hear our roar",
            "Unbowed, Unbent, Unbroken.",
            "As High as Honor",
            "Family, Duty, Honor.",
            "Growing Strong",
            "We do not sow"
    );

    public static final List<String> sExcerptList = Arrays.asList(
            "Bran thought about it. 'Can a man still be brave if he's afraid?' 'That is the only time a man can be brave', his father told him. (Bran)",
            "A ruler who hides behind paid executioners soon forgets what death is. (Ned)",
            "'Rise,' Ned commanded the villagers. He never trusted what a man told him from his knees. (Ned)",
            "Lord Stannis in particular. His claim is the true one, he is known for his prowess as a battle commander, and he is utterly without mercy. There is no creature on earth half so terrifying as a truly just man. (Ned)",
            "'The vanguard?' he repeated dubiously. Either his lord father had a new respect for Tyrion's abilities, or he's decided to rid himself of his embarrassing get for good. Tyrion had the gloomy feeling he knew which. (Tyrion)",
            "If I look back I am lost. (Dany)",
            "The night is dark and full of terrors.",
            "Courage and folly are cousins, or so I've heard. Whatever curse may linger over the Tower of the Hand, I Pray I'm small enough to escape its notice. (Tyrion)",
            "'Now that he's king, he believes he should do as he pleases, not as he's bid.' 'Crowns do queer things to the heads beneath them,' Tyrion agreed. (Tyrion)",
            "Tyrion lingered after his cousin had slipped away. At the warrior's altar, he used one candle to light another. Watch over my brother, you bloody bastard, he's one of yours. He lit a second candle to the Stranger, for himself. (Tyrion)",
            "The Lord of Casterly Rock made such an impressive figure that it was a shock when his destrier dropped a load of dung right at the base of the throne. Joffrey had to step gingerly around it as he descended to embrace his grandfather and proclaim him Savior of the City. (Sansa)",
            "The night is dark and full of terrors. (Melisandre)",
            "Your know nothing, Jon Snow. (Jon)"
    );
}
