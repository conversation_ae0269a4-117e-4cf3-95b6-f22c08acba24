package com.kaiba.lib.base.test.util;

import org.hamcrest.Matcher;
import org.junit.Assert;
import org.junit.internal.ArrayComparisonFailure;

import java.util.Collection;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * author: lyux
 * date: 18-7-19
 */
public class AssertHandler {

    private Object lastTarget;

    public AssertHandler() {}

    public static AssertHandler on() {
        return new AssertHandler();
    }

    // -----------------------------------------------------------

    private AssertHandler stdout(Object... msg) {
        AbstractTestCase.stdout(msg);
        return this;
    }

    public AssertHandler clear() {
        lastTarget = null;
        return this;
    }

    public AssertHandler stdoutLastTarget(String msg) {
        return stdout(msg, lastTarget);
    }

    public AssertHandler stdoutLastTarget() {
        return stdout(lastTarget);
    }

    @SuppressWarnings("unchecked")
    public <T> AssertHandler doWithLastTarget(Consumer<T> consumer) {
        consumer.accept((T) lastTarget);
        return this;
    }

    @SuppressWarnings("unchecked")
    public <T, R> AssertHandler mapLastTarget(Function<? super T, ? extends R> mapper) {
        lastTarget = mapper.apply((T) lastTarget);
        return this;
    }

    @SuppressWarnings("unchecked")
    public <T> T getLastTarget() {
        return (T) lastTarget;
    }

    @SuppressWarnings("unchecked")
    public <T> T getLastTarget(Class<T> resultClass) {
        return (T) lastTarget;
    }

    public AssertHandler setLastTarget(Object lastTarget) {
        this.lastTarget = lastTarget;
        return this;
    }

    // -----------------------------------------------------------
    // self defined

    public AssertHandler assertNotZero(int value) {
        AbstractTestCase.assertNotZero(value);
        lastTarget = value;
        return this;
    }

    public AssertHandler assertNotZero(long value) {
        AbstractTestCase.assertNotZero(value);
        lastTarget = value;
        return this;
    }

    public AssertHandler assertNotNegative(int value) {
        AbstractTestCase.assertNotNegative(value);
        lastTarget = value;
        return this;
    }

    public AssertHandler assertNotNegative(long value) {
        AbstractTestCase.assertNotNegative(value);
        lastTarget = value;
        return this;
    }

    public AssertHandler assertPositive(int value) {
        AbstractTestCase.assertPositive(value);
        lastTarget = value;
        return this;
    }

    public AssertHandler assertPositive(long value) {
        AbstractTestCase.assertPositive(value);
        lastTarget = value;
        return this;
    }

    public AssertHandler assertNotEmpty(String value) {
        AbstractTestCase.assertNotEmpty(value);
        lastTarget = value;
        return this;
    }

    public AssertHandler assertNotEmpty(Object[] value) {
        AbstractTestCase.assertNotEmpty(value);
        lastTarget = value;
        return this;
    }

    public AssertHandler assertNotEmpty(Collection<?> value) {
        AbstractTestCase.assertNotEmpty(value);
        lastTarget = value;
        return this;
    }

    // -----------------------------------------------------------

    public AssertHandler assertTrue(String message, boolean condition) {
        Assert.assertTrue(message, condition);
        lastTarget = condition;
        return this;
    }

    public AssertHandler assertTrue(boolean condition) {
        Assert.assertTrue(condition);
        lastTarget = condition;
        return this;
    }

    public AssertHandler assertFalse(String message, boolean condition) {
        Assert.assertFalse(message, condition);
        lastTarget = condition;
        return this;
    }

    public AssertHandler assertFalse(boolean condition) {
        Assert.assertFalse(condition);
        lastTarget = condition;
        return this;
    }

    public AssertHandler assertEquals(String message, Object expected, Object actual) {
        Assert.assertEquals(message, expected, actual);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertEquals(Object expected, Object actual) {
        Assert.assertEquals(expected, actual);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertNotEquals(String message, Object unexpected, Object actual) {
        Assert.assertNotEquals(message, unexpected, actual);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertNotEquals(Object unexpected, Object actual) {
        Assert.assertNotEquals(unexpected, actual);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertNotEquals(String message, long unexpected, long actual) {
        Assert.assertNotEquals(message, unexpected, actual);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertNotEquals(long unexpected, long actual) {
        Assert.assertNotEquals(unexpected, actual);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertNotEquals(String message, double unexpected, double actual, double delta) {
        Assert.assertNotEquals(message, unexpected, actual, delta);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertNotEquals(double unexpected, double actual, double delta) {
        Assert.assertNotEquals(unexpected, actual, delta);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertNotEquals(float unexpected, float actual, float delta) {
        Assert.assertNotEquals(unexpected, actual, delta);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertArrayEquals(String message, Object[] expecteds, Object[] actuals) throws ArrayComparisonFailure {
        Assert.assertArrayEquals(message, expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(Object[] expecteds, Object[] actuals) {
        Assert.assertArrayEquals(expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(String message, boolean[] expecteds, boolean[] actuals) throws ArrayComparisonFailure {
        Assert.assertArrayEquals(message, expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(boolean[] expecteds, boolean[] actuals) {
        Assert.assertArrayEquals(expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(String message, byte[] expecteds, byte[] actuals) throws ArrayComparisonFailure {
        Assert.assertArrayEquals(message, expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(byte[] expecteds, byte[] actuals) {
        Assert.assertArrayEquals(expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(String message, char[] expecteds, char[] actuals) throws ArrayComparisonFailure {
        Assert.assertArrayEquals(message, expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(char[] expecteds, char[] actuals) {
        Assert.assertArrayEquals(expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(String message, short[] expecteds, short[] actuals) throws ArrayComparisonFailure {
        Assert.assertArrayEquals(message, expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(short[] expecteds, short[] actuals) {
        Assert.assertArrayEquals(expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(String message, int[] expecteds, int[] actuals) throws ArrayComparisonFailure {
        Assert.assertArrayEquals(message, expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(int[] expecteds, int[] actuals) {
        Assert.assertArrayEquals(expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(String message, long[] expecteds, long[] actuals) throws ArrayComparisonFailure {
        Assert.assertArrayEquals(message, expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(long[] expecteds, long[] actuals) {
        Assert.assertArrayEquals(expecteds, actuals);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(String message, double[] expecteds, double[] actuals, double delta) throws ArrayComparisonFailure {
        Assert.assertArrayEquals(message, expecteds, actuals, delta);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(double[] expecteds, double[] actuals, double delta) {
        Assert.assertArrayEquals(expecteds, actuals, delta);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(String message, float[] expecteds, float[] actuals, float delta) throws ArrayComparisonFailure {
        Assert.assertArrayEquals(message, expecteds, actuals, delta);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertArrayEquals(float[] expecteds, float[] actuals, float delta) {
        Assert.assertArrayEquals(expecteds, actuals, delta);
        lastTarget = expecteds;
        return this;
    }

    public AssertHandler assertEquals(String message, double expected, double actual, double delta) {
        Assert.assertEquals(message, expected, actual, delta);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertEquals(String message, float expected, float actual, float delta) {
        Assert.assertEquals(message, expected, actual, delta);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertNotEquals(String message, float unexpected, float actual, float delta) {
        Assert.assertNotEquals(message, unexpected, actual, delta);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertEquals(long expected, long actual) {
        Assert.assertEquals(expected, actual);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertEquals(String message, long expected, long actual) {
        Assert.assertEquals(message, expected, actual);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertEquals(double expected, double actual, double delta) {
        Assert.assertEquals(expected, actual, delta);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertEquals(float expected, float actual, float delta) {
        Assert.assertEquals(expected, actual, delta);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertNotNull(String message, Object object) {
        Assert.assertNotNull(message, object);
        lastTarget = object;
        return this;
    }

    public AssertHandler assertNotNull(Object object) {
        Assert.assertNotNull(object);
        lastTarget = object;
        return this;
    }

    public AssertHandler assertNull(String message, Object object) {
        Assert.assertNull(message, object);
        lastTarget = object;
        return this;
    }

    public AssertHandler assertNull(Object object) {
        Assert.assertNull(object);
        lastTarget = object;
        return this;
    }

    public AssertHandler assertSame(String message, Object expected, Object actual) {
        Assert.assertSame(message, expected, actual);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertSame(Object expected, Object actual) {
        Assert.assertSame(expected, actual);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertNotSame(String message, Object unexpected, Object actual) {
        Assert.assertNotSame(message, unexpected, actual);
        lastTarget = actual;
        return this;
    }

    public AssertHandler assertNotSame(Object unexpected, Object actual) {
        Assert.assertNotSame(unexpected, actual);
        lastTarget = actual;
        return this;
    }

    public <T> AssertHandler assertThat(T actual, Matcher<? super T> matcher) {
        Assert.assertThat(actual, matcher);
        lastTarget = actual;
        return this;
    }

    public <T> AssertHandler assertThat(String reason, T actual, Matcher<? super T> matcher) {
        Assert.assertThat(reason, actual, matcher);
        lastTarget = actual;
        return this;
    }

    // -----------------------------------------------------------

}
