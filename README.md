# kaiba lib base

开吧 Spring Web Framework 基础项目. 主要用于 数据类型, 常量, 接口, 注解 等通用属性定义.

> 注意:
> 此项目不适用于 webflux. 若 webflux 项目引入本项目为基础会引起很多奇怪的问题.

## 版本号规则

该项目的版本号需 **严格遵循** 如下规则:

1. 如果对项目所做修改使得类库无法向下兼容, 则要抬升主版本号. 如: 1.0.0 升级为 2.0.0 .
2. 如果项目中有新增数据类型或接口, 并不影响向下兼容的情况下, 则要抬升次版本号. 如: 1.0.0 升级为 1.1.0 .
3. 如果对项目的修改仅限于内部逻辑, 没有增减数据类型定义及接口定义, 则抬升末尾版本号. 如: 1.0.0 升级为 1.0.1 .

> 注意:
> 如果不对版本号进行修改, 则云效自动构建会失败. maven 制品库不允许同版本号的 jar 包互相覆盖.

## git commit 规则

因为本项目为公共依赖项目, 因此要严格控制 git commit 内容. 务必详细描写所做修改. 格式如下:

```
title: 填写变更概要.
<空行>
新增定义: 填写新增的 数据类型, 接口定义, 常量定义.
修改定义: 填写对已有 数据类型, 接口定义, 常量定义 的修改.
删除定义: 填写对已有 数据类型, 接口定义, 常量定义 的删除.
逻辑变动: 填写重要的代码逻辑变动. 可不填.
```

## 本地环境搭建

本项目中含有一个 docker-compose 配置, 可用于一键启动本地环境, 以便大家进行本地断点调试. 具体启动命令如下:

```bash
cd project_tools/docker_composer_for_local
docker-compose up -d
```

本地环境启动后, 请前往 `http://localhost:8848/nacos/#/configurationManagement` 增加配置中心配置. 具体配置信息请参考 dev 环境配置. 下面为当前 (2020-04-08) local 环境配置示例:

```properties

kb.circle.main_page.search_hint=加油

kb.accounting.poundage.in=0.006
kb.accounting.poundage.out=0
kb.accounting.user.withdraw.limit.message=余额满10元方可提现
kb.accounting.user.withdraw.limit=10
kb.accounting.issue.service.charge=0

kb.issue.duration.init=604800
kb.issue.duration.waiting=3600
kb.issue.duration.peering.dispatch=3600
kb.issue.duration.peering.specify=3600
kb.issue.duration.served=86400

kb.issue.task.allow_reassign.dispatch_timeout=true
kb.issue.task.allow_reassign.dispatch_refused=false

kb.issue.task.duration.specify=600
kb.issue.task.duration.dispatch=30
kb.issue.task.retry_delay.dispatch=180
kb.issue.task.retry_delay.specify=180
kb.issue.task.expert.trim.rong_overall_threshold=129600
kb.issue.task.expert.trim.rong_offline_threshold=3600
kb.issue.task.expert.trim.task_timeout_threshold=10

kb.issue.task.score.accepted_task_count=1
kb.issue.task.score.current_refused_count=-5
kb.issue.task.score.current_timeout_count=-25
kb.issue.task.score.refused_task_count=-1
kb.issue.task.score.sealed_answer_count=10
kb.issue.task.score.timeout_task_count=-5
kb.issue.task.score.praise_count=1
kb.issue.task.score.follower_count=2
kb.issue.task.score.speed_ratio=10
kb.issue.task.score.quality_ratio=10
kb.issue.task.score.satisfaction_ratio=10
kb.issue.task.score.car_brand=1000
kb.issue.task.score.level=0.1
kb.issue.task.score.rank=0.3
kb.issue.task.score.vip=100000
kb.issue.task.score.already_timeout=-600
kb.issue.task.score.already_refused=-1000
kb.issue.task.vip.active_threshold=900

#############################################

kb.mongo.uri=**********************************************************

kb.redis.host=127.0.0.1
kb.redis.port=6379
kb.redis.password=Kaiba315Redis

kb.rabbitmq.host=localhost
kb.rabbitmq.port=5672
kb.rabbitmq.username=kaiba
kb.rabbitmq.password=kaiba_rabbitmq

#############################################

spring.redis.host=${kb.redis.host}
spring.redis.port=${kb.redis.port}
spring.redis.password=${kb.redis.password}

spring.rabbitmq.host=${kb.rabbitmq.host}
spring.rabbitmq.port=${kb.rabbitmq.port}
spring.rabbitmq.username=${kb.rabbitmq.username}
spring.rabbitmq.password=${kb.rabbitmq.password}
```

## 参考

[nacos official site](<https://nacos.io/>)

[nacos spring cloud config](<https://github.com/alibaba/spring-cloud-alibaba/blob/master/spring-cloud-alibaba-examples/nacos-example/nacos-config-example/readme-zh.md>)

[阿里云微服务注册中心 MSE 文档](<https://help.aliyun.com/document_detail/126761.html?spm=a2c4g.11186623.4.2.35ef2e56CP7YL9>)

[阿里云微服务配置中心 ACM 文档](<https://help.aliyun.com/document_detail/59953.html?spm=a2c4g.11186623.6.542.4d9743ffsD9Lfu>)

[阿里云 serverless 文档](<https://help.aliyun.com/document_detail/120994.html?spm=5176.12834076.0.0.6ef66a68jicg9b>)
